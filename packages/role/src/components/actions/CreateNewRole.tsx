/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Button, PlusIcon, RightTray } from 'ui-style';
import { useToggle } from 'ui-common';
import { useCallback, useRef } from 'react';
import { fetch, userUrls, schemaUrls, entityUrls } from '@tripudiotech/admin-api';
import { useRoles } from '@tripudiotech/admin-caching-store';
import <PERSON><PERSON>rom from '../RoleForm';

const CreateNewRoleActions = () => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);
    const { getRoles } = useRoles();

    const handleSubmit = useCallback(async (values) => {
        try {
            await fetch({
                ...userUrls.createRole,
                data: values,
                successMessage: (
                    <span>
                        Role
                        <b> {values.name} </b>
                        has been created successfully
                    </span>
                ),
            });
            await getRoles(true);
            openToggle.close();
        } catch (err) {
            return false;
        }
    }, []);
    return (
        <>
            <Button variant="contained" color="secondary" onClick={openToggle.open} size="small" endIcon={<PlusIcon />}>
                Create New Role
            </Button>
            <RightTray
                title={`Create new role`}
                componentName={'create-new-user'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={() => formRef.current.submitForm()}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <RoleFrom formRef={formRef} handleSubmit={handleSubmit} />
                </Box>
            </RightTray>
        </>
    );
};

export default CreateNewRoleActions;
