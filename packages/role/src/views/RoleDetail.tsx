/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useState } from 'react';
import { Box, Button, RightTray, LoadingOverlay, SchemaFieldSection, Grid, SchemaField } from 'ui-style';
import { useToggle } from 'ui-common';
import { useDialog, useRoles } from '@tripudiotech/admin-caching-store';
import { useMatch, useNavigate } from 'react-router-dom';
import get from 'lodash/get';
import { find } from 'lodash';
import { fetch, userUrls } from '@tripudiotech/admin-api';

const RoleDetail = () => {
    const [isEditing, isEditingToggle] = useToggle(false);
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const { roles, getRoles } = useRoles();

    const navigate = useNavigate();
    const matched = useMatch('role/:id');
    const roleId = get(matched, 'params.id');
    const role = find(roles, { id: roleId });

    useEffect(() => {
        const loadRoles = async () => {
            await getRoles();
        };
        loadRoles();
    }, []);

    const handleClose = useCallback(async () => {
        navigate(`/role`);
    }, []);

    const handleDelete = useCallback(async () => {
        try {
            await fetch({
                ...userUrls.deleteRole,
                params: { id: roleId },
                successMessage: (
                    <span>
                        Role
                        <b> {role.name} </b>
                        has been deleted successfully
                    </span>
                ),
            });
            await getRoles(true);
            handleClose();
        } catch (error) {
            console.error(error);
        }
    }, []);

    const onDelete = () => {
        onOpenDialog('Delete Role', `Do you really want to delete the role <b>${role.name}</b>`, handleDelete, 'error');
    };
    return (
        <RightTray
            title={`${get(role, 'name', ' ')}`}
            componentName={'user-detail'}
            open={true}
            onClose={handleClose}
            disableCloseOnClickOutside={isEditing}
            hideConfirm
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                    p: '16px',
                    gap: '16px',
                    overflow: 'auto',
                }}
            >
                <SchemaFieldSection>General Information</SchemaFieldSection>
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <SchemaField label="Name" value={role?.name || '-'} />
                        <SchemaField label={'Description'} value={role?.description || '-'} breakline={true} />
                    </Grid>
                </Grid>
            </Box>
            <Box
                sx={{
                    mt: 'auto',
                    display: 'flex',
                    gap: '8px',
                    justifyContent: 'flex-end',
                    margin: '16px 24px',
                    alignSelf: 'flex-end',
                }}
            >
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={() => (isEditing ? isEditingToggle.close() : handleClose())}
                >
                    Cancel
                </Button>
                <span>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                        }}
                        variant="outlined"
                        onClick={onDelete}
                        color="error"
                    >
                        Delete
                    </Button>
                </span>
            </Box>
        </RightTray>
    );
};

export default RoleDetail;
