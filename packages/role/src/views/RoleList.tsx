/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import {
    <PERSON>,
    Button,
    ContentHeader,
    FixedHeightContainer,
    styled,
    AnimatedPage,
    tableIcons,
    tableStyles,
    Loading,
    Typography,
} from 'ui-style';
import { EventType } from 'ui-common';
import { useRoles } from '@tripudiotech/admin-caching-store';
import { AgGridReact } from '@ag-grid-community/react';
import { Outlet, useNavigate } from 'react-router-dom';
import CreateNewRoleActions from '../components/actions/CreateNewRole';

const RoleActions = () => {
    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <CreateNewRoleActions />
        </Box>
    );
};

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    width: '100%',
}));

const RoleList = () => {
    const { roles, getRoles } = useRoles();

    const gridRef = useRef<AgGridReact>(null);

    useEffect(() => {
        const loadRoles = async () => {
            await getRoles();
        };
        loadRoles();
    }, []);

    const navigate = useNavigate();
    const onRowClicked = (e) => {
        const id = e.data.id;
        navigate(`/role/${id}`);
    };
    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Role Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                },
            ],
            icons: tableIcons,
            pagination: true,
            onRowClicked,
            rowSelection: 'single',
        };
    }, []);
    return (
        <AnimatedPage>
            <FixedHeightContainer>
                <ContentHeader title="Role" actionsRenderer={RoleActions} />
                <ContentWrapper sx={{ ...tableStyles, '& .ag-paging-panel': { display: 'none' } }}>
                    <div className="ag-theme-alpine" style={{ width: '100%' }}>
                        <AgGridReact ref={gridRef} className="ag-theme-alpine" {...gridOptions} rowData={roles} />
                    </div>
                    <div>
                        <Outlet />
                    </div>
                </ContentWrapper>
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default RoleList;
