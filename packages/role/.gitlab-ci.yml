.role-variables:
  variables:
    PACKAGE_DIR: packages/role

aws-stag-role-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .role-variables

aws-stag-role-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .role-variables
  needs:
    - aws-stag-role-package

gcp-stag-role-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .role-variables

gcp-stag-role-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .role-variables
  needs:
    - gcp-stag-role-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-role-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .role-variables

gcp-uat-role-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .role-variables
  needs:
    - gcp-uat-role-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json