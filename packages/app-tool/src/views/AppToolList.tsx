/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import {
    Box,
    Button,
    ContentHeader,
    FixedHeightContainer,
    styled,
    AnimatedPage,
    tableIcons,
    tableStyles,
    Loading,
    Chip,
    AGGridTablePagination,
    AccountValueRenderer,
} from 'ui-style';
import { buildSortParams, formatDateTime, DEFAULT_TABLE_PAGINATION_SIZE, buildQueryBasedOnFilter } from 'ui-common';
import { userUrls, fetch, getAvatarUrl } from '@tripudiotech/admin-api';
import { AgGridReact } from '@ag-grid-community/react';
import { Outlet, useNavigate, useOutletContext, useSearchParams } from 'react-router-dom';
import CreateNewAppToolActions from '../components/actions/CreateNewAppTool';
import get from 'lodash/get';

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    width: '100%',
}));

const AppToolList = () => {
    const gridRef = useRef<AgGridReact>(null);
    const navigate = useNavigate();
    const [appTools, setAppTools] = useState(null);

    const onRowClicked = (e) => {
        const name = e.data.name;
        navigate(`/app-tool/${name}`);
    };
    const loadAppTools = async () => {
        try {
            const {
                data: { data: data },
            } = await fetch({
                ...userUrls.getAppTools,
            });
            setAppTools(data);
        } catch (error) {
            console.error(error);
        }
    };
    useEffect(() => {
        loadAppTools();
    }, []);
    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'enabled',
                    headerName: 'Status',
                    cellRenderer: function (params) {
                        return (
                            <Chip
                                size="small"
                                label={params.value ? 'ACTIVE' : 'INACTIVE'}
                                variant={params.value ? 'status-success' : 'status-error'}
                            />
                        );
                    },
                },
                {
                    field: 'createdAt',
                    headerName: 'Created At',
                    filter: 'agTextColumnFilter',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'createdAt'));
                    },
                },
                {
                    field: 'createdBy',
                    headerName: 'Created By',
                    filter: 'agTextColumnFilter',
                    cellRenderer: AccountValueRenderer,
                    cellRendererParams: {
                        getAvatarUrl: getAvatarUrl,
                    },
                },
            ],
            icons: tableIcons,
            pagination: true,
            onRowClicked,
            rowSelection: 'single',
        };
    }, [gridRef]);

    const AppToolActions = () => {
        return (
            <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
                <CreateNewAppToolActions onRefresh={loadAppTools} appTools={appTools} />
            </Box>
        );
    };

    return (
        <AnimatedPage>
            <FixedHeightContainer>
                <ContentHeader title="App Tool" actionsRenderer={AppToolActions} />
                <ContentWrapper sx={{ ...tableStyles, '& .ag-paging-panel': { display: 'none' } }}>
                    <div className="ag-theme-alpine" style={{ width: '100%' }}>
                        <AgGridReact ref={gridRef} {...gridOptions} rowData={appTools} />
                    </div>
                    <div>
                        <Outlet context={{ onRefresh: loadAppTools }} />
                    </div>
                </ContentWrapper>
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default AppToolList;

type ContextType = { onRefresh: () => void };

export const useOnRefresh = () => {
    return useOutletContext<ContextType>();
};
