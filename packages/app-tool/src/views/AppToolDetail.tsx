/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useState } from 'react';
import { Box, Button, RightTray, LoadingOverlay, SchemaFieldSection, Grid, SchemaField } from 'ui-style';
import { useToggle } from 'ui-common';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { useMatch, useNavigate } from 'react-router-dom';
import get from 'lodash/get';
import { fetch, userUrls } from '@tripudiotech/admin-api';
import { useOnRefresh } from './AppToolList';

const AppToolDetail = () => {
    const [isEditing, isEditingToggle] = useToggle(false);
    const onOpenDialog = useDialog((state) => state.onOpenDialog);

    const navigate = useNavigate();
    const matched = useMatch('app-tool/:name');
    const appName = get(matched, 'params.name');
    const [appTool, setAppTool] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const { onRefresh } = useOnRefresh();

    useEffect(() => {
        const loadAppTool = async () => {
            try {
                setIsLoading(true);
                const { data } = await fetch({
                    ...userUrls.getAppTool,
                    params: {
                        name: appName,
                    },
                });
                setAppTool(data);
            } catch (error) {
                console.error(error);
            } finally {
                setIsLoading(false);
            }
        };
        loadAppTool();
    }, [appName]);

    const handleClose = useCallback(async () => {
        navigate(`/app-tool`);
    }, []);

    const handleDelete = useCallback(async () => {
        try {
            setIsLoading(true);
            await fetch({
                ...userUrls.deleteAppTool,
                params: { name: appName },
                successMessage: (
                    <span>
                        App Tool
                        <b> {appTool.name} </b>
                        has been deleted successfully
                    </span>
                ),
            });
            onRefresh();
            setIsLoading(false);
            handleClose();
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    }, [appTool]);

    const onDelete = () => {
        onOpenDialog(
            'Delete App Tool',
            `Do you really want to delete the app tool <b>${appTool.name}</b>`,
            handleDelete,
            'error'
        );
    };
    return (
        <RightTray
            title={`${get(appTool, 'name', ' ')}`}
            componentName={'app-tool-detail'}
            open={true}
            onClose={handleClose}
            disableCloseOnClickOutside={isEditing}
            hideConfirm
        >
            {isLoading && <LoadingOverlay />}
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                    p: '16px',
                    gap: '16px',
                    overflow: 'auto',
                }}
            >
                <SchemaFieldSection>General Information</SchemaFieldSection>
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <SchemaField label="Name" value={appTool?.name || '-'} />
                        <SchemaField label="Logo" value={appTool?.logo || '-'} />
                        <SchemaField label={'Description'} value={appTool?.description || '-'} breakline={true} />
                    </Grid>
                </Grid>
            </Box>
            <Box
                sx={{
                    mt: 'auto',
                    display: 'flex',
                    gap: '8px',
                    justifyContent: 'flex-end',
                    margin: '16px 24px',
                    alignSelf: 'flex-end',
                }}
            >
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={() => (isEditing ? isEditingToggle.close() : handleClose())}
                >
                    Cancel
                </Button>
                <span>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                        }}
                        variant="outlined"
                        onClick={onDelete}
                        color="error"
                    >
                        Delete
                    </Button>
                </span>
            </Box>
        </RightTray>
    );
};

export default AppToolDetail;
