/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Button, PlusIcon, RightTray } from 'ui-style';
import { useToggle } from 'ui-common';
import { useCallback, useRef } from 'react';
import { fetch, userUrls } from '@tripudiotech/admin-api';
import AppToolForm from '../AppToolForm';

const CreateNewAppToolActions = ({ onRefresh, appTools }) => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);

    const handleSubmit = useCallback(async (values, interfaces) => {
        try {
            await fetch({
                ...userUrls.createAppTool,
                data: { ...values, interfaceWiths: interfaces.map((i) => i.name) },
                successMessage: (
                    <span>
                        App Tool
                        <b> {values.name} </b>
                        has been created successfully
                    </span>
                ),
            });
            onRefresh();
            openToggle.close();
        } catch (err) {
            return false;
        }
    }, []);

    return (
        <>
            <Button variant="contained" color="secondary" onClick={openToggle.open} size="small" endIcon={<PlusIcon />}>
                Create New App Tool
            </Button>
            <RightTray
                title={`Create New App Tool`}
                componentName={'create-new-app-tool'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={() => formRef.current.submitForm()}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <AppToolForm formRef={formRef} handleSubmit={handleSubmit} appTools={appTools} />
                </Box>
            </RightTray>
        </>
    );
};

export default CreateNewAppToolActions;
