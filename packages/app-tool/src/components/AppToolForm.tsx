/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { styled, Typography, FormikTextField, RichTextField, Autocomplete, TextField } from 'ui-style';

import { Formik, Field } from 'formik';
import { useMemo, useState } from 'react';
import * as yup from 'yup';

const FormContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 'auto',
    '& .generalInfo': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '12px',
    },
    '& .sectionLabel': {
        color: theme.palette.info.main,
    },
}));

const AppToolForm = ({ formRef, handleSubmit, appTools }) => {
    const [interfaces, setInterfaces] = useState([]);
    const initialValues = useMemo(() => {
        return {
            name: '',
            description: '',
        };
    }, []);

    const validationSchema = useMemo(() => {
        const validateRule = {
            name: yup.string().required('Name is required'),
        };
        return yup.object().shape(validateRule);
    }, []);


    const onInterfaceSelection = (event, newValue) => {
        setInterfaces(newValue);
    };

    return (
        <FormContainer>
            <Formik
                enableReinitialize
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting }) => {
                    handleSubmit(values, interfaces);
                    setSubmitting(false);
                }}
                innerRef={formRef}
            >
                <div className="generalInfo">
                    <Typography variant="label2-med" className="sectionLabel">
                        General Information
                    </Typography>
                    <Field
                        fullWidth
                        component={FormikTextField}
                        label="Name"
                        helperText="App Tool Name"
                        name="name"
                        variant="outlined"
                        InputLabelProps={{ shrink: true }}
                        required
                    />
                    <Field
                        fullWidth
                        component={FormikTextField}
                        label="Logo"
                        helperText="App Tool Logo"
                        name="logo"
                        variant="outlined"
                        InputLabelProps={{ shrink: true }}
                    />
                    <Field
                        fullWidth
                        component={RichTextField}
                        rows={4}
                        label="Description"
                        helperText="Description"
                        name="description"
                        variant="outlined"
                        InputLabelProps={{ shrink: true }}
                    />
                    <Autocomplete
                        options={appTools}
                        multiple
                        getOptionLabel={(option: any) => option.name}
                        isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
                        id=""
                        value={interfaces}
                        onChange={onInterfaceSelection}
                        renderInput={(params) => <TextField {...params} variant="outlined" label="Interface With" size="small" />}
                    />
                </div>
            </Formik>
        </FormContainer>
    );
};

export default AppToolForm;
