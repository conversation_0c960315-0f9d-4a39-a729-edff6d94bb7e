const { mergeWithRules } = require('webpack-merge');
const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const webpack = require('webpack');
const dotenv = require('dotenv');
dotenv.config();

module.exports = (webpackConfigEnv, argv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: 'tripudiotech',
        projectName: 'app-tool',
        webpackConfigEnv,
        argv,
    });
    const sourceMap = process.env.SOURCE_MAP || false;
    return mergeWithRules({
        module: {
            rules: {
                test: 'match',
                use: 'replace',
            },
        },
    })(defaultConfig, {
        plugins: [
            new webpack.DefinePlugin({
                'process.env': JSON.stringify(process.env),
            }),
        ],
        module: {
            rules: [
                {
                    test: /\.(scss|sass)$/,
                    use: [
                        {
                            loader: 'style-loader',
                        },
                        {
                            loader: 'css-loader',
                        },
                        {
                            loader: 'sass-loader',
                        },
                    ],
                },
                {
                    test: /.m?js/,
                    resolve: {
                        fullySpecified: false,
                    },
                },
            ],
        },
        devtool: sourceMap,
    });
};
