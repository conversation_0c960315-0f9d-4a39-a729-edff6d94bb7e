.app-tool-variables:
  variables:
    PACKAGE_DIR: packages/app-tool

aws-stag-app-tool-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .app-tool-variables

aws-stag-app-tool-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .app-tool-variables
  needs:
    - aws-stag-app-tool-package

gcp-stag-app-tool-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .app-tool-variables

gcp-stag-app-tool-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .app-tool-variables
  needs:
    - gcp-stag-app-tool-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-app-tool-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .app-tool-variables

gcp-uat-app-tool-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .app-tool-variables
  needs:
    - gcp-uat-app-tool-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json