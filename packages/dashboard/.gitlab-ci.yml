.dashboard-variables:
  variables:
    PACKAGE_DIR: packages/dashboard

aws-stag-dashboard-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .dashboard-variables

aws-stag-dashboard-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .dashboard-variables
  needs:
    - aws-stag-dashboard-package

gcp-stag-dashboard-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .dashboard-variables

gcp-stag-dashboard-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .dashboard-variables
  needs:
    - gcp-stag-dashboard-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-dashboard-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .dashboard-variables

gcp-uat-dashboard-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .dashboard-variables
  needs:
    - gcp-uat-dashboard-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json