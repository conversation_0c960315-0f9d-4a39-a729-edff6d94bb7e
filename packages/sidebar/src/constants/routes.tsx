/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    IntegrationIcon,
    SchemaIcon,
    ClassificationIcon,
    LifecycleIcon,
    UserIcon,
    UserGroupIcon,
    RoleIcon,
    CompanyIcon,
    AppToolIcon,
    BusinessRuleIcon,
    SettingIcon,
    ProcessIcon,
    DigitalThreadIcon,
    DeleteIcon,
    AttributeIcon,
} from 'ui-style';

export const DEFAULT_ROUTES: any = [
    {
        id: 'function-management',
        label: 'Function Management',
        group: true,
        children: [
            {
                id: 'schema',
                icon: <SchemaIcon />,
                label: 'Class and Type',
                link: '/schema',
            },
            {
                id: 'digital-thread',
                icon: <DigitalThreadIcon />,
                label: 'Digital Thread',
                link: '/digital-thread',
            },
            {
                id: 'classification',
                icon: <ClassificationIcon />,
                label: 'Classification',
                link: '/classification',
            },
               {
                id: 'attribute',
                icon: <AttributeIcon />,
                label: 'Attribute',
                link: '/attribute',
            },
            {
                id: 'lifecycle',
                icon: <LifecycleIcon />,
                label: 'Lifecycle',
                link: '/lifecycle',
            },
            {
                id: 'process',
                icon: <ProcessIcon />,
                label: 'Process',
                link: '/process',
            },
            {
                id: 'business-rule',
                icon: <BusinessRuleIcon />,
                label: 'Business Rule',
                link: '/business-rule',
            },
            {
                id: 'recycle-bin',
                icon: <DeleteIcon style={{ width: '24px', height: '24px' }} />,
                label: 'Recycle Bin',
                link: '/recycle-bin',
            },
        ],
    },
    {
        id: 'org-management',
        label: 'Organization Management',
        group: true,
        children: [
            {
                id: 'user',
                icon: <UserIcon />,
                label: 'User',
                link: '/user',
            },
            {
                id: 'user-group-management',
                icon: <UserGroupIcon />,
                label: 'User Group',
                link: '/user-group-management',
            },
            {
                id: 'role',
                icon: <RoleIcon />,
                label: 'Role',
                link: '/role',
            },
            {
                id: 'company',
                icon: <CompanyIcon />,
                label: 'Company',
                link: '/company',
            },
        ],
    },
    {
        id: 'permission-management',
        label: 'Permission Management',
        group: true,
        children: [
            {
                id: 'permission-management-role',
                icon: <RoleIcon />,
                label: 'Role',
                link: '/permission-role',
            },
        ],
    },
    {
        id: 'system-configuration',
        label: 'System Configuration',
        group: true,
        children: [
            {
                id: 'app-tool',
                icon: <AppToolIcon />,
                label: 'App Tool',
                link: '/app-tool',
            },
            {
                id: 'global-setting',
                icon: <SettingIcon />,
                label: 'Global Setting',
                link: '/global-setting',
            },
            {
                id: 'integration',
                icon: <IntegrationIcon />,
                label: 'Integration',
                link: '/integration',
            },
        ],
    },
];
