/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { styled, StyledComponent } from 'ui-style';

const DESKTOP_WIDTH = 260;
const TABLET_WIDTH = 200;
const PREFIX = 'Sidebar';
export const classes = {
    menuButton: `${PREFIX}-menuButton`,
    drawer: `${PREFIX}-drawer`,
    hide: `${PREFIX}-hide`,
    drawerOpen: `${PREFIX}-drawerOpen`,
    drawerClose: `${PREFIX}-drawerClose`,
    toolbar: `${PREFIX}-toolbar`,
    content: `${PREFIX}-content`,
    mobileBackButton: `${PREFIX}-mobileBackButton`,
    hideSidebar: `${PREFIX}-hideSidebar`,
};
export const Root: StyledComponent<any> = styled('div')(({ theme, hideSidebar = false }: any) => ({
    [`& .${classes.drawer}`]: {
        width: DESKTOP_WIDTH,
        '& .MuiPaper-root': {
            borderRight: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
        },
        transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.easeInOut,
            duration: 200,
        }),
    },
    [`& .${classes.drawerOpen}`]: {
        width: DESKTOP_WIDTH,
        [theme.breakpoints.down('md')]: {
            width: TABLET_WIDTH,
        },
        backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
        overflowX: 'hidden',
    },
    [`& .${classes.drawerClose}`]: {
        overflowX: 'hidden',
        backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
        width: hideSidebar ? 0 : 57,
        [theme.breakpoints.down('sm')]: {
            width: 0,
        },
    },
    '& .actionButton': {
        width: '100%',
        minWidth: '100%',
        display: 'flex',
        justifyContent: 'center',
        '> span': { overflow: 'hidden', whiteSpace: 'nowrap' },
        '&.open': {
            justifyContent: 'space-between',
        },
    },
}));
