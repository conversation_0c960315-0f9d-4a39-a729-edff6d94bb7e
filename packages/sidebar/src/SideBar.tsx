/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
    Box,
    Drawer,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    styled,
    Avatar,
    Collapse,
    useTheme,
    useMediaQuery,
    MainTooltip,
    ExpandMoreIcon,
    AccountIcon,
    AdminIcon,
    LogoutIcon,
    Typography,
    Slide,
    ListItemProps,
} from 'ui-style';
import { authenticationService, IDENTITY_SERVICE_URL, getAvatarUrl } from '@tripudiotech/admin-api';
import classNames from 'classnames';
import { classes, Root } from './styles';

import { useSideBar, useAuth } from '@tripudiotech/admin-caching-store';
import { DEFAULT_ROUTES } from './constants/routes';
import { Link, useLocation } from 'react-router-dom';
import get from 'lodash/get';

type ListItemLinkProps = ListItemProps & {
    to?: string;
    component?: typeof Link;
};

function handleOpenAccountPage() {
    const tenant = authenticationService.getTenant();
    window.open(`${IDENTITY_SERVICE_URL}/auth/realms/${tenant}/account`, '_blank').focus();
}

function openUserSite() {
    const tenant = authenticationService.getTenant();
    if (tenant === 'master') {
        window.open([location.protocol, '//', location.host.replace('admin', 'ui')].join(''), '_blank').focus();
    } else {
        window.open([location.protocol, '//', location.host.replace('admin-', '')].join(''), '_blank').focus();
    }
}

const StyledListItem = styled(ListItem)<ListItemLinkProps>(({ theme }) => ({
    display: 'block',
    backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
    color: theme.palette.glide.text.normal.inverseSecondary,
    '& .sidebarButton': {
        padding: '12px 16px',
        '& .sidebarText': {
            marginTop: 0,
            marginBottom: 0,
            '& span': {
                fontWeight: 500,
                fontSize: '14px',
                lineHeight: '135%',
                letterSpacing: '-0.1px',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
            },
        },
        '& .sidebarItemIcon': {
            width: 40,
            minWidth: 40,
            color: theme.palette.glide.text.normal.inverseSecondary,
        },
        '&:hover': {
            backgroundColor: theme.palette.glide.background.hover.inverseSecondary,
            color: theme.palette.glide.text.normal.inverseSecondary,
            ' & .sidebarText span': {
                fontWeight: 700,
                letterSpacing: '0.2px',
            },
            '& .sidebarItemIcon svg': {
                fill: theme.palette.glide.text.normal.inverseSecondary,
            },
        },
        '&.active': {
            backgroundColor: theme.palette.glide.background.active.primary,
            color: theme.palette.glide.text.normal.tertiary,
            ' & .sidebarText span': {
                fontWeight: 700,
                letterSpacing: '0.2px',
            },
            '& .sidebarItemIcon svg': {
                fill: theme.palette.glide.text.normal.tertiary,
            },
            borderRight: `1px solid ${theme.palette.glide.stroke.normal.main}`,
        },
    },
}));

const SideBarLink = ({ link = '', icon = undefined, label = '', open = false, active = false }) => {
    return (
        //@ts
        <StyledListItem disablePadding component={Link} to={link}>
            <ListItemButton className={classNames('sidebarButton', { active: active })}>
                <ListItemIcon
                    className={classNames('sidebarItemIcon', {
                        open: open,
                    })}
                >
                    {icon}
                </ListItemIcon>
                <ListItemText
                    className="sidebarText"
                    primary={label}
                    sx={{ opacity: open ? 1 : 0, transition: 'ease-in-out 0.2s' }}
                />
            </ListItemButton>
        </StyledListItem>
    );
};

const Devider = styled(Box)(({ theme }) => ({
    width: '100%',
    height: '1px',
    backgroundColor: theme.palette.glide.stroke.normal.primary,
}));

const SideBar = ({ routes = DEFAULT_ROUTES, authentication }) => {
    const location = useLocation();
    // global
    const { open, setOpen } = useSideBar();
    const { userInfo, setUserInfo } = useAuth();
    const [accountOpened, setAccountOpened] = useState(false);
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    const ACTIONS = useMemo(() => {
        return [
            {
                icon: <AccountIcon />,
                label: 'Account',
                onClick: handleOpenAccountPage,
                requiredAdmin: false,
            },
            {
                icon: <AdminIcon />,
                label: 'User Site',
                onClick: openUserSite,
                requiredAdmin: true,
            },
            {
                icon: <LogoutIcon />,
                label: 'Log out',
                onClick: () => {
                    authentication.keycloak.logout({ logoutMethod: 'POST' }).then(() => {
                        window.sessionStorage.clear();
                    });
                },
                requiredAdmin: false,
            },
        ];
    }, [authentication]);

    useEffect(() => {
        if (!userInfo) {
            authenticationService.getUserInfo().then((userInformation) => {
                setUserInfo(userInformation);
            });
        }
    }, [userInfo]);

    const email = get(userInfo, 'email');
    const userName = get(userInfo, 'firstName', 'User');
    const isAdmin = get(userInfo, 'isAdmin', false) || get(userInfo, 'isSuperAdmin', false);

    const onAccountClick = () => {
        if (!open) {
            setOpen(true);
        }
        setAccountOpened((prev) => !prev);
    };

    useEffect(() => {
        if (!open) {
            setAccountOpened(false);
        }
    }, [open]);
    return (
        <Root>
            <Drawer
                variant={isMobile ? 'temporary' : 'permanent'}
                sx={
                    isMobile
                        ? {
                              '& .MuiPaper-root': {
                                  width: '250px',
                                  backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
                                  maxWidth: '50%',
                              },
                          }
                        : {}
                }
                className={classNames(classes.drawer, {
                    [classes.drawerOpen]: open,
                    [classes.drawerClose]: !open,
                })}
                classes={{
                    paper: classNames(classes.drawer, {
                        [classes.drawerOpen]: open,
                        [classes.drawerClose]: !open,
                    }),
                }}
                open={open}
                onClose={() => setOpen(false)}
            >
                <Devider sx={{ marginTop: '54px' }} />
                <List sx={{ pt: 0 }}>
                    {routes.map((props) => {
                        const isActive =
                            location.pathname.startsWith(`${props.link}/`) || location.pathname === props.link;
                        const isGroup = props.group;
                        return isGroup ? (
                            <Box
                                id={props.id}
                                key={props.id}
                                sx={{
                                    borderTop: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                }}
                            >
                                <Slide in={open} unmountOnExit direction="right">
                                    <Box sx={{ pl: '17.5px', marginTop: '12px' }}>
                                        <Typography
                                            variant="label3-med"
                                            sx={{
                                                color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                                            }}
                                        >
                                            {props.label}
                                        </Typography>
                                    </Box>
                                </Slide>
                                {props.children.map((childProps) => {
                                    const isActive =
                                        location.pathname.startsWith(`${childProps.link}/`) ||
                                        location.pathname === childProps.link;

                                    return (
                                        <MainTooltip
                                            key={`sidebar-link-${childProps.id}`}
                                            placement="right"
                                            title={open ? '' : childProps.label}
                                        >
                                            <span>
                                                <SideBarLink {...childProps} open={open} active={isActive} />
                                            </span>
                                        </MainTooltip>
                                    );
                                })}
                            </Box>
                        ) : (
                            <MainTooltip
                                key={`sidebar-link-${props.id}`}
                                placement="right"
                                title={open ? '' : props.label}
                            >
                                <span>
                                    <SideBarLink {...props} open={open} active={isActive} />
                                </span>
                            </MainTooltip>
                        );
                    })}
                </List>
                <List sx={{ marginTop: 'auto', display: 'flex', flexDirection: 'column', p: 0 }}>
                    <Devider />
                    <StyledListItem
                        disablePadding
                        sx={{
                            '& .sidebarButton': {
                                px: open ? '24px' : 0,
                                justifyContent: 'center',
                            },
                        }}
                    >
                        <ListItemButton onClick={onAccountClick} className="sidebarButton">
                            <Avatar alt={userName} src={email ? getAvatarUrl(email) : ''} />
                            {open && (
                                <>
                                    <ListItemText
                                        className="sidebarText"
                                        primary={userName}
                                        sx={{
                                            opacity: open ? 1 : 0,
                                            transition: 'ease-in-out 0.2s',
                                            ml: '16px',
                                            '& span': {
                                                fontWeight: 500,
                                                fontSize: '14px',
                                                color: (theme) => theme.palette.glide.text.normal.inverseSecondary,
                                            },
                                        }}
                                    />
                                    <ExpandMoreIcon sx={{ transform: accountOpened ? 'rotate(180deg)' : 'none' }} />
                                </>
                            )}
                        </ListItemButton>
                        <Collapse in={accountOpened} timeout="auto" unmountOnExit>
                            <List sx={{ ml: '0px' }}>
                                {ACTIONS.map(({ label, onClick, icon }) => (
                                    <ListItemButton onClick={onClick} className="sidebarButton" key={label}>
                                        <ListItemIcon className="sidebarItemIcon">{icon}</ListItemIcon>
                                        <ListItemText className="sidebarText" primary={label} />
                                    </ListItemButton>
                                ))}
                            </List>
                        </Collapse>
                    </StyledListItem>
                </List>
            </Drawer>
        </Root>
    );
};

export default SideBar;
