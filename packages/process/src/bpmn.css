.bjs-breadcrumbs {
  position: absolute;
  display: none;
  flex-wrap: wrap;
  align-items: center;
  top: 16px;
  left: 24px;
  padding: 0;
  padding-bottom: 4px;
  font-family: Work Sans;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
}

.bjs-breadcrumbs-shown .bjs-breadcrumbs {
  display: flex;
}

.djs-palette-shown .bjs-breadcrumbs {
  left: 90px;
}

.djs-palette-shown.djs-palette-two-column .bjs-breadcrumbs {
  left: 140px;
}

.bjs-breadcrumbs li {
  display: inline-flex;
  padding-bottom: 5px;
}

.bjs-breadcrumbs li a {
  cursor: pointer;
  color: #2F54EB;
}

.bjs-breadcrumbs li:last-of-type a {
  color: inherit;
  cursor: default;
}

.bjs-breadcrumbs li:not(:first-child)::before {
  content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" /><path d="M0 0h24v24H0z" fill="none" /></svg>');
  padding: 0 8px;
  color: #777777;
  height: 1em;
}

.bjs-breadcrumbs .bjs-crumb {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bjs-drilldown {
  width: 20px;
  height: 20px;

  padding: 0px;
  margin-left: -20px;

  cursor: pointer;
  border: none;
  border-radius: 2px;
  outline: none;

  fill: #FFFFFF;
  background-color: #2F54EB;
}

.bjs-drilldown-empty {
  display: none;
}

.selected .bjs-drilldown-empty {
  display: inherit;
}

[data-popup="align-elements"] .djs-popup-body {
  display: flex;
}

[data-popup="align-elements"] .djs-popup-body [data-group]+[data-group] {
  border-left: 1px solid #777777;
}

[data-popup="align-elements"] [data-group="align"] {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

[data-popup="align-elements"] .djs-popup-body .entry {
  padding: 6px 8px;
}

[data-popup="align-elements"] .djs-popup-body .entry img {
  display: block;

  height: 20px;
  width: 20px;
}

[data-popup="align-elements"] .bjs-align-elements-menu-entry {
  display: inline-block;
}