/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import 'react-toastify/dist/ReactToastify.css';
import { GlideThemeProvider } from 'ui-style';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

import { LicenseManager } from '@ag-grid-enterprise/core';
import '@ag-grid-community/core/dist/styles/ag-grid.css';
import '@ag-grid-community/core/dist/styles/ag-theme-alpine.css';
import { ModuleRegistry } from '@ag-grid-community/core';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ClipboardModule } from '@ag-grid-enterprise/clipboard';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';

import MainLayout from './layouts/MainLayout';
import ProcessList from './views/ProcessList';
import ProcessDetail from './views/ProcessDetail';

LicenseManager.setLicenseKey(process.env.AG_GRID_LICENSE);

ModuleRegistry.registerModules([
    RowGroupingModule,
    ExcelExportModule,
    SetFilterModule,
    ColumnsToolPanelModule,
    FiltersToolPanelModule,
    MenuModule,
    ClipboardModule,
    ServerSideRowModelModule,
]);

const Root = () => (
    <GlideThemeProvider>
        <BrowserRouter basename="admin">
            <Routes>
                <Route path="/process" element={<MainLayout />}>
                    <Route path="" element={<ProcessList />}>
                        <Route path=":id" element={<ProcessDetail />} />
                    </Route>
                </Route>
            </Routes>
        </BrowserRouter>
    </GlideThemeProvider>
);

export default Root;
