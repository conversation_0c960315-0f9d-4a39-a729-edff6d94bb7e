/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const ACTIVITI_PROPERTIES = {
    // Assignable
    ASSIGNEE: 'assignee',
    CANDIDATE_USERS: 'candidateUsers',
    CANDIDATE_GROUPS: 'candidateGroups',
    DUE_DATE: 'dueDate',
    FOLLOW_UP_DATE: 'followUpDate',
    PRIORITY: 'priority',

    // Multi instances
    LOOP_CARDINALITY: 'loopCardinality',
    COLLECTION: 'collection',
    ELEMENT_VARIABLE: 'elementVariable',
    COMPLETION_CONDITION: 'completionCondition',

    // Async Continuation
    ASYNC: 'async',
    EXCLUSIVE: 'exclusive',

    // Failed job retry
    CYCLE: 'cycle',

    // Call activity
    INHERIT_BUSINESS_KEY: 'inheritBusinessKey',
    CALLED_ELEMENT: 'calledElement',
};

export const ACTIVITI_LABELS = {
    ASSIGNEE: 'Assignee',
    CANDIDATE_USERS: 'Candidate Users',
    CANDIDATE_GROUPS: 'Candidate Groups',
    DUE_DATE: 'Due Date',
    FOLLOW_UP_DATE: 'Follow Up Date',
    PRIORITY: 'Priority',
    LOOP_CARDINALITY: 'Loop Cardinality',
    COLLECTION: 'Collection',
    ELEMENT_VARIABLE: 'Element Variable',
    COMPLETION_CONDITION: 'Completion Condition',
    ASYNC: 'Asynchronous',
    EXCLUSIVE: 'Exclusive',
    CYCLE: 'Retry Time Cycle',
    INHERIT_BUSINESS_KEY: 'Inherit business key',
    CALLED_ELEMENT: 'Called Element',
};

export const PRIORITY_OPTIONS = [
    {
        label: 'None',
        value: '0',
    },
    {
        label: 'Low',
        value: '1',
    },
    {
        label: 'Normal',
        value: '2',
    },
    {
        label: 'High',
        value: '3',
    },
];

export const CALL_ACTIVITY_DEFAULT_PROPS = {
    calledElement: undefined,
    'activiti:calledElementBinding': 'latest',
    'activiti:calledElementVersion': undefined,
    'activiti:calledElementTenantId': undefined,
    'activiti:variableMappingClass': undefined,
    'activiti:variableMappingDelegateExpression': undefined,
    'activiti:caseRef': undefined,
    'activiti:caseBinding': 'latest',
    'activiti:caseVersion': undefined,
    'activiti:caseTenantId': undefined,
};

export const DELEGATE_TYPES = ['class', 'expression', 'delegateExpression', 'implementation'];

export const SERVICE_TASK_PROPERTIES = {
    class: 'activiti:class',
    expression: 'activiti:expression',
    delegateExpression: 'activiti:delegateExpression',
    implementation: 'implementation',
};
