/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { is } from 'bpmn-js/lib/util/ModelUtil';
import UserEntry from '../../entries/UserEntry';
import ConnectionEntry from '../../entries/ConnectionEntry';

const PROPERTIES_PRIORITY = 500;

const CAMUNDA_GROUP = {
    USER_ASSIGNMENT: 'CamundaPlatform__UserAssignment',
    SERVICE_TASK_IMPLEMENTATION: 'CamundaPlatform__Implementation',
};

const overrideServiceTaskProperties = (groups: any[], element) => {
    return groups.map((group) => {
        if (group.id === CAMUNDA_GROUP.SERVICE_TASK_IMPLEMENTATION) {
            let newGroup = { ...group };
            newGroup.entries = group.entries.map((entry) => {
                // Override expression
                if (entry.id === 'expression') {
                    return {
                        id: entry.id,
                        label: 'Expression',
                        // description: "Select an assignee from the list of users",
                        element,
                        component: ConnectionEntry,
                        modelProperty: 'expression',
                    };
                }
                return entry;
            });
            return newGroup;
        }
        return group;
    });
};
const overrideUserTaskProperties = (groups: any[], element) => {
    return groups.map((group) => {
        if (group.id === CAMUNDA_GROUP.USER_ASSIGNMENT) {
            let newGroup = { ...group };
            newGroup.entries = group.entries.map((entry) => {
                // Override assignee
                if (entry.id === 'assignee') {
                    return {
                        id: entry.id,
                        label: 'Assignee',
                        description: 'Select an assignee from the list of users',
                        element,
                        component: UserEntry,
                        modelProperty: 'assignee',
                    };
                }
                return entry;
            });
            return newGroup;
        }

        return group;
    });
};

export default function CamundaCustomProvider(propertiesPanel, bpmnFactory, translate) {
    // API ////////

    /**
     * Return the groups provided for the given element.
     *
     * @param {DiagramElement} element
     *
     * @return {(Object[]) => (Object[])} groups middleware
     */
    this.getGroups = function (element) {
        /**
         * We return a middleware that modifies
         * the existing groups.
         *
         * @param {Object[]} groups
         *
         * @return {Object[]} modified groups
         */
        return function (groups) {
            // Details group
            let newGroups = [...groups];

            // Override groups of Camunda User Task
            if (is(element, 'bpmn:UserTask')) {
                newGroups = overrideUserTaskProperties(groups, element);
            }

            if (is(element, 'bpmn:ServiceTask')) {
                newGroups = overrideServiceTaskProperties(groups, element);
            }

            return newGroups;
        };
    };

    // registration ////////

    // Register our custom magic properties provider.
    // Use a lower priority to ensure it is loaded after
    // the basic BPMN properties.
    propertiesPanel.registerProvider(PROPERTIES_PRIORITY, this);
}

CamundaCustomProvider.$inject = ['propertiesPanel', 'bpmnFactory', 'translate'];
