/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import TextField from '../../../entries/TextField';
import { cmdHelper, serviceTaskHelper } from '../../../helper';

const delegate = (element, bpmnFactory, translate) => {
    function getDelegationLabel(type) {
        switch (type) {
            case 'class':
                return translate('Java Class');
            case 'expression':
                return translate('Expression');
            case 'delegateExpression':
                return translate('Delegate Expression');
            case 'implementation':
                return translate('Implementation');
            default:
                return '';
        }
    }

    const bo = serviceTaskHelper.getBusinessObject(element);
    const implementationType = serviceTaskHelper.getImplementationType(element);
    const attr = serviceTaskHelper.getAttribute(implementationType);
    const label = getDelegationLabel(implementationType);

    const delegateEntry = {
        id: 'delegate',
        label: translate(label),
        modelProperty: 'delegate',
        element,
        component: TextField,
        get: function (element) {
            return bo.get(attr);
        },
        set: function (newValue) {
            const prop = {};
            prop[attr] = newValue || '';
            return cmdHelper.updateBusinessObject(element, bo, prop);
        },
    };
    return delegateEntry;
};
export default function (group, element, bpmnFactory, translate) {
    if (!serviceTaskHelper.isServiceTaskLike(serviceTaskHelper.getBusinessObject(element))) {
        return;
    }

    group.entries.push(delegate(element, bpmnFactory, translate));
}
