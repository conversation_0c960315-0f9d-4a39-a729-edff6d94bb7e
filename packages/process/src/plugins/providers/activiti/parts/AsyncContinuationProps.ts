/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { asyncCapableHelper, cmdHelper, eventDefinitionHelper } from '../../../helper';
import assign from 'lodash/assign';
import { ACTIVITI_PROPERTIES, ACTIVITI_LABELS } from '../../../constants/activiti';
import CheckboxField from '../../../entries/CheckboxField';

function isActivitiAsync(bo) {
    return asyncCapableHelper.isActivitiAsync(bo);
}

function isExclusive(bo) {
    return asyncCapableHelper.isExclusive(bo);
}

function removeFailedJobRetryTimeCycle(bo, element) {
    return asyncCapableHelper.removeFailedJobRetryTimeCycle(bo, element);
}

function canRemoveFailedJobRetryTimeCycle(element) {
    return !eventDefinitionHelper.getTimerEventDefinition(element);
}

export default function (element, bpmnFactory, options, translate) {
    const getBusinessObject = options.getBusinessObject;
    const idPrefix = options.idPrefix || '';
    const labelPrefix = options.labelPrefix || '';

    const activitiAsyncEntry = {
        id: idPrefix + ACTIVITI_PROPERTIES.ASYNC,
        label: labelPrefix + translate(ACTIVITI_LABELS.ASYNC),
        modelProperty: ACTIVITI_PROPERTIES.ASYNC,
        element,
        component: CheckboxField,
        get: () => {
            const bo = getBusinessObject(element);
            return isActivitiAsync(bo);
        },
        setCommands: (newValue) => {
            const bo = getBusinessObject(element);
            const asyncValue = !!newValue;
            let props = {
                'activiti:async': asyncValue,
            };
            const commands: any = [];
            if (!asyncValue) {
                props = assign({ 'activiti:exclusive': true }, props);
                if (canRemoveFailedJobRetryTimeCycle(element)) {
                    commands.push(removeFailedJobRetryTimeCycle(bo, element));
                }
            }
            commands.push(cmdHelper.updateBusinessObject(element, bo, props));
            return commands;
        },
    };

    const exclusiveEntry = {
        id: idPrefix + ACTIVITI_PROPERTIES.EXCLUSIVE,
        label: labelPrefix + translate(ACTIVITI_LABELS.EXCLUSIVE),
        modelProperty: ACTIVITI_PROPERTIES.EXCLUSIVE,
        element,
        component: CheckboxField,
        get: () => {
            const bo = getBusinessObject(element);
            return isExclusive(bo);
        },
        setCommands: (newValue) => {
            const bo = getBusinessObject(element);
            return cmdHelper.updateBusinessObject(element, bo, {
                'activiti:exclusive': !!newValue,
            });
        },
        hidden: (element) => {
            const bo = getBusinessObject(element);
            return bo && !isActivitiAsync(bo);
        },
    };

    return [activitiAsyncEntry, exclusiveEntry];
}
