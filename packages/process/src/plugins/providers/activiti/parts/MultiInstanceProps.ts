/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { is, getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';
import TextField from '../../../entries/TextField.js';
import { ACTIVITI_PROPERTIES, ACTIVITI_LABELS } from '../../../constants/activiti';

import { cmd<PERSON>elper, elementHelper } from '../../../helper';

import asyncContinuationProps from './AsyncContinuationProps';

import jobRetryTimeCycleProps from './JobRetryTimeCycleProps';

const PREFIX_ID = 'multiInstance-';

export default function (group, element, bpmnFactory, translate) {
    if (!ensureMultiInstanceSupported(element)) {
        return;
    }
    // Loop Cardinality
    addLoopCardinalityCharacteristicsGroup(group, element, bpmnFactory, translate);

    // Async Continuation
    addAsyncContinuationGroup(group, element, bpmnFactory, translate);

    // Retry Time Cycle
    addRetryTimeCycleGroup(group, element, bpmnFactory, translate);
}

/**
 * Helpers
 */
function updateFormalExpression(element, propertyName, newValue, bpmnFactory) {
    let loopCharacteristics = getLoopCharacteristics(element);
    let expressionProps = {};

    if (!newValue) {
        // remove formal expression
        expressionProps[propertyName] = undefined;
        return cmdHelper.updateBusinessObject(element, loopCharacteristics, expressionProps);
    }

    let existingExpression = loopCharacteristics.get(propertyName);

    if (!existingExpression) {
        // add formal expression
        expressionProps[propertyName] = createFormalExpression(loopCharacteristics, newValue, bpmnFactory);
        return cmdHelper.updateBusinessObject(element, loopCharacteristics, expressionProps);
    }

    // edit existing formal expression
    return cmdHelper.updateBusinessObject(element, existingExpression, {
        body: newValue,
    });
}

function addRetryTimeCycleGroup(group, element, bpmnFactory, translate) {
    group.entries.concat(
        jobRetryTimeCycleProps(
            element,
            bpmnFactory,
            {
                getBusinessObject: getLoopCharacteristics,
                idPrefix: PREFIX_ID,
                labelPrefix: translate('Multi Instance '),
            },
            translate
        )
    );
}

function addAsyncContinuationGroup(group, element, bpmnFactory, translate) {
    if (is(element, 'activiti:AsyncCapable')) {
        group.entries.concat(
            asyncContinuationProps(
                element,
                bpmnFactory,
                {
                    getBusinessObject: getLoopCharacteristics,
                    idPrefix: PREFIX_ID,
                    labelPrefix: translate('Multi Instance '),
                },
                translate
            )
        );
    }
}

function addLoopCardinalityCharacteristicsGroup(group, element, bpmnFactory, translate) {
    const loopCharacteristics = getLoopCharacteristics(element);
    // Loop Cardinality
    group.entries.push({
        id: `${PREFIX_ID}${ACTIVITI_PROPERTIES.LOOP_CARDINALITY}`,
        label: translate(ACTIVITI_LABELS.LOOP_CARDINALITY),
        modelProperty: ACTIVITI_PROPERTIES.LOOP_CARDINALITY,
        element,
        component: TextField,
        get: () => {
            return getLoopCardinalityValue(element);
        },
        setCommands: (newValue) => {
            return updateFormalExpression(element, ACTIVITI_PROPERTIES.LOOP_CARDINALITY, newValue, bpmnFactory);
        },
    });
    // Collection
    group.entries.push({
        id: `${PREFIX_ID}${ACTIVITI_PROPERTIES.COLLECTION}`,
        label: translate(ACTIVITI_LABELS.COLLECTION),
        modelProperty: ACTIVITI_PROPERTIES.COLLECTION,
        element,
        component: TextField,
        get: () => {
            return getCollection(element);
        },
        setCommands: (newValue) => {
            return cmdHelper.updateBusinessObject(element, loopCharacteristics, {
                'activiti:collection': newValue || undefined,
            });
        },
    });
    // Element Variable
    group.entries.push({
        id: `${PREFIX_ID}${ACTIVITI_PROPERTIES.ELEMENT_VARIABLE}`,
        label: translate(ACTIVITI_LABELS.ELEMENT_VARIABLE),
        modelProperty: ACTIVITI_PROPERTIES.ELEMENT_VARIABLE,
        element,
        component: TextField,
        get: () => {
            return getElementVariable(element);
        },
        setCommands: (newValue) => {
            return cmdHelper.updateBusinessObject(element, loopCharacteristics, {
                'activiti:elementVariable': newValue || undefined,
            });
        },
    });
    // Completion Condition
    group.entries.push({
        id: `${PREFIX_ID}${ACTIVITI_PROPERTIES.COMPLETION_CONDITION}`,
        label: translate(ACTIVITI_LABELS.COMPLETION_CONDITION),
        modelProperty: ACTIVITI_PROPERTIES.COMPLETION_CONDITION,
        element,
        component: TextField,
        get: () => {
            return getCompletionConditionValue(element);
        },
        setCommands: (newValue) => {
            return updateFormalExpression(element, ACTIVITI_PROPERTIES.COMPLETION_CONDITION, newValue, bpmnFactory);
        },
    });
}
function getElementVariable(element) {
    return getProperty(element, 'activiti:elementVariable');
}

function createFormalExpression(parent, body, bpmnFactory) {
    return elementHelper.createElement('bpmn:FormalExpression', { body: body }, parent, bpmnFactory);
}

function getProperty(element, propertyName) {
    const loopCharacteristics = getLoopCharacteristics(element);
    return loopCharacteristics && loopCharacteristics.get(propertyName);
}

function getBody(expression) {
    return expression && expression.get('body');
}

function getLoopCardinality(element) {
    return getProperty(element, 'loopCardinality');
}

function getLoopCardinalityValue(element) {
    const loopCardinality = getLoopCardinality(element);
    return getBody(loopCardinality);
}

function getCompletionCondition(element) {
    return getProperty(element, ACTIVITI_PROPERTIES.COMPLETION_CONDITION);
}

function getCompletionConditionValue(element) {
    const completionCondition = getCompletionCondition(element);
    return getBody(completionCondition);
}

function getCollection(element) {
    return getProperty(element, 'activiti:collection');
}
function getLoopCharacteristics(element) {
    const bo = getBusinessObject(element);
    return bo.loopCharacteristics;
}

function ensureMultiInstanceSupported(element) {
    const loopCharacteristics = getLoopCharacteristics(element);
    return !!loopCharacteristics && is(loopCharacteristics, 'activiti:Collectable');
}
