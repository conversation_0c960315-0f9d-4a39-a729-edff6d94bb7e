/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { is } from 'bpmn-js/lib/util/ModelUtil';
import TextField from '../../../entries/TextField.js';
import <PERSON>Field from '../../../entries/SelectField';
import { ACTIVITI_PROPERTIES, ACTIVITI_LABELS, PRIORITY_OPTIONS } from '../../../constants/activiti';

export default function (group, element, translate) {
    if (is(element, 'activiti:Assignable')) {
        // Assignee
        group.entries.push({
            id: ACTIVITI_PROPERTIES.ASSIGNEE,
            label: translate(ACTIVITI_LABELS.ASSIGNEE),
            modelProperty: ACTIVITI_PROPERTIES.ASSIGNEE,
            element,
            component: TextField,
        });

        // Candidate Users
        group.entries.push({
            id: ACTIVITI_PROPERTIES.CANDIDATE_USERS,
            label: translate(ACTIVITI_LABELS.CANDIDATE_USERS),
            modelProperty: ACTIVITI_PROPERTIES.CANDIDATE_USERS,
            element,
            component: TextField,
        });

        // Candidate Groups
        group.entries.push({
            id: ACTIVITI_PROPERTIES.CANDIDATE_GROUPS,
            label: translate(ACTIVITI_LABELS.CANDIDATE_GROUPS),
            modelProperty: ACTIVITI_PROPERTIES.CANDIDATE_GROUPS,
            element,
            component: TextField,
        });

        // Due Date
        group.entries.push({
            id: ACTIVITI_PROPERTIES.DUE_DATE,
            label: translate(ACTIVITI_LABELS.DUE_DATE),
            modelProperty: ACTIVITI_PROPERTIES.DUE_DATE,
            description: translate(
                'The due date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)'
            ),
            element,
            component: TextField,
        });

        // Follow Up Date
        group.entries.push({
            id: ACTIVITI_PROPERTIES.FOLLOW_UP_DATE,
            label: translate(ACTIVITI_LABELS.FOLLOW_UP_DATE),
            modelProperty: ACTIVITI_PROPERTIES.FOLLOW_UP_DATE,
            description: translate(
                'The follow up date as an EL expression (e.g. ${someDate} or an ' +
                    'ISO date (e.g. 2015-06-26T09:54:00)'
            ),
            element,
            component: TextField,
        });

        // Priority
        group.entries.push({
            id: ACTIVITI_PROPERTIES.PRIORITY,
            label: translate(ACTIVITI_LABELS.PRIORITY),
            modelProperty: ACTIVITI_PROPERTIES.PRIORITY,
            element,
            component: SelectField,
            getOptions: () => {
                return PRIORITY_OPTIONS;
            },
        });
    }
}
