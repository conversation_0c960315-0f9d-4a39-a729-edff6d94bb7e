/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { asyncCapableHelper, cmdHelper, elementHelper, eventDefinitionHelper } from '../../../helper';
import { is } from 'bpmn-js/lib/util/ModelUtil';
import { ACTIVITI_PROPERTIES, ACTIVITI_LABELS } from '../../../constants/activiti';
import TextField from '../../../entries/TextField.js';

function isActivitiAsync(bo) {
    return asyncCapableHelper.isActivitiAsync(bo);
}

function getFailedJobRetryTimeCycle(bo) {
    return asyncCapableHelper.getFailedJobRetryTimeCycle(bo);
}

function removeFailedJobRetryTimeCycle(bo, element) {
    return asyncCapableHelper.removeFailedJobRetryTimeCycle(bo, element);
}

function createExtensionElements(parent, bpmnFactory) {
    return elementHelper.createElement('bpmn:ExtensionElements', { values: [] }, parent, bpmnFactory);
}

function createFailedJobRetryTimeCycle(parent, bpmnFactory, cycle) {
    return elementHelper.createElement('activiti:FailedJobRetryTimeCycle', { body: cycle }, parent, bpmnFactory);
}

export default function (element, bpmnFactory, options, translate) {
    const getBusinessObject = options.getBusinessObject;

    const idPrefix = options.idPrefix || '';
    const labelPrefix = options.labelPrefix || '';

    return [
        {
            id: idPrefix + 'retryTimeCycle',
            label: labelPrefix + translate(ACTIVITI_LABELS.CYCLE),
            modelProperty: ACTIVITI_PROPERTIES.CYCLE,
            component: TextField,
            get: (element, node) => {
                const retryTimeCycle = getFailedJobRetryTimeCycle(getBusinessObject(element));
                const value = retryTimeCycle && retryTimeCycle.get('body');
                return value;
            },
            setCommands: (newValue) => {
                const bo = getBusinessObject(element);

                if (newValue === '' || typeof newValue === 'undefined') {
                    // remove retry time cycle element(s)
                    return removeFailedJobRetryTimeCycle(bo, element);
                }

                let retryTimeCycle = getFailedJobRetryTimeCycle(bo);

                if (!retryTimeCycle) {
                    // add new retry time cycle element
                    let commands: any = [];

                    let extensionElements = bo.get('extensionElements');

                    if (!extensionElements) {
                        extensionElements = createExtensionElements(bo, bpmnFactory);
                        commands.push(
                            cmdHelper.updateBusinessObject(element, bo, {
                                extensionElements: extensionElements,
                            })
                        );
                    }

                    retryTimeCycle = createFailedJobRetryTimeCycle(extensionElements, bpmnFactory, newValue);
                    commands.push(
                        cmdHelper.addAndRemoveElementsFromList(
                            element,
                            extensionElements,
                            'values',
                            'extensionElements',
                            [retryTimeCycle],
                            []
                        )
                    );

                    return commands;
                }

                // update existing retry time cycle element
                return cmdHelper.updateBusinessObject(element, retryTimeCycle, {
                    body: newValue,
                });
            },
            hidden: (element) => {
                const bo = getBusinessObject(element);

                if (bo && isActivitiAsync(bo)) {
                    return false;
                }

                if (is(element, 'bpmn:Event')) {
                    return !eventDefinitionHelper.getTimerEventDefinition(element);
                }

                return true;
            },
        },
    ];
}
