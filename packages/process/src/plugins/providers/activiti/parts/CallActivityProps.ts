/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { getBusinessObject, is } from 'bpmn-js/lib/util/ModelUtil';

import CheckboxField from '../../../entries/CheckboxField';
import TextField from '../../../entries/TextField';
import { ACTIVITI_PROPERTIES, ACTIVITI_LABELS } from '../../../constants/activiti';

function getCallableType(element) {
    const bo = getBusinessObject(element);

    const boCalledElement = bo.get('calledElement'),
        boCaseRef = bo.get('activiti:caseRef');

    let callActivityType = '';
    if (typeof boCalledElement !== 'undefined') {
        callActivityType = 'bpmn';
    } else if (typeof boCaseRef !== 'undefined') {
        callActivityType = 'cmmn';
    }

    return callActivityType;
}

export default function (group, element, bpmnFactory, translate) {
    if (!is(element, 'activiti:CallActivity')) {
        return;
    }

    // Inherit business key (CorrelationId)
    group.entries.push({
        id: ACTIVITI_PROPERTIES.INHERIT_BUSINESS_KEY,
        label: translate(ACTIVITI_LABELS.INHERIT_BUSINESS_KEY),
        modelProperty: ACTIVITI_PROPERTIES.INHERIT_BUSINESS_KEY,
        component: CheckboxField,
    });

    // Called element
    group.entries.push({
        id: ACTIVITI_PROPERTIES.CALLED_ELEMENT,
        label: translate(ACTIVITI_LABELS.CALLED_ELEMENT),
        modelProperty: ACTIVITI_PROPERTIES.CALLED_ELEMENT,
        component: TextField,
    });
}
