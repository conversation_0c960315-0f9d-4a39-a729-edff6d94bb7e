/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import userTaskProps from './parts/UserTaskProps';
import multiInstanceProps from './parts/MultiInstanceProps';
import serviceTaskProps from './parts/ServiceTaskProps';
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';
import callActivityProps from './parts/CallActivityProps';

const ACTIVITI_PROPERTIES_PRIORITY = 500;

export default function ActivitiPropertiesProvider(propertiesPanel, bpmnFactory, translate) {
    // API ////////

    /**
     * Return the groups provided for the given element.
     *
     * @param {DiagramElement} element
     *
     * @return {(Object[]) => (Object[])} groups middleware
     */
    this.getGroups = function (element) {
        /**
         * We return a middleware that modifies
         * the existing groups.
         *
         * @param {Object[]} groups
         *
         * @return {Object[]} modified groups
         */
        return function (groups) {
            // Details group
            const detailsGroup = buildDetailsGroup(groups, element, bpmnFactory, translate);

            const multiInstanceGroup = buildMultiInstanceGroup(translate, element, bpmnFactory);

            addGroup(groups, detailsGroup);
            groups = updateMultiInstanceGroup(groups, 'multiInstance', multiInstanceGroup);

            return groups;
        };
    };

    // registration ////////

    // Register our custom magic properties provider.
    // Use a lower priority to ensure it is loaded after
    // the basic BPMN properties.
    propertiesPanel.registerProvider(ACTIVITI_PROPERTIES_PRIORITY, this);
}

ActivitiPropertiesProvider.$inject = ['propertiesPanel', 'bpmnFactory', 'translate'];

function addGroup(groups, newGroup) {
    if (newGroup && newGroup.entries?.length > 0) {
        groups.push(newGroup);
    }
}

function buildDetailsGroup(groups, element, bpmnFactory, translate) {
    let detailsGroup = {
        id: 'details',
        label: translate('Details'),
        entries: [],
    };

    // Add Activiti specific UserTask properties
    userTaskProps(detailsGroup, element, translate);
    serviceTaskProps(detailsGroup, element, bpmnFactory, translate);
    callActivityProps(detailsGroup, element, bpmnFactory, translate);
    return detailsGroup;
}

function buildMultiInstanceGroup(translate, element, bpmnFactory) {
    let multiInstanceGroup = {
        id: 'multiInstance',
        label: translate('Multi Instance'),
        entries: [],
    };

    // Add Activiti specific UserTask properties
    multiInstanceProps(multiInstanceGroup, element, bpmnFactory, translate);
    return multiInstanceGroup;
}

function updateMultiInstanceGroup(groups, groupName, newGroup) {
    return groups.map((group) => (group.id === groupName ? newGroup : group));
}
function findGroup(groups, id) {
    return groups.find((g) => g.id === id);
}
