/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import reduce from 'lodash/reduce';
import keys from 'lodash/keys';
import forEach from 'lodash/forEach';
import { is } from 'bpmn-js/lib/util/ModelUtil';

export default function UpdateBusinessObjectHandler(elementRegistry) {
    this._elementRegistry = elementRegistry;
}

UpdateBusinessObjectHandler.$inject = ['elementRegistry'];

/**
 * returns the root element
 */
function getRoot(businessObject) {
    var parent = businessObject;
    while (parent.$parent) {
        parent = parent.$parent;
    }
    return parent;
}

function getProperties(businessObject, propertyNames) {
    return reduce(
        propertyNames,
        function (result, key) {
            result[key] = businessObject.get(key);
            return result;
        },
        {}
    );
}

function setProperties(businessObject, properties) {
    forEach(properties, function (value, key) {
        businessObject.set(key, value);
    });
}

// api /////////////////////////////////////////////

/**
 * Updates a business object with a list of new properties
 *
 * @method  UpdateBusinessObjectHandler#execute
 *
 * @param {Object} context
 * @param {djs.model.Base} context.element the element which has a child business object updated
 * @param {moddle.businessObject} context.businessObject the businessObject to update
 * @param {Object} context.properties a list of properties to set on the businessObject
 *
 * @return {Array<djs.mode.Base>} the updated element
 */
UpdateBusinessObjectHandler.prototype.execute = function (context) {
    var element = context.element,
        businessObject = context.businessObject,
        rootElements = getRoot(businessObject).rootElements,
        referenceType = context.referenceType,
        referenceProperty = context.referenceProperty,
        changed = [element]; // this will not change any diagram-js elements

    if (!element) {
        throw new Error('element required');
    }

    if (!businessObject) {
        throw new Error('businessObject required');
    }

    var properties = context.properties,
        oldProperties = context.oldProperties || getProperties(businessObject, keys(properties));

    // check if there the update needs an external element for reference
    if (typeof referenceType !== 'undefined' && typeof referenceProperty !== 'undefined') {
        forEach(rootElements, function (rootElement) {
            if (is(rootElement, referenceType)) {
                if (rootElement.id === properties[referenceProperty]) {
                    properties[referenceProperty] = rootElement;
                }
            }
        });
    }

    // update properties
    setProperties(businessObject, properties);

    // store old values
    context.oldProperties = oldProperties;
    context.changed = changed;

    // indicate changed on objects affected by the update
    return changed;
};

/**
 * Reverts the update
 *
 * @method  UpdateBusinessObjectHandler#revert
 *
 * @param {Object} context
 *
 * @return {djs.mode.Base} the updated element
 */
UpdateBusinessObjectHandler.prototype.revert = function (context) {
    var oldProperties = context.oldProperties,
        businessObject = context.businessObject;

    // update properties
    setProperties(businessObject, oldProperties);

    return context.changed;
};
