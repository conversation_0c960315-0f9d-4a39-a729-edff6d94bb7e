/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import forEach from 'lodash/forEach';

function UpdateBusinessObjectListHandler(elementRegistry, bpmnFactory) {
    this._elementRegistry = elementRegistry;
    this._bpmnFactory = bpmnFactory;
}

UpdateBusinessObjectListHandler.$inject = ['elementRegistry', 'bpmnFactory'];

export default UpdateBusinessObjectListHandler;

function ensureNotNull(prop, name) {
    if (!prop) {
        throw new Error(name + 'required');
    }
    return prop;
}

// api /////////////////////////////////////////////

/**
 * Updates a element under a provided parent.
 */
UpdateBusinessObjectListHandler.prototype.execute = function (context) {
    var currentObject = ensureNotNull(context.currentObject, 'currentObject'),
        propertyName = ensureNotNull(context.propertyName, 'propertyName'),
        updatedObjectList = context.updatedObjectList,
        objectsToRemove = context.objectsToRemove || [],
        objectsToAdd = context.objectsToAdd || [],
        changed = [context.element], // this will not change any diagram-js elements
        referencePropertyName;

    if (context.referencePropertyName) {
        referencePropertyName = context.referencePropertyName;
    }

    var objectList = currentObject[propertyName];
    // adjust array reference in the parent business object
    context.previousList = currentObject[propertyName];

    if (updatedObjectList) {
        currentObject[propertyName] = updatedObjectList;
    } else {
        var listCopy: any = [];
        // remove all objects which should be removed
        forEach(objectList, function (object) {
            if (objectsToRemove.indexOf(object) == -1) {
                listCopy.push(object);
            }
        });
        // add all objects which should be added
        listCopy = listCopy.concat(objectsToAdd);

        // set property to new list
        if (listCopy.length > 0 || !referencePropertyName) {
            // as long as there are elements in the list update the list
            currentObject[propertyName] = listCopy;
        } else if (referencePropertyName) {
            // remove the list when it is empty
            var parentObject = currentObject.$parent;
            parentObject.set(referencePropertyName, undefined);
        }
    }

    context.changed = changed;

    // indicate changed on objects affected by the update
    return changed;
};

/**
 * Reverts the update
 *
 * @method  CreateBusinessObjectListHandler#revert
 *
 * @param {Object} context
 *
 * @return {djs.mode.Base} the updated element
 */
UpdateBusinessObjectListHandler.prototype.revert = function (context) {
    var currentObject = context.currentObject,
        propertyName = context.propertyName,
        previousList = context.previousList,
        parentObject = currentObject.$parent;

    if (context.referencePropertyName) {
        parentObject.set(context.referencePropertyName, currentObject);
    }

    // remove new element
    currentObject.set(propertyName, previousList);

    return context.changed;
};
