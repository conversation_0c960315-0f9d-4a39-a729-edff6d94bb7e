/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import forEach from 'lodash/forEach';
import UpdateBusinessObjectHandler from './UpdateBusinessObjectHandler';
import CreateAndReferenceHandler from './CreateAndReferenceHandler';
import CreateBusinessObjectListHandler from './CreateBusinessObjectListHandler';
import UpdateBusinessObjectListHandler from './UpdateBusinessObjectListHandler';
import MultiCommandHandler from './MultiCommandHandler';

const HANDLERS = {
    'properties-panel.update-businessobject': UpdateBusinessObjectHandler,
    'properties-panel.create-and-reference': CreateAndReferenceHandler,
    'properties-panel.create-businessobject-list': CreateBusinessObjectListHandler,
    'properties-panel.update-businessobject-list': UpdateBusinessObjectListHandler,
    // 'properties-panel.multi-command-executor': MultiCommandHandler
};

function CommandInitializer(eventBus, commandStack) {
    eventBus.on('diagram.init', function () {
        forEach(HANDLERS, function (handler, id) {
            commandStack.registerHandler(id, handler);
        });
    });
}

CommandInitializer.$inject = ['eventBus', 'commandStack'];

export default {
    __init__: [CommandInitializer],
};
