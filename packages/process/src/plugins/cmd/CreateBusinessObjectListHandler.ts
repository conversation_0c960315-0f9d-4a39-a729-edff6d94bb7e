/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import forEach from 'lodash/forEach';
import { elementHelper } from '../helper';

function CreateBusinessObjectListHandler(elementRegistry, bpmnFactory) {
    this._elementRegistry = elementRegistry;
    this._bpmnFactory = bpmnFactory;
}

CreateBusinessObjectListHandler.$inject = ['elementRegistry', 'bpmnFactory'];

export default CreateBusinessObjectListHandler;

function ensureNotNull(prop, name) {
    if (!prop) {
        throw new Error(name + ' required');
    }
    return prop;
}
function ensureList(prop, name) {
    if (!prop || Object.prototype.toString.call(prop) !== '[object Array]') {
        throw new Error(name + ' needs to be a list');
    }
    return prop;
}

// api /////////////////////////////////////////////

/**
 * Creates a new element under a provided parent and updates / creates a reference to it in
 * one atomic action.
 *
 * @method  CreateBusinessObjectListHandler#execute
 *
 * @param {Object} context
 * @param {djs.model.Base} context.element which is the context for the reference
 * @param {moddle.referencingObject} context.referencingObject the object which creates the reference
 * @param {String} context.referenceProperty the property of the referencingObject which makes the reference
 * @param {moddle.newObject} context.newObject the new object to add
 * @param {moddle.newObjectContainer} context.newObjectContainer the container for the new object
 *
 * @return {Array<djs.mode.Base>} the updated element
 */
CreateBusinessObjectListHandler.prototype.execute = function (context) {
    var currentObject = ensureNotNull(context.currentObject, 'currentObject'),
        propertyName = ensureNotNull(context.propertyName, 'propertyName'),
        newObjects = ensureList(context.newObjects, 'newObjects'),
        changed = [context.element]; // this will not change any diagram-js elements

    var childObjects: any[] = [];
    var self = this;

    // create new array of business objects
    forEach(newObjects, function (obj) {
        var element: any = elementHelper.createElement(obj.type, obj.properties, currentObject, self._bpmnFactory);

        childObjects.push(element);
    });
    context.childObject = childObjects;

    // adjust array reference in the parent business object
    context.previousChilds = currentObject[propertyName];
    currentObject[propertyName] = childObjects;

    context.changed = changed;

    // indicate changed on objects affected by the update
    return changed;
};

/**
 * Reverts the update
 *
 * @method  CreateBusinessObjectListHandler#revert
 *
 * @param {Object} context
 *
 * @return {djs.mode.Base} the updated element
 */
CreateBusinessObjectListHandler.prototype.revert = function (context) {
    var currentObject = context.currentObject,
        propertyName = context.propertyName,
        previousChilds = context.previousChilds;

    // remove new element
    currentObject.set(propertyName, previousChilds);

    return context.changed;
};
