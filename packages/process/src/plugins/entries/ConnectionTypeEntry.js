/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SelectEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { userService, fetch, integrationUrls } from '@tripudiotech/admin-api';
import { useEffect, useState } from '@bpmn-io/properties-panel/preact/hooks';

const connectionProperty = 'connection';
const ConnectionTypeEntry = ({ label, description, modelProperty, element, id, get, emptyParameter }) => {
    const [connectionTypes, setConnectionTypes] = useState([]);
    const [selectedType, setSelectedType] = useState(null);
    const [connections, setConnections] = useState([]);
    const [isLoaded, setIsLoaded] = useState(false);
    const modeling = useService('modeling');
    const translate = useService('translate');
    const debounce = useService('debounceInput');

    useEffect(() => {
        fetch({
            ...integrationUrls.getConnectionTypes,
        }).then(({ data }) => {
            setIsLoaded(true);
            setConnectionTypes(data.data);
        });
    }, []);

    useEffect(() => {
        if (selectedType) {
            fetch({
                ...integrationUrls.getConnectionsUnderConnectionType,
                params: {
                    connectionTypeId: selectedType?.id,
                },
            }).then(({ data }) => {
                setConnections(data.data);
            });
        }
    }, [selectedType]);

    const getValue = () => {
        return element.businessObject[modelProperty] || '';
    };

    const getConnectionValue = () => {
        return element.businessObject[connectionProperty] || '';
    };
    const getConnectionOptions = () => {
        return connections.map((connection) => ({
            label: connection.name,
            value: connection.name,
        }));
    };

    const setConnectionValue = (value) => {
        if (emptyParameter && value === '') {
            newValue = undefined;
        }

        // Update current model properties
        return modeling.updateProperties(element, {
            [connectionProperty]: value,
        });
    };
    const getOptions = () => {
        return connectionTypes.map((connectionType) => ({
            label: connectionType.name,
            value: connectionType.name,
        }));
    };

    const setValue = (value) => {
        if (emptyParameter && value === '') {
            newValue = undefined;
        }
        const option = connectionTypes.find((connectionType) => connectionType.name === value);
        setSelectedType(option);
        // Update current model properties
        return modeling.updateProperties(element, {
            [modelProperty]: value,
        });
    };
    const getter = get || getValue;

    return (
        <>
            <SelectEntry
                id={id}
                element={element}
                description={translate(description || '')}
                label={'Connection Type'}
                getValue={getter}
                setValue={setValue}
                debounce={debounce}
                getOptions={getOptions}
            />
            <SelectEntry
                id={'connection-field'}
                element={element}
                description={translate(description || '')}
                label={'Connection'}
                getValue={getConnectionValue}
                setValue={setConnectionValue}
                debounce={debounce}
                getOptions={getConnectionOptions}
            />
        </>
    );
};

export default ConnectionTypeEntry;
