/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SelectEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { integrationUrls, authenticationService, fetch } from '@tripudiotech/admin-api';
import { useEffect, useState } from '@bpmn-io/properties-panel/preact/hooks';
import TextField from './TextField';
import ConnectionTypeEntry from './ConnectionTypeEntry';

const ConnectionEntry = ({
    label,
    description,
    modelProperty,
    emptyParameter,
    element,
    id,
    get,
    setCommands,
    validate,
    hidden,
}) => {
    const modeling = useService('modeling');
    const translate = useService('translate');
    const debounce = useService('debounceInput');
    const [isLoaded, setIsLoaded] = useState(false);
    const [routes, setRoutes] = useState([]);

    useEffect(() => {
        const getRoutes = async () => {
            try {
                const { data } = await fetch({
                    ...integrationUrls.getAvailableRoutes,
                });
                setRoutes(data);
            } finally {
                setIsLoaded(true);
            }
        };
        getRoutes();
    }, []);

    const getValue = () => {
        return element.businessObject[modelProperty] || '';
    };
    const getRouteValue = () => {
        const val = element.businessObject[modelProperty] || '';
        if (val.includes('integration.invoke')) {
            return val.replace(`#{integration.invoke('`, '').replace(`')}`, '');
        }
        return 'None';
    };

    const getOptions = () => {
        return routes
            .map((route) => ({
                label: route.routerId?.replace(`${authenticationService.getTenant()}.`, ''),
                value: route.endpointUri,
            }))
            .concat([
                {
                    label: 'None',
                    value: 'None',
                },
            ]);
    };

    const setValue = (value) => {
        if (emptyParameter && value === '') {
            newValue = undefined;
        }

        // Update current model properties
        return modeling.updateProperties(element, {
            [modelProperty]: value,
        });
    };

    const setRouteValue = (value) => {
        if (emptyParameter && value === '') {
            newValue = undefined;
        }
        return modeling.updateProperties(element, {
            [modelProperty]: `#{integration.invoke('${value}')}`,
        });
    };
    const getter = get || getValue;

    return (
        <>
            <div className="bio-properties-panel-select">
                {isLoaded ? (
                    <>
                        <SelectEntry
                            id={id}
                            element={element}
                            description={translate(description || '')}
                            label={'Integration Route'}
                            getValue={getRouteValue}
                            setValue={setRouteValue}
                            debounce={debounce}
                            getOptions={getOptions}
                        />
                        <TextField
                            label={label}
                            description={description}
                            modelProperty={modelProperty}
                            element={element}
                            id={id}
                            get={get}
                            setCommands={setCommands}
                            validate={validate}
                            hidden={hidden}
                        />
                    </>
                ) : (
                    <div style={{ marginLeft: '18px' }}>Loading...</div>
                )}
            </div>
        </>
    );
};

export default ConnectionEntry;
