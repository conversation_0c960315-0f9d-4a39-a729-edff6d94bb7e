/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SelectEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';

const SelectField = ({
    label,
    description,
    modelProperty,
    element,
    id,
    getOptions,
    get,
    setCommands,
    validate,
    hidden,
    emptyParameter,
}) => {
    const modeling = useService('modeling');
    const translate = useService('translate');
    const debounce = useService('debounceInput');
    const commandStack = useService('commandStack');

    const getValue = () => {
        return element.businessObject[modelProperty] || '';
    };

    const setValue = (value) => {
        let newValue = value;
        if (emptyParameter && value === '') {
            newValue = undefined;
        }
        // Run the custom command instead of updating current element properties
        if (setCommands) {
            const commands = setCommands(newValue);
            if (Array.isArray(commands)) {
                commands.forEach(({ cmd, context }) => {
                    commandStack.execute(cmd, context);
                });
            } else if (commands) {
                commandStack.execute(commands.cmd, commands.context);
            }

            return;
        }

        // Update current model properties
        return modeling.updateProperties(element, {
            [modelProperty]: value,
        });
    };

    if (hidden && hidden(element)) {
        return null;
    }

    const getter = get || getValue;

    return (
        <SelectEntry
            id={id}
            element={element}
            description={translate(description || '')}
            label={translate(label)}
            getValue={getter}
            setValue={setValue}
            debounce={debounce}
            getOptions={getOptions}
        />
    );
};

export default SelectField;
