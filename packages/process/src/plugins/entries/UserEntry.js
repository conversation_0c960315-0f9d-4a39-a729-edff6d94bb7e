/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SelectEntry } from '@bpmn-io/properties-panel';
import { useService } from 'bpmn-js-properties-panel';
import { userService } from '@tripudiotech/admin-api';
import { useEffect, useState } from '@bpmn-io/properties-panel/preact/hooks';

const UserEntry = ({ label, description, modelProperty, element, id, get, emptyParameter }) => {
    const [users, setUsers] = useState([]);
    const [isLoaded, setIsLoaded] = useState(false);
    const modeling = useService('modeling');
    const translate = useService('translate');
    const debounce = useService('debounceInput');

    useEffect(() => {
        userService.getUsers(0, 500).then((response) => {
            if (response.status === 200) {
                setUsers(
                    response.data.data[0].map((user) => ({
                        label: user.email,
                        value: user.email,
                    }))
                );
                setIsLoaded(true);
            }
        });
    }, []);

    const getValue = () => {
        return element.businessObject[modelProperty] || '';
    };

    const getOptions = () => {
        return users;
    };

    const setValue = (value) => {
        if (emptyParameter && value === '') {
            newValue = undefined;
        }

        // Update current model properties
        return modeling.updateProperties(element, {
            [modelProperty]: value,
        });
    };
    const getter = get || getValue;

    return (
        isLoaded && (
            <SelectEntry
                id={id}
                element={element}
                description={translate(description || '')}
                label={translate(label)}
                getValue={getter}
                setValue={setValue}
                debounce={debounce}
                getOptions={getOptions}
            />
        )
    );
};

export default UserEntry;
