/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import map from 'lodash/map';
import forEach from 'lodash/forEach';
import { is, getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';

import { DELEGATE_TYPES, SERVICE_TASK_PROPERTIES } from '../constants/activiti';

export const extensionElementsHelper = {
    getExtensionElements: (bo, type) => {
        const extensionElements = bo.get('extensionElements');
        if (typeof extensionElements !== 'undefined') {
            const extensionValues = extensionElements.get('values');
            if (typeof extensionValues !== 'undefined') {
                const elements = extensionValues.filter(function (value) {
                    return is(value, type);
                });
                if (elements.length) {
                    return elements;
                }
            }
        }
    },
    addEntry: (bo, element, entry, bpmnFactory) => {
        let extensionElements = bo.get('extensionElements');

        // if there is no extensionElements list, create one
        if (!extensionElements) {
            extensionElements = elementHelper.createElement(
                'bpmn:ExtensionElements',
                { values: [entry] },
                bo,
                bpmnFactory
            );
            return { extensionElements: extensionElements };
        } else {
            // add new failedJobRetryExtensionElement to existing extensionElements list
            return cmdHelper.addElementsTolist(element, extensionElements, 'values', [entry]);
        }
    },
    removeEntry: (bo, element, entry) => {
        const extensionElements = bo.get('extensionElements');

        if (!extensionElements) {
            // return an empty command when there is no extensionElements list
            return {};
        }

        return cmdHelper.removeElementsFromList(element, extensionElements, 'values', 'extensionElements', [entry]);
    },
};

export const eventDefinitionHelper = {
    getEventDefinition: (element, eventType) => {
        const bo = getBusinessObject(element);
        let eventDefinition = null;

        if (bo.eventDefinitions) {
            forEach(bo.eventDefinitions, (event) => {
                if (is(event, eventType)) {
                    eventDefinition = event;
                }
            });
        }

        return eventDefinition;
    },
    getTimerEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:TimerEventDefinition');
    },
    getMessageEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:MessageEventDefinition');
    },
    getSignalEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:SignalEventDefinition');
    },
    getErrorEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:ErrorEventDefinition');
    },
    getEscalationEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:EscalationEventDefinition');
    },
    getCompensateEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:CompensateEventDefinition');
    },
    getLinkEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:LinkEventDefinition');
    },
    getConditionalEventDefinition: (element) => {
        return eventDefinitionHelper.getEventDefinition(element, 'bpmn:ConditionalEventDefinition');
    },
};

export const asyncCapableHelper = {
    isActivitiAsync: (bo) => {
        return !!bo.get('activiti:async');
    },
    isExclusive: (bo) => {
        return !!bo.get('activiti:exclusive');
    },
    getFailedJobRetryTimeCycle: (bo) => {
        return (extensionElementsHelper.getExtensionElements(bo, 'activiti:FailedJobRetryTimeCycle') || [])[0];
    },
    removeFailedJobRetryTimeCycle: (bo, element) => {
        const retryTimeCycles = extensionElementsHelper.getExtensionElements(bo, 'activiti:FailedJobRetryTimeCycle');
        return map(retryTimeCycles, function (cycle) {
            return extensionElementsHelper.removeEntry(bo, element, cycle);
        });
    },
};

export const elementHelper = {
    createElement: (elementType, properties, parent, factory) => {
        let element = factory.create(elementType, properties);
        element.$parent = parent;

        return element;
    },
};

export const cmdHelper = {
    updateBusinessObject: (element, businessObject, newProperties) => {
        return {
            cmd: 'properties-panel.update-businessobject',
            context: {
                element: element,
                businessObject: businessObject,
                properties: newProperties,
            },
        };
    },
    updateProperties: (element, properties) => {
        return {
            cmd: 'element.updateProperties',
            context: { element: element, properties: properties },
        };
    },
    addElementsTolist: (element, businessObject, listPropertyName, objectsToAdd) => {
        return {
            cmd: 'properties-panel.update-businessobject-list',
            context: {
                element: element,
                currentObject: businessObject,
                propertyName: listPropertyName,
                objectsToAdd: objectsToAdd,
            },
        };
    },
    removeElementsFromList: (element, businessObject, listPropertyName, referencePropertyName, objectsToRemove) => {
        return {
            cmd: 'properties-panel.update-businessobject-list',
            context: {
                element: element,
                currentObject: businessObject,
                propertyName: listPropertyName,
                referencePropertyName: referencePropertyName,
                objectsToRemove: objectsToRemove,
            },
        };
    },
    addAndRemoveElementsFromList: (
        element,
        businessObject,
        listPropertyName,
        referencePropertyName,
        objectsToAdd,
        objectsToRemove
    ) => {
        return {
            cmd: 'properties-panel.update-businessobject-list',
            context: {
                element: element,
                currentObject: businessObject,
                propertyName: listPropertyName,
                referencePropertyName: referencePropertyName,
                objectsToAdd: objectsToAdd,
                objectsToRemove: objectsToRemove,
            },
        };
    },
    setList: (element, businessObject, listPropertyName, updatedObjectList) => {
        return {
            cmd: 'properties-panel.update-businessobject-list',
            context: {
                element: element,
                currentObject: businessObject,
                propertyName: listPropertyName,
                updatedObjectList: updatedObjectList,
            },
        };
    },
};

export const extensionsElementHelper = {
    getExtensionElements: (bo, type) => {
        const extensionElements = bo.get('extensionElements');
        if (typeof extensionElements !== 'undefined') {
            const extensionValues = extensionElements.get('values');
            if (typeof extensionValues !== 'undefined') {
                const elements = extensionValues.filter(function (value) {
                    return is(value, type);
                });
                if (elements.length) {
                    return elements;
                }
            }
        }
    },
};

export const implementationHelper = {
    isServiceTaskLike: (element) => {
        return is(element, 'activiti:ServiceTaskLike');
    },
    isDmnCapable: (element) => {
        return is(element, 'activiti:DmnCapable');
    },
    isExternalCapable: (element) => {
        return is(element, 'activiti:ExternalCapable');
    },
    isTaskListener: (element) => {
        return is(element, 'activiti:TaskListener');
    },
    isExecutionListener: (element) => {
        return is(element, 'activiti:ExecutionListener');
    },
    isListener: (element) => {
        return implementationHelper.isTaskListener(element) || implementationHelper.isExecutionListener(element);
    },
    isSequenceFlow: (element) => {
        return is(element, 'bpmn:SequenceFlow');
    },
    getServiceTaskLikeBusinessObject: (element) => {
        if (is(element, 'bpmn:IntermediateThrowEvent') || is(element, 'bpmn:EndEvent')) {
            const messageEventDefinition = eventDefinitionHelper.getMessageEventDefinition(element);
            if (messageEventDefinition) {
                element = messageEventDefinition;
            }
        }

        return implementationHelper.isServiceTaskLike(element) && getBusinessObject(element);
    },
    isServiceTask: (element) => {
        return is(element, 'bpmn:ServiceTask');
    },
    getImplementationType: (element) => {
        let bo = implementationHelper.getServiceTaskLikeBusinessObject(element);

        if (!bo) {
            if (implementationHelper.isListener(element)) {
                bo = element;
            } else {
                return;
            }
        }

        if (implementationHelper.isDmnCapable(bo)) {
            const decisionRef = bo.get('activiti:decisionRef');
            if (typeof decisionRef !== 'undefined') {
                return 'dmn';
            }
        }

        if (implementationHelper.isServiceTaskLike(bo)) {
            const connectors = extensionsElementHelper.getExtensionElements(bo, 'activiti:Connector');
            if (typeof connectors !== 'undefined') {
                return 'connector';
            }
        }

        if (implementationHelper.isExternalCapable(bo)) {
            const type = bo.get('activiti:type');
            if (type === 'external') {
                return 'external';
            }
        }

        const cls = bo.get('activiti:class');
        if (typeof cls !== 'undefined') {
            return 'class';
        }

        const expression = bo.get('activiti:expression');
        if (typeof expression !== 'undefined') {
            return 'expression';
        }

        const delegateExpression = bo.get('activiti:delegateExpression');
        if (typeof delegateExpression !== 'undefined') {
            return 'delegateExpression';
        }

        if (implementationHelper.isListener(bo)) {
            const script = bo.get('script');
            if (typeof script !== 'undefined') {
                return 'script';
            }
        }

        if (implementationHelper.isServiceTask(bo)) {
            return 'implementation';
        }
    },
};

export const serviceTaskHelper = {
    isDelegate: (type) => {
        return DELEGATE_TYPES.indexOf(type) !== -1;
    },
    getAttribute: (type) => {
        return SERVICE_TASK_PROPERTIES[type];
    },
    getBusinessObject: (element) => {
        return implementationHelper.getServiceTaskLikeBusinessObject(element);
    },
    getImplementationType: (element) => {
        return implementationHelper.getImplementationType(element);
    },
    isServiceTaskLike: (element) => {
        return implementationHelper.isServiceTaskLike(element);
    },
};
