/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useEffect } from 'react';
import { Formik, Form, Field } from 'formik';
import { formHelper } from 'ui-common';
import { Box, FormikTextField } from 'ui-style';
import { AttributeType } from '@tripudiotech/admin-styleguide';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import * as yup from 'yup';
import useProcess from '../stores/useProcess';

const ProcessForm = ({ formRef, processData }: any) => {
    const { schema } = useProcess();
    const [initialValues, setInitialValues] = useState<any>({});
    const [attrs, setAttrs] = useState([]);
    const [validationSchema, setValicationSchema] = useState(null);

    useEffect(() => {
        if (!isEmpty(schema)) {
            const { attributes } = schema;

            setInitialValues(formHelper.buildInitialValue(attributes, processData));
            setAttrs(attributes);

            const schemaObj: any = attributes.reduce((res: any, attr: any) => {
                const { name, type, nullable, displayName } = attr;

                if (type === AttributeType.STRING || AttributeType.TEXT) {
                    let yupValidator = yup.string().nullable();

                    if (!isNil(nullable) && !Boolean(nullable)) {
                        yupValidator = yupValidator.required(formHelper.buildRequiredMessage(displayName));
                    }

                    return {
                        ...res,
                        [name]: yupValidator,
                    };
                }
            }, {});

            setValicationSchema(yup.object(schemaObj));
        }
    }, [schema, processData]);

    return (
        <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={(values, { setSubmitting }) => {}}
            innerRef={formRef}
        >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                {attrs.map((attr) => {
                    const { id, displayName, name, nullable, type } = attr;
                    const isRequired = isNil(nullable) ? false : !Boolean(nullable);

                    if (type === AttributeType.TEXT) {
                        return (
                            <Field
                                key={id}
                                fullWidth
                                multiline
                                minRows={2}
                                component={FormikTextField}
                                label={displayName}
                                name={name}
                                variant="outlined"
                                required={isRequired}
                            />
                        );
                    }

                    return (
                        <Field
                            key={id}
                            fullWidth
                            component={FormikTextField}
                            label={displayName}
                            name={name}
                            variant="outlined"
                            required={isRequired}
                        />
                    );
                })}
            </Box>
        </Formik>
    );
};

export default ProcessForm;
