/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef } from 'react';
import { Box, Button, RightTray, Stack, SchemaFieldSection, CollapsablePanel, LoadingOverlay } from 'ui-style';
import { useToggle } from 'ui-common';
import { notifySuccess } from '@tripudiotech/admin-styleguide';
import ProcessForm from './ProcessForm';
import ProcessNodeDetail from './ProcessNodeDetail';
import BpmnDiagram from './BpmnDiagram';
import { DEFAULT_XML } from '../constants/common';
import useProcess from '../stores/useProcess';

const CreateProcessAction = () => {
    const [open, openToggle] = useToggle(false);
    const { isLoading, createProcess } = useProcess();
    const formRef = useRef(null);
    const modelerRef = useRef(null);
    const xml = DEFAULT_XML;

    const handleClick = async () => {
        openToggle.open();
    };

    const onCreateSuccess = (res) => {
        notifySuccess(
            <span>
                Process <b>{res.name}</b> has been created successfully
            </span>
        );
        formRef.current.resetForm();
        openToggle.close();

        // Dispatch a custom event to refresh the data of process
        const refreshProcessListEvent = new CustomEvent('refresh-process', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
    };

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <Button variant="contained" color="secondary" size="small" onClick={handleClick}>
                Create Process
            </Button>

            <RightTray
                title="Create Process"
                componentName="create-process"
                open={open}
                onClose={openToggle.close}
                onConfirm={async () => {
                    formRef.current.submitForm();
                    if (!formRef.current.isValid) return;

                    const processConfiguration = await modelerRef.current.saveXML({
                        format: true,
                    });
                    createProcess(
                        {
                            ...formRef.current.values,
                            processConfiguration: processConfiguration.xml,
                        },
                        onCreateSuccess,
                        () => formRef.current.setSubmitting(false)
                    );
                }}
                confirmText="Create"
                fullWidth
                disableCloseOnClickOutside
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                    }}
                >
                    <CollapsablePanel>
                        <Box className="general-info--wrapper">
                            <Box
                                className="general-info"
                                sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px' }}
                            >
                                <SchemaFieldSection>General Information</SchemaFieldSection>
                                <ProcessForm formRef={formRef} />
                            </Box>

                            <ProcessNodeDetail />
                        </Box>
                    </CollapsablePanel>

                    <Box
                        sx={{
                            flexGrow: 1,
                            '> .fullscreen': {
                                height: '100%',
                            },
                        }}
                    >
                        <BpmnDiagram modelerRef={modelerRef} xml={xml} processName="" />
                    </Box>
                </Stack>
            </RightTray>

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default CreateProcessAction;
