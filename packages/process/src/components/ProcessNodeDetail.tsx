/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Box, useTheme } from 'ui-style';

const ProcessNodeDetail = () => {
    const {
        palette: { glide },
    } = useTheme();
    return (
        <Box
            sx={{
                '.bio-properties-panel-header': {
                    backgroundColor: glide.background.normal.inversePrimary,
                    padding: '7px 10px 8px',
                    border: 'none',
                    '& .bio-properties-panel-header-type': {
                        color: glide.text.normal.main,
                        fontFamily: 'Work Sans',
                        fontSize: '20px',
                        lineHeight: '150%',
                        letterSpacing: -0.2,
                        fontWeight: 600,
                        textTransform: 'initial',
                    },
                    '.bio-properties-panel-header-label': {
                        display: 'none',
                    },
                    '& .bio-properties-panel-header-icon': {
                        display: 'none',
                    },
                },
                '.bio-properties-panel-entry': {
                    margin: '6px 16px 10px 16px',
                    'input, textarea, select': {
                        border: `1px solid ${glide.stroke.normal.primary}`,
                        borderRadius: '2px!important',
                        color: glide.text.normal.inverseSecondary,
                        fontSize: '14px',
                        lineHeight: '22px',
                        fontWeight: 400,
                        background: glide.background.light,
                        height: '40px',
                        padding: '6px 12px',
                        '&.bio-properties-panel-input.auto-resize': {
                            minHeight: '60px',
                        },
                    },
                    select: {
                        padding: '0 6px',
                    },
                },
                '.bio-properties-panel-description': {
                    fontSize: '11px',
                },
                '.bio-properties-panel-container': {
                    height: 'calc(100vh - 370px)!important',
                },
                '.bio-properties-panel-scroll-container': {
                    padding: 1.5,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '12px',
                    // "& input, textarea, select": {
                    //     padding: "8px",
                    //     borderRadius: "4px",
                    // },
                },
                '.bio-properties-panel-group': {
                    border: `1px solid ${glide.stroke.normal.primary}`,
                    '*': {
                        fontFamily: 'Work Sans',
                    },
                },
                '.bio-properties-panel-group-header': {
                    backgroundColor: glide.background.normal.inversePrimary,
                    position: 'initial',
                    flexDirection: 'row-reverse',
                    justifyContent: 'flex-end',
                    height: '40px',
                    marginBottom: 0,
                    '.bio-properties-panel-group-header-title': {
                        color: glide.text.normal.inverseTertiary,
                        fontFamily: 'Work Sans',
                        margin: 0,
                        fontSize: '14px',
                        fontWeight: 500,
                        lineHeight: '18px',
                    },
                    '.bio-properties-panel-group-header-buttons': {
                        '.bio-properties-panel-dot': {
                            display: 'none',
                        },
                    },
                },
                '.bio-properties-panel-group-entries': {
                    paddingTop: '5px',
                },
                '.bio-properties-panel-label': {
                    fontFamily: 'Work Sans',
                    fontSize: '12px',
                    fontWeight: 500,
                    lineHeight: '16px',
                    color: glide.text.normal.inversePrimary,
                    padding: '0 12px',
                },
            }}
        >
            <Box sx={{ height: '100%', padding: '5px 0 0' }} id="properties-panel"></Box>
        </Box>
    );
};

export default ProcessNodeDetail;
