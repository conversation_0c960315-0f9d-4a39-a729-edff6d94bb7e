/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useRef } from 'react';
import {
    BpmnPropertiesPanelModule,
    BpmnPropertiesProviderModule,
    CamundaPlatformPropertiesProviderModule,
} from 'bpmn-js-properties-panel';
import { Box, IconButton, MuiIcon, useTheme } from 'ui-style';
import Modeler from 'bpmn-js/lib/Modeler';
import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
import 'bpmn-js-properties-panel/dist/assets/properties-panel.css';
import AddExporter from '@bpmn-io/add-exporter';
import { fitView, openDiagram, saveFile, saveImage, resetZoom } from '../utils/bpmn';
import { toast } from 'react-toastify';
import { DEFAULT_XML } from '../constants/common';
import lintModule from 'bpmn-js-bpmnlint';
import 'bpmn-js-bpmnlint/dist/assets/css/bpmn-js-bpmnlint.css';
import camundaModdle from 'camunda-bpmn-moddle/resources/camunda.json';
import camundaCustomProvider from '../plugins/providers/camunda';

//@ts-ignore
import bpmnlintConfig from '../../.bpmnlintrc';

const { Image: ImageIcon, Download: DownloadIcon, Folder: FolderIcon } = MuiIcon;

const BpmnDiagram = ({ xml, modelerRef, processName }) => {
    const theme = useTheme();
    const containerRef = useRef(null);

    useEffect(() => {
        const container = containerRef.current;
        const modeler = new Modeler({
            linting: {
                bpmnlint: bpmnlintConfig,
            },
            container,
            keyboard: {
                bindTo: window,
            },
            exporter: {
                name: 'BPMN Process',
                version: '0.0.1',
            },
            propertiesPanel: {
                parent: '#properties-panel',
            },
            additionalModules: [
                BpmnPropertiesPanelModule,
                BpmnPropertiesProviderModule,
                AddExporter,
                lintModule,
                CamundaPlatformPropertiesProviderModule,
                camundaCustomProvider,
            ],
            moddleExtensions: {
                camunda: camundaModdle,
            },
        });
        modelerRef.current = modeler;

        modeler
            .importXML(xml)
            .then(({ warnings }) => {
                if (warnings.length) {
                    console.info('Warnings', warnings);
                }

                fitView(modelerRef);
                const eventBus = modeler.get('eventBus');
                eventBus.on('root.set', () => {
                    setTimeout(() => fitView(modelerRef), 20);
                });
            })
            .catch((err) => {
                modeler.importXML(DEFAULT_XML).then(() => fitView(modelerRef));
                console.info('error', err);
            });

        return () => {
            modeler.destroy();
        };
    }, [xml]);

    const handleOpenFile = (e) => {
        const input = document.getElementById('upload-bpmn') as HTMLInputElement;
        if (input && input.files.length > 0) {
            const file = input.files[0];
            let reader = new FileReader();
            reader.readAsText(file);
            reader.onloadend = function () {
                const success = openDiagram(modelerRef, reader.result);
                if (!success) {
                    toast.error('Failed to open BPMN file, please double check your configuration file');
                }
            };
        }
    };

    useEffect(() => {
        resetZoom(modelerRef);
        fitView(modelerRef);
    }, []);

    return (
        <Box
            sx={{
                display: 'flex',
                backgroundColor: theme.palette.glide.background.normal.inversePrimary,
                width: '100%',
                minWidth: '300px',
                height: '100%',
                position: 'relative',
            }}
        >
            <Box
                sx={{
                    position: 'absolute',
                    bottom: '0px',
                    zIndex: 1000,
                }}
            >
                <IconButton aria-label="upload file" component="label">
                    <input id="upload-bpmn" hidden accept=".xml,.bpmn" type="file" onChange={handleOpenFile} />
                    <FolderIcon />
                </IconButton>
                <IconButton onClick={() => saveFile(modelerRef, processName)}>
                    <DownloadIcon />
                </IconButton>
                <IconButton onClick={() => saveImage(modelerRef, processName)}>
                    <ImageIcon />
                </IconButton>
            </Box>
            <div
                ref={containerRef}
                className="container"
                style={{
                    width: '100%',
                    overflow: 'auto',
                    height: '100%',
                }}
            />
        </Box>
    );
};

export default BpmnDiagram;
