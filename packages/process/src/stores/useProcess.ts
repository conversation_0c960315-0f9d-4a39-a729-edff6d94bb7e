/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { schemaUrls, processUrls, fetch } from '@tripudiotech/admin-api';

type ProcessAttrs = {
    name: string;
    description?: string;
    title?: string;
    processConfiguration: string;
};

type ProcessSchema = {
    schema: any;
    isLoading: boolean;
    updateLoading: (status: boolean) => void;
    createProcess: (attributes: ProcessAttrs, onSuccess?: (res: any) => void, onFailed?: (err: any) => void) => void;
    updateProcess: (
        id: string,
        attributes: ProcessAttrs,
        onSuccess?: (res: any) => void,
        onFailed?: (err: any) => void
    ) => void;
    deployProcess: (id: string, name: string, onSuccess?: (res: any) => void, onFailed?: (err: any) => void) => void;
    setIsLoading: (status: boolean) => void;
};

const useProcess = create<ProcessSchema>((set, get) => ({
    schema: {
        attributes: [
            {
                id: 'name',
                name: 'name',
                type: 'STRING',
                displayName: 'Name',
                description: 'The name attribute',
                nullable: false,
                constraint: { maxLength: 500 },
            },
            {
                id: 'description',
                name: 'description',
                type: 'TEXT',
                displayName: 'Description',
                description: 'Description attribute',
                nullable: true,
                richText: true,
            },
        ],
    },
    isLoading: false,
    updateLoading: (status: boolean) => {
        set({
            isLoading: status,
        });
    },
    createProcess: async (attributes: any, onSuccess = (res: any) => {}, onFailed = (e: any) => {}) => {
        try {
            set({ isLoading: true });
            const res = await fetch({
                ...processUrls.createProcess,
                data: { attributes },
            });
            onSuccess({ ...res, name: attributes.name });
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
    updateProcess: async (
        id: string,
        attributes: ProcessAttrs,
        onSuccess = (res: any) => {},
        onFailed = (e: any) => {}
    ) => {
        try {
            set({ isLoading: true });
            const res = await fetch({
                ...processUrls.updateProcess,
                params: { entityId: id },
                data: { attributes },
            });
            onSuccess({ ...res, name: attributes.name });
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
    deployProcess: async (id: string, name: string, onSuccess = (res: any) => {}, onFailed = (e: any) => {}) => {
        try {
            set({ isLoading: true });
            const res = await fetch({
                ...processUrls.deployProcess,
                params: { entityId: id },
            });
            onSuccess({ ...res, name });
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
    setIsLoading: (isLoading: boolean) => {
        set({ isLoading });
    },
}));

export default useProcess;
