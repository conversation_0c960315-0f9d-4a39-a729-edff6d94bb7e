/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const saveImage = (modelerRef, fileName?) => {
    modelerRef.current
        .saveSVG({ format: true })
        .then((file) => {
            const svgBlob = new Blob([file.svg], {
                type: 'image/svg+xml',
            });

            downloadFile(fileName, svgBlob, '.svg');
        })
        .catch(() => {
            return false;
        });
    return true;
};

export const saveFile = (modelerRef, fileName?): boolean => {
    modelerRef.current
        .saveXML({ format: true })
        .then((file) => {
            const fileBlob = new Blob([file.xml]);
            downloadFile(fileName, fileBlob);
        })
        .catch((err) => {
            return false;
        });
    return true;
};

export const openDiagram = (modelerRef, bpmnXml): boolean => {
    modelerRef.current
        .importXML(bpmnXml)
        .then(() => {
            // access modeler components
            const canvas = modelerRef.current.get('canvas');
            canvas.zoom('fit-viewport');
        })
        .catch((err) => {
            console.error('could not import BPMN 2.0 diagram', err);
            return false;
        });
    return true;
};

export const fitView = (modelerRef) => {
    if (modelerRef.current) {
        const canvas = modelerRef.current.get('canvas');
        canvas.zoom('fit-viewport', 'auto');
    }
};

export const resetZoom = (modelerRef) => {
    if (modelerRef.current) {
        modelerRef.current.get('zoomScroll').reset();
    }
};

function downloadFile(fileName: any, fileBlob: Blob, ext = '.bpmn20.xml') {
    const name = (fileName || Math.random().toString().substring(7)) + ext;
    let downloadLink = document.createElement('a');
    downloadLink.download = name;
    downloadLink.innerHTML = 'Get BPMN file';
    downloadLink.href = window.URL.createObjectURL(fileBlob);
    downloadLink.onclick = function (event: any) {
        document.body.removeChild(event.target);
    };
    downloadLink.style.visibility = 'hidden';
    document.body.appendChild(downloadLink);
    downloadLink.click();
}
