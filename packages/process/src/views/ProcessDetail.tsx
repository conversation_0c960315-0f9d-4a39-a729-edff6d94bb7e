/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
    Box,
    Button,
    RightTray,
    Stack,
    SchemaFieldSection,
    CollapsablePanel,
    LoadingOverlay,
    <PERSON>rid,
    <PERSON>hemaField,
} from 'ui-style';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { processUrls, fetch } from '@tripudiotech/admin-api';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { notifySuccess } from '@tripudiotech/admin-styleguide';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import ProcessForm from '../components/ProcessForm';
import ProcessNodeDetail from '../components/ProcessNodeDetail';
import BpmnDiagram from '../components/BpmnDiagram';
import { DEFAULT_XML } from '../constants/common';
import useProcess from '../stores/useProcess';
import '../bpmn.css';

const ProcessDetail = () => {
    const navigate = useNavigate();
    const params = useParams();
    const location = useLocation();
    const { schema, isLoading, updateLoading, updateProcess, setIsLoading } = useProcess();
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const formRef = useRef(null);
    const modelerRef = useRef(null);
    const xml = DEFAULT_XML;
    const [processData, setProcessData] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [attrs, setAttrs] = useState([]);

    const emitRefresfEventNRedirect = useCallback(() => {
        // Dispatch a custom event to refresh the data of process
        const refreshProcessListEvent = new CustomEvent('refresh-process', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
        navigate('/process');
    }, [navigate]);

    const onUpdateSuccess = (res) => {
        notifySuccess(
            <span>
                Process <b>{res.name}</b> has been updated successfully
            </span>
        );
        formRef.current.resetForm();
        updateLoading(false);
        emitRefresfEventNRedirect();
    };

    const onUpdateFailed = useCallback(() => {
        formRef.current?.setSubmitting(false);
    }, [formRef]);

    const getProcessDetail = useCallback(async () => {
        setIsLoading(true);
        const { data } = await fetch({
            ...processUrls.getProcessDetail,
            params: {
                entityId: params.id,
            },
        });
        setProcessData(data);
        setIsLoading(false);
    }, [params.id]);

    const handleDelete = useCallback(async () => {
        onOpenDialog(
            'Delete process',
            `Do you really want to delete the process <b>${processData.name}</b>`,
            async () => {
                try {
                    updateLoading(true);
                    await fetch({
                        ...processUrls.deleteProcess,
                        params: { processId: params.id },
                        successMessage: (
                            <span>
                                <b>{processData.name}</b> has been deleted successfully
                            </span>
                        ),
                    });
                    updateLoading(false);
                    emitRefresfEventNRedirect();
                } catch (e) {
                    updateLoading(false);
                }
            },
            'error'
        );
    }, [processData, params]);

    const handleSave = async () => {
        formRef.current.submitForm();
        if (!formRef.current.isValid) return;

        const processConfiguration = await modelerRef.current.saveXML({
            format: true,
        });

        updateLoading(true);
        updateProcess(
            params.id,
            {
                ...formRef.current.values,
                processConfiguration: processConfiguration.xml,
            },
            onUpdateSuccess,
            onUpdateFailed
        );
    };

    useEffect(() => {
        if (!isEmpty(get(location, 'state.data', null))) {
            setProcessData(get(location, 'state.data', null));
            return;
        }
        getProcessDetail();
    }, [params, location]);

    useEffect(() => {
        if (!isEmpty(schema)) {
            const { attributes } = schema;

            setAttrs(attributes);
        }
    }, [schema]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title="Process Detail"
                componentName="process-detail"
                open
                onClose={() => {
                    navigate('/process');
                }}
                fullWidth
                disableCloseOnClickOutside
                hideConfirm
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                    }}
                >
                    <CollapsablePanel>
                        <Box className="general-info--wrapper">
                            <Box
                                className="general-info"
                                sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px' }}
                            >
                                <SchemaFieldSection>General Information</SchemaFieldSection>
                                {!isEditing ? (
                                    <Grid container spacing={2}>
                                        <Grid item xs={12}>
                                            {attrs.map((attr) => (
                                                <SchemaField
                                                    label={attr.displayName}
                                                    value={get(processData, attr.name, '-')}
                                                    key={attr.id}
                                                />
                                            ))}
                                        </Grid>
                                    </Grid>
                                ) : (
                                    <ProcessForm formRef={formRef} processData={processData} />
                                )}
                            </Box>

                            <ProcessNodeDetail />
                        </Box>
                    </CollapsablePanel>

                    <Box
                        sx={{
                            flexGrow: 1,
                            '> .fullscreen': {
                                height: '100%',
                            },
                        }}
                    >
                        <BpmnDiagram
                            modelerRef={modelerRef}
                            xml={processData?.processConfiguration || xml}
                            processName={processData?.name}
                        />
                    </Box>
                </Stack>

                <Box
                    sx={{
                        mt: 'auto',
                        display: 'flex',
                        gap: '8px',
                        justifyContent: 'flex-end',
                        margin: '16px 24px',
                    }}
                >
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={() => {
                            if (isEditing) {
                                setIsEditing(false);
                                return;
                            }

                            navigate('/process');

                            formRef.current?.resetForm();
                        }}
                    >
                        Cancel
                    </Button>
                    {!isEditing && (
                        <span>
                            <Button
                                sx={{
                                    width: '136px',
                                    justifyContent: 'flex-start',
                                }}
                                variant="outlined"
                                onClick={handleDelete}
                                color="error"
                            >
                                Delete
                            </Button>
                        </span>
                    )}
                    <span>
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                                maxWidth: '260px',
                            }}
                            variant="contained"
                            onClick={() => {
                                if (!isEditing) {
                                    setIsEditing(true);
                                    return;
                                }

                                handleSave();
                            }}
                            color="primary"
                        >
                            {isEditing ? 'Save' : 'Edit'}
                        </Button>
                    </span>
                </Box>
            </RightTray>

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default ProcessDetail;
