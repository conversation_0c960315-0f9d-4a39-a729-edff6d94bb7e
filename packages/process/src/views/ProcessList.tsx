/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState, useEffect } from 'react';
import { fetch, getAvatarUrl, processUrls } from '@tripudiotech/admin-api';
import { notifySuc<PERSON> } from '@tripudiotech/admin-styleguide';
import { formatDateTime, buildSortParams, buildQueryBasedOnFilter, DEFAULT_TABLE_PAGINATION_SIZE } from 'ui-common';
import { AgGridReact } from '@ag-grid-community/react';
import { Outlet, useNavigate } from 'react-router-dom';
import {
    AttributeNameRenderer,
    styled,
    tableIcons,
    tableStyles,
    RichtextCellRenderer,
    Loading,
    Box,
    AGGridTablePagination,
    Button,
    UpgradeIcon,
    AccountValueRenderer,
} from 'ui-style';
import get from 'lodash/get';
import useProcess from '../stores/useProcess';

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    height: '100%',
    width: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const ProcessList = () => {
    const gridRef = useRef<AgGridReact>(null);
    const navigate = useNavigate();
    const [totalRows, setTotalRows] = useState<number>(0);
    const { deployProcess, isLoading } = useProcess();

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'serverSide',
            rowSelection: 'single',
            headerHeight: 34,
            rowHeight: 34,
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: AttributeNameRenderer,
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'createdAt',
                    headerName: 'Created At',
                    filter: 'agTextColumnFilter',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'createdAt'));
                    },
                },
                {
                    field: 'createdBy',
                    headerName: 'Created By',
                    filter: 'agTextColumnFilter',
                    cellRenderer: AccountValueRenderer,
                    cellRendererParams: {
                        getAvatarUrl: getAvatarUrl,
                    },
                },
                {
                    field: 'deployedAt',
                    headerName: 'Deployed At',
                    filter: 'agTextColumnFilter',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'deployedAt'));
                    },
                },
                {
                    field: 'deploy',
                    headerName: 'Deploy',
                    filter: false,
                    sortable: false,
                    cellRenderer: (params) => {
                        const { data } = params;
                        return (
                            <Button
                                sx={{
                                    height: 'auto',
                                    padding: '4px 10px',
                                    fontSize: '14px',
                                }}
                                onClick={(e) => {
                                    deployProcess(data.id, data.name, onDeploySuccess);
                                }}
                            >
                                Deploy <UpgradeIcon sx={{ width: '16px', height: '16px', ml: 1 }} />
                            </Button>
                        );
                    },
                    cellStyle: {
                        padding: '0 8px',
                    },
                },
            ],
            icons: tableIcons,
            loadingOverlayComponent: Loading,

            // Pagination options
            pagination: true,
            suppressPaginationPanel: true,
            serverSideInfiniteScroll: true,
            cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
            paginationPageSize: DEFAULT_TABLE_PAGINATION_SIZE,
        };
    }, [gridRef]);

    const onDeploySuccess = (res) => {
        notifySuccess(
            <span>
                Process <b>{res.name}</b> has been deployed successfully
            </span>
        );
        gridRef.current?.api?.refreshServerSide();
    };

    const handleSetDataSource = () => {
        const buildParams = (params) => {
            const { sortModel, startRow, filterModel } = params.request;

            const queryParams = {
                offset: startRow || 0,
                limit: params.api.paginationGetPageSize(),
                ...buildSortParams(sortModel),
            };

            if (filterModel && Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            }
            return queryParams;
        };

        const dataSource = {
            getRows: (params) => {
                params.api.showLoadingOverlay();
                fetch({
                    ...processUrls.getProcesses,
                    qs: buildParams(params),
                })
                    .then((response) => {
                        if (response.status != 200) {
                            params.failCallback();
                            return;
                        }
                        const totalCount = response.data.pageInfo.total;
                        const rowsThisPage = response.data.data;

                        params.successCallback(rowsThisPage, totalCount);
                        setTotalRows(totalCount);
                    })
                    .catch((e) => {
                        // console.log(e)
                    })
                    .finally(() => {
                        gridRef.current?.api.hideOverlay();
                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };

        return dataSource;
    };

    const onGridReady = useCallback((e) => {
        const datasource = handleSetDataSource();
        e.api.setServerSideDatasource(datasource);
    }, []);

    useEffect(() => {
        // Listen the custom event fighting after process created successefull
        window.addEventListener('refresh-process', (e) => {
            gridRef.current?.api?.refreshServerSide();
        });
    }, [gridRef]);

    const onCellClicked = useCallback((params) => {
        // Suppress click events on action columns
        if (!['deploy'].includes(params.column.colId)) {
            const { data } = params;
            navigate(`/process/${data.id}`, { state: { data } });
        }
    }, []);

    return (
        <Box
            sx={{
                height: '100%',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <ContentWrapper>
                <Box className="ag-theme-alpine" sx={{ height: '100%' }}>
                    <AgGridReact
                        ref={gridRef}
                        {...gridOptions}
                        onGridReady={onGridReady}
                        onCellClicked={onCellClicked}
                    />
                </Box>
            </ContentWrapper>
            <AGGridTablePagination gridRef={gridRef} totalRows={totalRows} />

            <Outlet />
        </Box>
    );
};

export default ProcessList;
