const { merge } = require('webpack-merge');
const webpack = require('webpack');
const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const dotenv = require('dotenv');
dotenv.config();

module.exports = (webpackConfigEnv, argv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: 'tripudiotech',
        projectName: 'process',
        webpackConfigEnv,
        argv,
    });

    return merge(defaultConfig, {
        devtool: false,
        plugins: [
            new webpack.DefinePlugin({
                'process.env': JSON.stringify(process.env),
            }),
        ],
        module: {
            rules: [
                {
                    test: /\.(scss|sass)$/,
                    use: [
                        {
                            loader: 'style-loader',
                        },
                        {
                            loader: 'css-loader',
                        },
                        {
                            loader: 'sass-loader',
                        },
                    ],
                },
                {
                    test: /\.(woff(2)?|ttf|eot|svg)(\?v=\d+\.\d+\.\d+)?$/,
                    use: [
                        {
                            loader: 'file-loader',
                            options: {
                                name: '[name].[ext]',
                                outputPath: 'fonts/',
                            },
                        },
                    ],
                },
                {
                    test: /\.m?js$/,
                    exclude: /node_modules/,
                    use: {
                        loader: 'babel-loader',
                        options: {
                            plugins: [
                                [
                                    '@babel/plugin-transform-react-jsx',
                                    {
                                        importSource: '@bpmn-io/properties-panel/preact',
                                        runtime: 'automatic',
                                    },
                                ],
                            ],
                        },
                    },
                },
                {
                    test: /\.bpmnlintrc$/i,
                    use: [
                        {
                            loader: 'bpmnlint-loader',
                        },
                    ],
                },
                {
                    test: /.m?js/,
                    resolve: {
                        fullySpecified: false,
                    },
                },
            ],
        },
        // modify the webpack config however you'd like to by adding to this object
    });
};
