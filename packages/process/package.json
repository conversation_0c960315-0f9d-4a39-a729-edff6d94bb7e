{"name": "@tripudiotech/admin-process", "version": "1.0.0", "scripts": {"precommit": "lint-staged", "start": "webpack serve --port 9013", "start:standalone": "webpack serve --env standalone", "build": "concurrently npm:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "test": "cross-env BABEL_ENV=test jest", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage"}, "devDependencies": {"typescript": "5.7.3", "@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-react-jsx": "^7.21.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@types/testing-library__jest-dom": "^5.14.1", "babel-jest": "^27.0.6", "bpmnlint": "^8.1.1", "bpmnlint-loader": "^0.1.6", "bpmnlint-plugin-camunda": "^0.6.0", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.30.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "ts-config-single-spa": "^3.0.0", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@bpmn-io/add-exporter": "^0.2.0", "@bpmn-io/properties-panel": "^2.2", "@types/jest": "^27.0.1", "@types/react": "^17.0.19", "@types/react-dom": "^17.0.9", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "bpmn-js": "^11.5.0", "bpmn-js-bpmnlint": "^0.20.1", "bpmn-js-properties-panel": "^1.18.0", "camunda-bpmn-moddle": "^7.0.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-full-screen": "^1.1.1", "react-quill": "^2.0.0", "react-toastify": "^9.0.8", "single-spa": "^5.9.3", "ui-common": "npm:@tripudiotech/ui-common@^1.2.2", "ui-style": "npm:@tripudiotech/ui-style@^1.3.3", "single-spa-react": "^4.3.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --no-ignore --fix", "git add --force"], "*.{json,md,js,jsx,ts,tsx}": ["prettier --write", "git add --force"]}, "types": "dist/tripudiotech-process.d.ts"}