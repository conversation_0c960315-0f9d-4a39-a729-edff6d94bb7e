{"extends": ["ts-react-important-stuff", "plugin:prettier/recommended", "plugin:react/recommended"], "parser": "@babel/eslint-parser", "plugins": ["eslint-plugin-react", "eslint-plugin-import"], "rules": {"import/no-unresolved": 0, "react/react-in-jsx-scope": "off", "react/prop-types": "off", "import/no-duplicates": "error", "react-hooks/exhaustive-deps": "off", "react/no-unescaped-entities": "off", "jsx-a11y/no-static-element-interactions": "off", "react/no-children-prop": "off", "react-hooks/rules-of-hooks": "off"}}