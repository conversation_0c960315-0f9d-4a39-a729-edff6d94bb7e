.process-variables:
  variables:
    PACKAGE_DIR: packages/process

aws-stag-process-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .process-variables

aws-stag-process-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .process-variables
  needs:
    - aws-stag-process-package

gcp-stag-process-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .process-variables

gcp-stag-process-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .process-variables
  needs:
    - gcp-stag-process-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-process-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .process-variables

gcp-uat-process-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .process-variables
  needs:
    - gcp-uat-process-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json