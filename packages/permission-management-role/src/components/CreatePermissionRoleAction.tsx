/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef } from 'react';
import { Box, Button, RightTray, SchemaFieldSection, LoadingOverlay } from 'ui-style';
import { useToggle } from 'ui-common';
import { notifySuccess } from '@tripudiotech/admin-styleguide';
import PermissionRoleForm from './PermissionRoleForm';
import permissionRoleStore from '../store/permissionRoleStore';

const CreatePermissionRoleAction = () => {
    const [open, openToggle] = useToggle(false);
    const { createRole, isLoading } = permissionRoleStore();
    const formRef = useRef(null);

    const onCreateSuccess = (res: any) => {
        notifySuccess(
            <span>
                Role <b>{res.name}</b> has been created successfully
            </span>
        );
        formRef.current.resetForm();
        openToggle.close();

        // Dispatch a custom event to refresh the data of process
        const refreshProcessListEvent = new CustomEvent('refresh-role-table', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
    };

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <Button variant="contained" color="secondary" size="small" onClick={openToggle.open}>
                Create Role
            </Button>

            <RightTray
                title="Create Role"
                componentName="create-permission-role"
                open={open}
                onClose={openToggle.close}
                onConfirm={async () => {
                    formRef.current.submitForm();
                    if (!formRef.current.isValid) return;

                    createRole(formRef.current.values, onCreateSuccess);
                }}
                confirmText="Create"
                disableCloseOnClickOutside
                PaperProps={{
                    sx: {
                        width: 450,
                    },
                }}
            >
                <Box className="general-info--wrapper">
                    <Box
                        className="general-info"
                        sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px' }}
                    >
                        <SchemaFieldSection>General Information</SchemaFieldSection>
                        <PermissionRoleForm formRef={formRef} />
                    </Box>
                </Box>
            </RightTray>

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default CreatePermissionRoleAction;
