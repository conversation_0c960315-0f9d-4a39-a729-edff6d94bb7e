/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useEffect } from 'react';
import * as yup from 'yup';
import { formHelper } from 'ui-common';
import { Formik, Field } from 'formik';
import { Box, FormikTextField } from 'ui-style';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';

type ROLE = {
    name: string;
    description: string;
};

const PermissionRoleForm = ({ formRef, roleData }: { formRef: any; roleData?: ROLE }) => {
    const [initialValues, setInitialValues] = useState<ROLE>({
        name: '',
        description: '',
    });
    const validationSchema = yup.object({
        name: yup.string().required(formHelper.buildRequiredMessage('Name')),
        description: yup.string().required(formHelper.buildRequiredMessage('Description')),
    });

    useEffect(() => {
        if (!isEmpty(roleData)) {
            setInitialValues({
                name: get(roleData, 'name', ''),
                description: get(roleData, 'description', ''),
            });
        }
    }, [roleData]);

    return (
        <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={(values, { setSubmitting }) => {}}
            innerRef={formRef}
        >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <Field fullWidth component={FormikTextField} label="Name" name="name" variant="outlined" required />
                <Field
                    fullWidth
                    multiline
                    minRows={2}
                    component={FormikTextField}
                    label="Description"
                    name="description"
                    variant="outlined"
                    required
                />
            </Box>
        </Formik>
    );
};

export default PermissionRoleForm;
