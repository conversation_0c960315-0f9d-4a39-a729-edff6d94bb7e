/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, schemaUrls } from '@tripudiotech/admin-api';

type RoleAttrs = {
    name: string;
    description: string;
};

type PermissionRoleStoreType = {
    isLoading: boolean;
    updateLoading: (status: boolean) => void;
    createRole: (attributes: RoleAttrs, onSuccess?: (res: any) => void, onFailed?: (err: any) => void) => void;
    updateRole: (
        roleName: string,
        attributes: RoleAttrs,
        onSuccess?: (res: any) => void,
        onFailed?: (err: any) => void
    ) => void;
};

const permissionRoleStore = create<PermissionRoleStoreType>((set, get) => ({
    isLoading: false,
    updateLoading: (isLoading: boolean) => {
        set({
            isLoading,
        });
    },
    createRole: async (data: any, onSuccess = (res: any) => {}, onFailed = (e: any) => {}) => {
        try {
            set({ isLoading: true });
            const res = await fetch({
                ...schemaUrls.createPermissionRole,
                data,
            });
            onSuccess({ ...res, name: data.name });
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
    updateRole: async (roleName: string, data: any, onSuccess = (res: any) => {}, onFailed = (e: any) => {}) => {
        try {
            set({ isLoading: true });
            const res = await fetch({
                ...schemaUrls.updatePermissionRole,
                params: {
                    roleName,
                },
                data,
            });
            onSuccess({ ...res, name: data.name });
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
}));

export default permissionRoleStore;
