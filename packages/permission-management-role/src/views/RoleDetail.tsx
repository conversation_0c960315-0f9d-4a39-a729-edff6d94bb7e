/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Box, Button, RightTray, Stack, SchemaFieldSection, LoadingOverlay, Grid, SchemaField } from 'ui-style';
import { useNavigate, useParams, useLocation, Outlet } from 'react-router-dom';
import { schemaUrls, fetch } from '@tripudiotech/admin-api';
import { notifySuccess } from '@tripudiotech/admin-styleguide';
import { useDialog } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import permissionRoleStore from '../store/permissionRoleStore';
import PermissionRoleForm from '../components/PermissionRoleForm';

const RoleDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const formRef = useRef(null);
    const { isLoading, updateRole, updateLoading } = permissionRoleStore();

    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const [roleData, setRoleData] = useState(null);
    const [isEditing, setIsEditing] = useState(false);

    const emitRefresfEventNRedirect = useCallback(() => {
        // Dispatch a custom event to refresh the data of rule
        const refreshProcessListEvent = new CustomEvent('refresh-role-table', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
        navigate('/permission-role');
    }, [navigate]);

    const handleDelete = () => {
        onOpenDialog(
            'Delete Role',
            `Do you want to delete Role <b>${roleData.name}</b>`,
            async () => {
                try {
                    updateLoading(true);
                    await fetch({
                        ...schemaUrls.deletePermissionRole,
                        params: {
                            roleName: roleData.name,
                        },
                        successMessage: (
                            <span>
                                <b>{roleData.name}</b> has been deleted successfully
                            </span>
                        ),
                    });
                    updateLoading(false);
                    emitRefresfEventNRedirect();
                } catch (e) {
                    updateLoading(false);
                }
            },
            'error'
        );
    };

    const getRoleDetail = useCallback(async () => {
        try {
            updateLoading(true);
            const { data } = await fetch({
                ...schemaUrls.getPermissionRoleByName,
                params: {
                    roleName: params.name,
                },
            });

            setRoleData(data);
        } catch (e) {
            console.info(e);
        } finally {
            updateLoading(false);
        }
    }, [params]);

    const handleSave = async () => {
        updateRole(params.name, formRef.current?.values, () => {
            notifySuccess(<span>Role has been updated successfully</span>);
            formRef.current?.resetForm();
            setIsEditing(false);
            emitRefresfEventNRedirect();
        });
    };

    useEffect(() => {
        if (!isEmpty(get(location.state, 'data', null))) {
            setRoleData(get(location.state, 'data', null));
            return;
        }

        getRoleDetail();
    }, [location.state]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title="Role Detail"
                componentName="permission-role-detail"
                open
                onClose={() => {
                    navigate('/permission-role');
                }}
                PaperProps={{
                    sx: {
                        width: 450,
                    },
                }}
                disableCloseOnClickOutside
                hideConfirm
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                    }}
                >
                    <Box
                        className="general-info--wrapper"
                        sx={{
                            width: '100%',
                        }}
                    >
                        <Box
                            className="general-info"
                            sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px' }}
                        >
                            <SchemaFieldSection>General Information</SchemaFieldSection>

                            <Grid container spacing={2}>
                                <Grid
                                    item
                                    xs={12}
                                    sx={{
                                        '> .MuiGrid-root': {
                                            '&:nth-of-type(2)': {
                                                'div:nth-of-type(2)': {
                                                    span: {
                                                        lineHeight: '18px',
                                                    },
                                                },
                                            },
                                        },
                                    }}
                                >
                                    {isEditing ? (
                                        <PermissionRoleForm formRef={formRef} roleData={roleData} />
                                    ) : (
                                        <>
                                            <SchemaField label="Name" value={get(roleData, 'name', '')} />
                                            <SchemaField label="Description" value={get(roleData, 'description', '')} />
                                        </>
                                    )}
                                </Grid>
                            </Grid>
                        </Box>
                    </Box>
                </Stack>

                <Box
                    sx={{
                        mt: 'auto',
                        display: 'flex',
                        gap: '8px',
                        justifyContent: 'flex-end',
                        margin: '16px 24px',
                    }}
                >
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={() => {
                            if (isEditing) {
                                setIsEditing(false);
                                return;
                            }
                            navigate('/permission-role');
                        }}
                    >
                        Cancel
                    </Button>
                    {!isEditing && (
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                            }}
                            variant="outlined"
                            onClick={handleDelete}
                            color="error"
                        >
                            Delete
                        </Button>
                    )}
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        onClick={() => {
                            if (!isEditing) {
                                setIsEditing(true);
                                return;
                            }

                            handleSave();
                        }}
                        color="primary"
                    >
                        {isEditing ? 'Save' : 'Edit'}
                    </Button>
                </Box>
            </RightTray>

            <Outlet />

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default RoleDetail;
