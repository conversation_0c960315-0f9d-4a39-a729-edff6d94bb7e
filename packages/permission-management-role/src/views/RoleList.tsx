/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import { Box, styled, tableIcons, tableStyles, Loading, AGGridTablePagination } from 'ui-style';
import { buildSortParams, DEFAULT_TABLE_PAGINATION_SIZE, buildQueryBasedOnFilter, formatDateTime } from 'ui-common';
import { schemaUrls, fetch } from '@tripudiotech/admin-api';
import { AgGridReact } from '@ag-grid-community/react';
import get from 'lodash/get';
import { Outlet, useNavigate } from 'react-router-dom';

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    height: '100%',
    width: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const RoleList = () => {
    const navigate = useNavigate();
    const [totalRows, setTotalRows] = useState<number>(1);
    const gridRef = useRef<AgGridReact>(null);

    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Role Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'createdAt',
                    headerName: 'Created At',
                    filter: 'agTextColumnFilter',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'createdAt'));
                    },
                },
                {
                    field: 'updatedAt',
                    headerName: 'Updated At',
                    filter: 'agTextColumnFilter',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                },
            ],
            icons: tableIcons,
            cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            suppressRowClickSelection: true,
            suppressPaginationPanel: true,
            pagination: true,
        };
    }, [gridRef]);

    const createServerSideDataSource = useCallback((event) => {
        const buildParams = (params) => {
            const filterModel = params.filterModel;
            let queryParams = {
                offset: params.startRow || 0,
                limit: event?.api.paginationGetPageSize(),
                ...buildSortParams(params.sortModel),
                eventType: get(filterModel, ['eventType', 'values'], []),
            };
            if (filterModel && Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            }
            return queryParams;
        };
        return {
            getRows: (params) => {
                gridRef.current?.api.showLoadingOverlay();
                fetch({
                    ...schemaUrls.getPermissionRoles,
                    qs: buildParams(params?.request),
                })
                    .then((response) => {
                        if (response.status !== 200) {
                            params.failCallback();
                            return;
                        }
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                        setTotalRows(rowCount);
                    })
                    .finally(() => {
                        gridRef.current?.api.hideOverlay();
                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);

    const handleSetDataSource = useCallback((event) => {
        const dataSource = createServerSideDataSource(event);
        event?.api.setServerSideDatasource(dataSource);
    }, []);

    useEffect(() => {
        // Listen the custom event fighting after new rule created successefull
        window.addEventListener('refresh-role-table', (e) => {
            gridRef.current?.api?.refreshServerSide();
        });
    }, [gridRef]);

    return (
        <Box
            sx={{
                height: '100%',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <ContentWrapper>
                <Box className="ag-theme-alpine" sx={{ height: '100%' }}>
                    <AgGridReact
                        ref={gridRef}
                        {...gridOptions}
                        onGridReady={handleSetDataSource}
                        onRowClicked={(params) => {
                            const { data } = params;
                            navigate(`/permission-role/${data.name}`, { state: { data } });
                        }}
                    />
                </Box>
            </ContentWrapper>
            <AGGridTablePagination gridRef={gridRef} totalRows={totalRows} />

            <Outlet />
        </Box>
    );
};

export default RoleList;
