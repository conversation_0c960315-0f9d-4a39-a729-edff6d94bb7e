.permission-management-role-variables:
  variables:
    PACKAGE_DIR: packages/permission-management-role

aws-stag-permission-management-role-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .permission-management-role-variables

aws-stag-permission-management-role-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .permission-management-role-variables
  needs:
    - aws-stag-permission-management-role-package

gcp-stag-permission-management-role-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .permission-management-role-variables

gcp-stag-permission-management-role-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .permission-management-role-variables
  needs:
    - gcp-stag-permission-management-role-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-permission-management-role-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .permission-management-role-variables

gcp-uat-permission-management-role-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .permission-management-role-variables
  needs:
    - gcp-uat-permission-management-role-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json