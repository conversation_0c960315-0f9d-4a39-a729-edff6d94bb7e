/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import {
    fetch,
    userUrls,
    entityUrls,
    SYSTEM_RELATION,
    SYSTEM_ENTITY_TYPE,
    DEFAULT_CLIENT_SIDE_LIMIT,
} from '@tripudiotech/admin-api';
import eq from 'lodash/eq';

export const DEFAULT_USER_ROLE = 'user';

export interface LocalStore {
    userInfo: any;
    assignedRoles: any[];
    assignedTeams: any[];
    assignedDepartments: any[];
    userId: string;
    authServerId: string;
    loading: Boolean;
    getUserInfo: (userId: string, force?) => Promise<Boolean>;
    deactivateUser: () => Promise<Boolean>;
    activateUser: () => Promise<Boolean>;
    updateUser: (value: any, roles, departments, teams) => Promise<Boolean>;
    assignRoles: (roleNames: string[]) => Promise<Boolean>;
    cleanUserInfo: () => void;
    setLoading: (loading: Boolean) => void;
}

const transformRelation = (relations, type) => {
    return relations.filter((rel) => rel.relation.type === type).map((rel) => rel.relation);
};

export const handleTeamOrDepChanged = (assignedValues, newValues, userEntityId) => {
    let requests = [];
    const removedList = assignedValues.filter(
        (assignedValue) => !newValues.some((newValue) => newValue.id === assignedValue.id)
    );
    const addedList = newValues.filter(
        (newValue) => !assignedValues.some((assignedValue) => newValue.id === assignedValue.id)
    );

    removedList.forEach((team) => {
        requests.push(
            fetch({
                ...entityUrls.deleteRelation,
                params: {
                    fromEntityId: userEntityId,
                    relationType: SYSTEM_RELATION.WORKS_FOR,
                    toEntityId: team.id,
                },
            })
        );
    });
    addedList.forEach((team) => {
        requests.push(
            fetch({
                ...entityUrls.createRelation,
                params: {
                    fromEntityId: userEntityId,
                    relationType: SYSTEM_RELATION.WORKS_FOR,
                    toEntityId: team.id,
                },
            })
        );
    });
    return requests;
};

const useLocalStore = create<LocalStore>((set, get) => ({
    userInfo: null,
    assignedRoles: [],
    assignedDepartments: [],
    assignedTeams: [],
    userId: '',
    authServerId: '',
    loading: false,
    cleanUserInfo: () => {
        set({
            userId: '',
            userInfo: null,
            assignedRoles: [],
            assignedDepartments: [],
            assignedTeams: [],
        });
    },
    getUserInfo: async (userId: string, force = false) => {
        if (get().userInfo && !force) return;
        try {
            set({ loading: true });

            const [userInfoRs, worksForRelationRes] = await Promise.all([
                fetch({
                    ...userUrls.getUser,
                    params: { userId: userId },
                }),
                fetch({
                    ...entityUrls.getAllRelationsUnderEntity,
                    params: {
                        // Entity ID
                        entityId: userId,
                    },
                    qs: {
                        relationNames: [SYSTEM_RELATION.WORKS_FOR],
                        type: [SYSTEM_ENTITY_TYPE.TEAM, SYSTEM_ENTITY_TYPE.DEPARTMENT],
                        limit: DEFAULT_CLIENT_SIDE_LIMIT,
                    },
                }),
            ]);
            const roleRs = await fetch({
                ...userUrls.getAssignRole,
                params: { userId: userInfoRs.data?.authServerId },
                qs: { limit: DEFAULT_CLIENT_SIDE_LIMIT },
            });

            set({
                userInfo: userInfoRs.data,
                assignedRoles: roleRs.data?.filter((role) => role.name !== DEFAULT_USER_ROLE),
                userId,
                authServerId: userInfoRs.data?.authServerId,
                assignedTeams: transformRelation(worksForRelationRes.data.data, SYSTEM_ENTITY_TYPE.TEAM),
                assignedDepartments: transformRelation(worksForRelationRes.data.data, SYSTEM_ENTITY_TYPE.DEPARTMENT),
            });

            return true;
        } catch (err) {
            console.error(err);
            return false;
        } finally {
            set({ loading: false });
        }
    },
    deactivateUser: async () => {
        try {
            await fetch({
                ...userUrls.deactivateUser,
                params: { userId: get().authServerId },
                successMessage: `Successfully deactivate user`,
            });
            await get().getUserInfo(get().userId);
            return true;
        } catch (err) {
            console.error(err);
            return false;
        }
    },

    activateUser: async () => {
        try {
            await fetch({
                ...userUrls.activateUser,
                params: { userId: get().authServerId },
                successMessage: `Successfully activate user`,
            });

            await get().getUserInfo(get().userId);
            return true;
        } catch (err) {
            console.error(err);
            return false;
        }
    },
    updateUser: async (value: any, roles, departments, teams) => {
        try {
            set({ loading: true });
            const request = prepareRequest(value);
            const shouldUpdateRoles = !eq(roles, get().assignedRoles);

            let requests: Promise<any>[] = [
                fetch({
                    ...userUrls.updateUser,
                    params: { userId: get().authServerId },
                    data: request,
                }),
            ];
            if (shouldUpdateRoles) {
                if (roles?.length > 0) {
                    requests.push(get().assignRoles(roles.map((role) => role.name)));
                } else {
                    // backend throws error if request is empty. So passing in the default 'user'
                    requests.push(get().assignRoles([DEFAULT_USER_ROLE]));
                }
            }
            const { assignedTeams, assignedDepartments } = get();
            const userEntityId = get().userId;
            requests.concat(handleTeamOrDepChanged(assignedTeams, teams, userEntityId));
            requests.concat(handleTeamOrDepChanged(assignedDepartments, departments, userEntityId));
            await Promise.all(requests);
            return true;
        } catch (error) {
            console.error(error);
        } finally {
            set({ loading: false });
        }
    },
    assignRoles: async (roleNames: string[]) => {
        try {
            await Promise.all(
                get()
                    .assignedRoles.filter((role) => !roleNames.includes(role.name))
                    .map((r) => {
                        return fetch({
                            ...userUrls.unassignRole,
                            params: { userId: get().authServerId, roleName: r.name },
                        });
                    })
            );
            await fetch({
                ...userUrls.assignRole,
                params: { userId: get().authServerId },
                data: roleNames,
            });
            return true;
        } catch (e) {
            return false;
        }
    },
    setLoading: (loading) => set({ loading }),
}));

export default useLocalStore;

function prepareRequest(data) {
    const cloneObj = Object.assign({}, data);
    delete cloneObj['firstName'];
    delete cloneObj['lastName'];
    delete cloneObj['companyId'];
    delete cloneObj['newCompanyId'];
    return {
        firstName: data.firstName,
        lastName: data.lastName,
        description: data.description,
        newCompanyId: data.newCompanyId,
        properties: cloneObj,
    };
}
