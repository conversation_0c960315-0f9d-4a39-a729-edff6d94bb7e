/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Button, PlusIcon, RightTray } from 'ui-style';
import { useToggle } from 'ui-common';
import { useCallback, useRef } from 'react';
import UserForm from '../UserForm';
import { fetch, userUrls } from '@tripudiotech/admin-api';
import get from 'lodash/get';
import useLocalStore, { DEFAULT_USER_ROLE, handleTeamOrDepChanged } from '../../store/useLocalStore';
function prepareRequest(data, roles) {
    const cloneObj = Object.assign({}, data);
    delete cloneObj['companyId'];
    return {
        firstName: data.firstName,
        lastName: data.lastName,
        username: data.email,
        description: data.description,
        properties: cloneObj,
        roles: roles?.length > 0 ? roles.map((r) => r.name) : [DEFAULT_USER_ROLE],
    };
}

const CreateNewUserActions = ({ disabled = false, onRefresh }) => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);
    const [setLoading] = useLocalStore((state) => [state.setLoading]);
    const handleSubmit = useCallback(async (values, roles, departments, teams, onError) => {
        try {
            setLoading(true);
            const res = await fetch({
                ...userUrls.createUser,
                params: { companyId: values.companyId },
                data: prepareRequest(values, roles),
                successMessage: (
                    <span>
                        User <b>{values.firstName}</b> has been created successfully. An email has been sent to the user
                        to finish the registration.
                    </span>
                ),
            });
            const userEntityId = get(res, 'data.id');
            let reqs = [];
            if (roles.length > 0) {
                reqs.push(
                    fetch({
                        ...userUrls.assignRole,
                        params: { userId: userEntityId },
                        data: roles.map((r) => r.name),
                    })
                );
            }
            reqs.concat(handleTeamOrDepChanged([], departments, userEntityId));
            reqs.concat(handleTeamOrDepChanged([], teams, userEntityId));
            await Promise.all(reqs);
            onRefresh();
            openToggle.close();
        } catch (error) {
            console.error(error);
            onError && onError(error.response);
        } finally {
            setLoading(false);
        }
    }, []);

    return (
        <>
            <Button
                variant="contained"
                color="secondary"
                onClick={openToggle.open}
                size="small"
                endIcon={<PlusIcon />}
                disabled={disabled}
            >
                Create New User
            </Button>
            <RightTray
                title={`Create new user`}
                componentName={'create-new-user'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={() => formRef.current.submitForm()}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <UserForm formRef={formRef} handleSubmit={handleSubmit} isCreateNew />
                </Box>
            </RightTray>
        </>
    );
};

export default CreateNewUserActions;
