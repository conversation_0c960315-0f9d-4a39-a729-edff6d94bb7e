/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { AttributeSchema, formatDate } from 'ui-common';
import { Chip, Grid, SchemaField, SchemaFieldSection } from 'ui-style';
import get from 'lodash/get';
import useLocalStore from '../store/useLocalStore';
import { SchemaWithLifeCycleDetail, useOrg } from '@tripudiotech/admin-caching-store';
import { filterAndSortAttributesForRead, UserInfo } from '@tripudiotech/admin-api';

const UserInformation = ({
    userInfo,
    personSchema,
}: {
    userInfo: UserInfo;
    personSchema: SchemaWithLifeCycleDetail;
}) => {
    const { assignedRoles, assignedDepartments, assignedTeams } = useLocalStore();
    const companies = useOrg((state) => state.companiesMap);
    const stateName = get(userInfo, ['state', 'name'], '');

    return personSchema && userInfo ? (
        <>
            <SchemaFieldSection>General Information</SchemaFieldSection>
            <Grid container spacing={2}>
                <Grid item xs={12}>
                    <SchemaField label="Company" value={companies?.[userInfo?.companyId]?.properties?.name} />
                    <SchemaField label="Department" value={assignedDepartments.map((r) => r.name).join(', ') || '-'} />
                    <SchemaField label="Team" value={assignedTeams.map((r) => r.name).join(', ') || '-'} />
                    <DynamicAttributes
                        attributes={personSchema?.attributes}
                        userInfo={userInfo}
                        attributeOrder={personSchema?.entityType?.attributeOrder}
                    />
                    <SchemaField label="Role" value={assignedRoles.map((r) => r.name).join(', ') || '-'} />
                    <SchemaField
                        label="Status"
                        value={
                            <Chip
                                size="small"
                                label={stateName.toUpperCase()}
                                variant={stateName === 'Active' ? 'status-success' : 'status-error'}
                            />
                        }
                    />
                    <SchemaField label="Created At" value={formatDate(get(userInfo, 'createdAt'))} />
                </Grid>
            </Grid>
        </>
    ) : null;
};

const DynamicAttributes = ({
    attributes,
    userInfo,
    attributeOrder,
}: {
    attributes: Record<string, AttributeSchema>;
    userInfo: UserInfo;
    attributeOrder: string[];
}) => {
    if (!attributes || !userInfo) {
        return null;
    }
    return (
        <>
            {filterAndSortAttributesForRead(Object.values(attributes), attributeOrder).map((att) => {
                if (!att.visible) return;
                return (
                    <SchemaField
                        key={att.name}
                        label={att.displayName}
                        value={get(userInfo, ['properties', att.name]) || '-'}
                        breakline={att.richText}
                    />
                );
            })}
        </>
    );
};

export default UserInformation;
