/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useRef, useCallback, useState, useEffect } from 'react';
import {
    Box,
    ContentHeader,
    FixedHeightContainer,
    styled,
    AnimatedPage,
    tableIcons,
    tableStyles,
    Loading,
    AGGridTablePagination,
    Checkbox,
    Chip,
    LoadingOverlay,
    NoRowsOverlay,
} from 'ui-style';

import { DEFAULT_TABLE_PAGINATION_SIZE } from 'ui-common';

import { userUrls, fetch, User } from '@tripudiotech/admin-api';
import { formatDateTime } from '@tripudiotech/admin-styleguide';
import { AgGridReact } from '@ag-grid-community/react';
import { type GridOptions } from '@ag-grid-community/core';
import get from 'lodash/get';
import { Link, Outlet, useOutletContext } from 'react-router-dom';
import CreateNewUserActions from '../components/actions/CreateNewUser';
import useLocalStore from '../store/useLocalStore';
import { useOrg } from '@tripudiotech/admin-caching-store';

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    width: '100%',
}));

const GRID_OPTIONS: GridOptions = {
    headerHeight: 34,
    rowHeight: 34,
    loadingOverlayComponent: Loading,
    animateRows: true,
    defaultColDef: {
        sortable: true,
        resizable: true,
        flex: 1,
        filter: true,
        floatingFilter: false,
        cellStyle: () => ({
            display: 'block',
        }),
    },
    getRowId: (params) => {
        return params.data.id;
    },
    columnDefs: [
        {
            field: 'name',
            headerName: 'Full Name',
            filter: 'agTextColumnFilter',
            valueGetter: (params) => {
                return [params.data?.firstName, params.data?.lastName].join(' ');
            },
            cellRenderer: (props) => {
                const id = props.data?.attributes?.referenceId?.[0];
                return <Link to={`/user/${id}`}>{props.value}</Link>;
            },
        },
        {
            field: 'email',
            headerName: 'Email',
            filter: 'agTextColumnFilter',
        },
        {
            field: 'enabled',
            headerName: 'Status',
            cellRenderer: function (params) {
                return (
                    <Chip
                        size="small"
                        label={params.value ? 'ACTIVE' : 'INACTIVE'}
                        variant={params.value ? 'status-success' : 'status-error'}
                    />
                );
            },
        },
        {
            field: 'isEmailVerified',
            headerName: 'Email Verified',
            cellRenderer: function (params) {
                return (
                    <Checkbox sx={{ m: 0, p: 0, mb: '4px' }} checked={get(params, 'data.isEmailVerified')} disabled />
                );
            },
        },
        {
            field: 'createdTimestamp',
            headerName: 'Created At',
            valueGetter: (params) => {
                return formatDateTime(get(params, 'data.createdTimestamp'));
            },
        },
    ],
    icons: tableIcons,
    cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
    rowModelType: 'clientSide',
    serverSideInfiniteScroll: true,
    suppressRowClickSelection: true,
    pagination: true,
};

const MainLayout = () => {
    const gridRef = useRef<AgGridReact>(null);
    const { loading, setLoading } = useLocalStore();
    const getAllOrg = useOrg((state) => state.getAll);
    const onCellClicked = useCallback((e) => {
        // Suppress click events on specific columns
        if (!['identifier'].includes(e.column.colId)) {
            e.node.setSelected(true);
        }
    }, []);
    const [users, setUsers] = useState<User[]>(null);
    const [error, setError] = useState(false);

    useEffect(() => {
        getAllOrg();
    }, []);

    const fetchUsers = useCallback(() => {
        setLoading(true);
        fetch({
            ...userUrls.getUsers,
            qs: {
                limit: 5000, // TODO: when backend supports sorting, filtering change to SSRM
                offset: -1,
            },
        })
            .then((response) => {
                if (response.status !== 200) {
                    setError(true);
                    return;
                }
                setUsers(response.data?.data?.[0]);
            })
            .catch((e) => {
                console.error('Error fetching users: ' + e);
                setError(true);
                setUsers([]);
            })
            .finally(() => {
                setLoading(false);
            });
    }, []);

    const UserActions = () => {
        return (
            <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
                <CreateNewUserActions onRefresh={fetchUsers} />
            </Box>
        );
    };

    return (
        <AnimatedPage>
            {loading && <LoadingOverlay />}
            <FixedHeightContainer>
                <ContentHeader title="User" actionsRenderer={UserActions} />
                <ContentWrapper sx={{ ...tableStyles, '& .ag-paging-panel': { display: 'none' } }}>
                    <div className="ag-theme-alpine" style={{ width: '100%' }}>
                        <AgGridReact
                            ref={gridRef}
                            {...GRID_OPTIONS}
                            onGridReady={fetchUsers}
                            rowData={users}
                            onCellClicked={onCellClicked}
                            noRowsOverlayComponent={NoRowsOverlay}
                            noRowsOverlayComponentParams={{
                                message: error
                                    ? `There was an error fetching the Users`
                                    : `Currently, there are no Users configured in the system`,
                                error,
                            }}
                        />
                    </div>
                    <div>
                        <Outlet context={{ onRefresh: fetchUsers }} />
                    </div>
                </ContentWrapper>
                <AGGridTablePagination gridRef={gridRef} totalRows={users?.length} />
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default MainLayout;

type ContextType = { onRefresh: () => void };

export const useOnRefresh = () => {
    return useOutletContext<ContextType>();
};
