/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Box, Button, RightTray, LoadingOverlay } from 'ui-style';
import { useToggle } from 'ui-common';
import { useDialog, useSchemaDetail, useOrg } from '@tripudiotech/admin-caching-store';
import { useMatch, useNavigate } from 'react-router-dom';
import get from 'lodash/get';
import UserInformation from '../components/UserInformation';
import useLocalStore from '../store/useLocalStore';
import { useOnRefresh } from '../layouts/MainLayout';
import UserForm from '../components/UserForm';
import { FormikProps } from 'formik';
import { SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';

const UserDetail = () => {
    const [isEditing, isEditingToggle] = useToggle(false);
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const { onRefresh } = useOnRefresh();
    const formRef = useRef<FormikProps<any>>(null);
    const navigate = useNavigate();
    const matched = useMatch('user/:id');
    const userId = get(matched, 'params.id');
    const [personSchema, getSchema] = useSchemaDetail((state) => [
        state.schema[SYSTEM_ENTITY_TYPE.PERSON],
        state.getSchema,
    ]);
    const [getCompanies] = useOrg((state) => [state.getCompanies]);

    const {
        userInfo,
        getUserInfo,
        deactivateUser,
        activateUser,
        updateUser,
        assignedRoles,
        assignRoles,
        cleanUserInfo,
        setLoading,
    } = useLocalStore();

    const isActive = get(userInfo, ['state', 'name'], '') === 'Active';
    useEffect(() => {
        getUserInfo(userId);
        getSchema(SYSTEM_ENTITY_TYPE.PERSON);
        getCompanies();
    }, [userId]);

    const handleClose = useCallback(() => {
        cleanUserInfo();
        navigate(`/user`);
    }, []);

    const onSubmit = () => {
        formRef.current?.submitForm();
    };

    const handleChangeStatus = useCallback(async () => {
        try {
            setLoading(true);
            if (!isActive) {
                await activateUser();
            } else {
                await deactivateUser();
            }
            onRefresh();
            handleClose();
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    }, [userInfo]);

    const onChangeStatus = useCallback(() => {
        onOpenDialog(
            'Delete attribute',
            `Do you really want to <b>${!isActive ? 'activate' : 'deactivate'}</b> the user <b>${get(
                userInfo,
                'properties.name',
                ' '
            )}</b> `,
            handleChangeStatus,
            'error'
        );
    }, [onOpenDialog, handleChangeStatus]);

    const handleSubmit = useCallback(
        async (values, roles, departments, teams) => {
            const rs = await updateUser(values, roles, departments, teams);
            if (rs) {
                onRefresh();
                handleClose();
            }
        },
        [onRefresh, assignedRoles]
    );

    return (
        <RightTray
            title={`${get(userInfo, 'properties.name', ' ')}`}
            componentName={'user-detail'}
            open={true}
            onClose={handleClose}
            disableCloseOnClickOutside={isEditing}
            hideConfirm
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                    p: '16px',
                    gap: '16px',
                    overflow: 'auto',
                }}
            >
                {isEditing ? (
                    <UserForm formRef={formRef} handleSubmit={handleSubmit} />
                ) : (
                    <UserInformation userInfo={userInfo} personSchema={personSchema} />
                )}
            </Box>
            <Box
                sx={{
                    mt: 'auto',
                    display: 'flex',
                    gap: '8px',
                    justifyContent: 'flex-end',
                    margin: '16px 24px',
                    alignSelf: 'flex-end',
                }}
            >
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={() => (isEditing ? isEditingToggle.close() : handleClose())}
                >
                    Cancel
                </Button>
                {!isEditing && (
                    <span>
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                            }}
                            variant="outlined"
                            onClick={onChangeStatus}
                            color="error"
                        >
                            {!isActive ? 'Activate' : 'Deactivate'}
                        </Button>
                    </span>
                )}
                <span>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        color="primary"
                        onClick={() => (isEditing ? onSubmit() : isEditingToggle.open())}
                    >
                        {isEditing ? 'Save' : 'Edit'}
                    </Button>
                </span>
            </Box>
        </RightTray>
    );
};

export default UserDetail;
