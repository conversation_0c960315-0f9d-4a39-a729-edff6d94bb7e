.user-variables:
  variables:
    PACKAGE_DIR: packages/user

aws-stag-user-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .user-variables

aws-stag-user-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .user-variables
  needs:
    - aws-stag-user-package

gcp-stag-user-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .user-variables

gcp-stag-user-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .user-variables
  needs:
    - gcp-stag-user-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-user-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .user-variables

gcp-uat-user-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .user-variables
  needs:
    - gcp-uat-user-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json