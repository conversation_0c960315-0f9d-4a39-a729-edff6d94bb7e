.business-rule-variables:
  variables:
    PACKAGE_DIR: packages/business-rule

aws-stag-business-rule-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .business-rule-variables

aws-stag-business-rule-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .business-rule-variables
  needs:
    - aws-stag-business-rule-package

gcp-stag-business-rule-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .business-rule-variables

gcp-stag-business-rule-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .business-rule-variables
  needs:
    - gcp-stag-business-rule-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-business-rule-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .business-rule-variables

gcp-uat-business-rule-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .business-rule-variables
  needs:
    - gcp-uat-business-rule-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json