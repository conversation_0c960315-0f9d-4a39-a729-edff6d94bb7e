/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useEffect, useCallback } from 'react';
import { Box, Button, RightTray, Stack, SchemaFieldSection, LoadingOverlay } from 'ui-style';
import { useNavigate, useParams, useLocation } from 'react-router-dom';

import { notifySuccess } from '@tripudiotech/admin-styleguide';
import RuleForm from '../components/RuleForm';
import useLocalStore from '../store';

const BusinessRuleEdit = () => {
    const navigate = useNavigate();
    const params = useParams();
    const [isLoading, ruleData, updateRule, getRuleDetail] = useLocalStore((state) => [
        state.isLoading,
        state.rule[params.id],
        state.updateRule,
        state.getRuleDetail,
    ]);
    const formRef = useRef(null);
    // const [ruleData, setRuleData] = useState(null);
    const emitRefresfEventNRedirect = useCallback(
        (data: any) => {
            // Dispatch a custom event to refresh the data of rule table
            const refreshProcessListEvent = new CustomEvent('refresh-rule-table', { detail: true });
            window.dispatchEvent(refreshProcessListEvent);
            navigate(`/business-rule/${params.id}`, {
                state: {
                    data,
                },
            });
        },
        [navigate]
    );

    const onUpdateSuccess = (res) => {
        notifySuccess(<span>Update Rule successfully</span>);
        formRef.current.resetForm();
        emitRefresfEventNRedirect(res.data);
    };

    const onUpdateFailed = useCallback(() => {
        formRef.current?.setSubmitting(false);
    }, [formRef]);

    const handleSave = async () => {
        formRef.current.submitForm();
        if (!formRef.current.isValid) return;

        updateRule(params.id, formRef.current.values, onUpdateSuccess, onUpdateFailed);
    };

    useEffect(() => {
        getRuleDetail(params.id);
    }, [params.id]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title="Business Rule Detail"
                componentName="business-rule-detail"
                open
                onClose={() => {
                    navigate(`/business-rule/${params.id}`);
                }}
                PaperProps={{
                    sx: {
                        width: 950,
                    },
                }}
                disableCloseOnClickOutside
                hideConfirm
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                    }}
                >
                    <Box
                        className="general-info--wrapper"
                        sx={{
                            width: '100%',
                        }}
                    >
                        <Box
                            className="general-info"
                            sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px' }}
                        >
                            <SchemaFieldSection>General Information</SchemaFieldSection>
                            <RuleForm formRef={formRef} ruleData={ruleData} />
                        </Box>
                    </Box>
                </Stack>

                <Box
                    sx={{
                        mt: 'auto',
                        display: 'flex',
                        gap: '8px',
                        justifyContent: 'flex-end',
                        margin: '16px 24px',
                    }}
                >
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={() => {
                            formRef.current?.resetForm();
                            navigate(`/business-rule/${params.id}`, { state: { data: ruleData } });
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        onClick={() => {
                            handleSave();
                        }}
                        color="primary"
                    >
                        Save
                    </Button>
                </Box>
            </RightTray>

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default BusinessRuleEdit;
