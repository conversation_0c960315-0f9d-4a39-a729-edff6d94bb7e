/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useState, useCallback } from 'react';
import { Box, Button, RightTray, Stack, SchemaFieldSection, LoadingOverlay, Grid, SchemaField } from 'ui-style';
import { useNavigate, useParams, useLocation, Outlet } from 'react-router-dom';
import { ruleUrls, fetch } from '@tripudiotech/admin-api';
import { useEventTypes, useDialog } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import useLocalStore from '../store';

const BusinessRuleDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { eventTypes } = useEventTypes();
    const { isLoading, updateLoading } = useLocalStore();
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const { id } = useParams();
    const [ruleData, getRuleDetail] = useLocalStore((state) => [state.rule[id], state.getRuleDetail]);
    const emitRefresfEventNRedirect = useCallback(() => {
        // Dispatch a custom event to refresh the data of rule
        const refreshProcessListEvent = new CustomEvent('refresh-rule-table', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
        navigate('/business-rule');
    }, [navigate]);

    const handleDelete = () => {
        onOpenDialog(
            'Delete Rule',
            `Do you want to delete Rule <b>${ruleData.name}</b>`,
            async () => {
                try {
                    updateLoading(true);
                    await fetch({
                        ...ruleUrls.deleteRule,
                        params: {
                            ruleId: ruleData.id,
                        },
                        successMessage: (
                            <span>
                                <b>{ruleData.name}</b> has been deleted successfully
                            </span>
                        ),
                    });
                    updateLoading(false);
                    emitRefresfEventNRedirect();
                } catch (e) {
                    updateLoading(false);
                }
            },
            'error'
        );
    };

    useEffect(() => {
        if (id) {
            getRuleDetail(id);
        }
    }, [id]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title="Business Rule Detail"
                componentName="business-rule-detail"
                open
                onClose={() => {
                    navigate('/business-rule');
                }}
                PaperProps={{
                    sx: {
                        width: 475,
                    },
                }}
                disableCloseOnClickOutside
                hideConfirm
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                    }}
                >
                    <Box
                        className="general-info--wrapper"
                        sx={{
                            width: '100%',
                        }}
                    >
                        <Box
                            className="general-info"
                            sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px' }}
                        >
                            <SchemaFieldSection>General Information</SchemaFieldSection>

                            <Grid container spacing={2}>
                                <Grid
                                    item
                                    xs={12}
                                    sx={{
                                        '> .MuiGrid-root': {
                                            '&:nth-of-type(2)': {
                                                'div:nth-of-type(2)': {
                                                    paddingTop: 0,
                                                    span: {
                                                        lineHeight: '18px',
                                                    },
                                                },
                                            },
                                        },
                                    }}
                                >
                                    <SchemaField label="Name" value={get(ruleData, 'name', '')} />
                                    <SchemaField
                                        label="Description"
                                        value={get(ruleData, 'description', '')}
                                        breakline
                                    />
                                    <SchemaField label="Entity Type" value={get(ruleData, 'entityType', '')} />
                                    <SchemaField
                                        label="Event"
                                        value={get(eventTypes, [get(ruleData, 'eventType', ''), 'description'], '')}
                                    />
                                </Grid>
                            </Grid>
                        </Box>
                    </Box>
                </Stack>

                <Box
                    sx={{
                        mt: 'auto',
                        display: 'flex',
                        gap: '8px',
                        justifyContent: 'flex-end',
                        margin: '16px 24px',
                    }}
                >
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={() => {
                            navigate('/business-rule');
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                        }}
                        variant="outlined"
                        onClick={handleDelete}
                        color="error"
                    >
                        Delete
                    </Button>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        onClick={() => {
                            navigate(`${location.pathname}/edit`, {
                                state: { data: location.state?.data || ruleData },
                            });
                        }}
                        color="primary"
                    >
                        Edit
                    </Button>
                </Box>
            </RightTray>

            {isLoading && <LoadingOverlay />}

            <Outlet />
        </Box>
    );
};

export default BusinessRuleDetail;
