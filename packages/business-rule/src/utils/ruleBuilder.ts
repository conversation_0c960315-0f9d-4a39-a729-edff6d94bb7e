/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Config, Fields, FieldOrGroup, MuiConfig } from '@react-awesome-query-builder/mui';
import orderBy from 'lodash/orderBy';
import * as yup from 'yup';
import { SYSTEM_RELATION, RELATION_DIRECTION } from '@tripudiotech/admin-api';
import { RULE_ACTION_TYPE } from '../constants';

export const validationSchema = yup.object({
    name: yup.string().required('Name is required'),
    description: yup.string(),
    entityType: yup.string().required('Entity Type is required'),
    eventType: yup.string().required('Event Type is required'),
    then: yup.object({
        status: yup.string().required('Status is required'),
        message: yup.string(),
        modify: yup.string(),
    }),
    actions: yup.array().of(
        yup.object({
            actionType: yup.string().required('Action Type is required'),
            parameters: yup
                .object()
                .when('actionType', {
                    is: RULE_ACTION_TYPE.ADD_RELATION,
                    then: yup.object({
                        fromEntity: yup.string().required('You must select an entity to create a relationship'),
                        toEntity: yup.string().required('You must select an entity to create a relationship'),
                        relationType: yup.string().required('Relationship Type is required'),
                    }),
                })
                .when('actionType', {
                    is: RULE_ACTION_TYPE.NOTIFY,
                    then: yup.object({
                        recipients: yup.string().required('Recipients is required to send a notification'),
                        templateName: yup.string().required('An email template is required to send a notification'),
                    }),
                }),
        })
    ),
});

export const DEFAULT_RULE_CONFIG: Config = MuiConfig;

delete DEFAULT_RULE_CONFIG.operators.proximity;

export const DEFAULT_STATE_FIELDS: FieldOrGroup = {
    type: '!group',
    label: 'State',
    mode: 'struct',
    subfields: {
        name: {
            type: 'text',
            label: 'Name',
        },
        isObsoleted: {
            type: 'boolean',
            label: 'Is obsoleted',
        },
        isOfficial: {
            type: 'boolean',
            label: 'Is Official',
        },
        isSuperseded: {
            type: 'boolean',
            label: 'Is Superseded',
        },
    },
};

export const DEFAULT_PERMISSION_FIELDS: FieldOrGroup = {
    type: '!group',
    label: 'Permission',
    mode: 'struct',
    subfields: {
        canCheckIn: {
            type: 'boolean',
            label: 'canCheckIn',
        },
        canCheckOut: {
            type: 'boolean',
            label: 'canCheckOut',
        },
        canConnectAsFromSide: {
            type: 'boolean',
            label: 'canConnectAsFromSide',
        },
        canConnectAsToSide: {
            type: 'boolean',
            label: 'canConnectAsToSide',
        },
        canCopy: {
            type: 'boolean',
            label: 'canCopy',
        },
        canCreate: {
            type: 'boolean',
            label: 'canCreate',
        },
        canDelete: {
            type: 'boolean',
            label: 'canDelete',
        },
        canDemote: {
            type: 'boolean',
            label: 'canDemote',
        },
        canDisconnectAsFromSide: {
            type: 'boolean',
            label: 'canDisconnectAsFromSide',
        },
        canDisconnectAsToSide: {
            type: 'boolean',
            label: 'canDisconnectAsToSide',
        },
        canDownload: {
            type: 'boolean',
            label: 'canDownload',
        },
        canGrantAccess: {
            type: 'boolean',
            label: 'canGrantAccess',
        },
        canLock: {
            type: 'boolean',
            label: 'canLock',
        },
        canModify: {
            type: 'boolean',
            label: 'canModify',
        },
        canPromote: {
            type: 'boolean',
            label: 'canPromote',
        },
        canPublish: {
            type: 'boolean',
            label: 'canPublish',
        },
        canRead: {
            type: 'boolean',
            label: 'canRead',
        },
        canRevise: {
            type: 'boolean',
            label: 'canRevise',
        },
        canRevokeAccess: {
            type: 'boolean',
            label: 'canRevokeAccess',
        },
        canUnlock: {
            type: 'boolean',
            label: 'canUnlock',
        },
        canClassify: {
            type: 'boolean',
            label: 'canClassify',
        },
    },
};

export const TYPE_MAPPER = {
    STRING: 'text',
    TEXT: 'text',
    BOOLEAN: 'boolean',
    INTEGER: 'number',
    LONG: 'number',
    FLOAT: 'number',
    DATE: 'date',
    DATE_TIME: 'datetime',
};

export const buildDynamicPropertiesConfig = (detailSchema): FieldOrGroup | any => {
    const attributes = orderBy(Object.values(detailSchema.attributes), 'name');
    const attributesConfig: any = attributes.reduce((prev: any, cur: any) => {
        const { name, displayName, type } = cur;
        return {
            ...prev,
            [name]: {
                type: TYPE_MAPPER[type],
                label: displayName,
            },
        };
    }, {});
    return {
        type: '!group',
        label: 'Properties',
        mode: 'struct',
        subfields: { ...attributesConfig },
    };
};

export const buildEntityConfig = (
    selectedSchema,
    label = 'Entity',
    isRelation = false,
    selectedSchemaLifecycle = null
) => {
    const propertiesConfig: any = selectedSchema
        ? buildDynamicPropertiesConfig(selectedSchema)
        : {
              type: '!group',
              label: 'Properties',
              subfields: {},
              mode: 'struct',
          };

    const defaultConfig = {
        type: '!group',
        mode: 'struct',
        label,
        subfields: {
            createdAt: {
                label: 'Created At',
                type: 'datetime',
            },
            createdBy: {
                label: 'Created By',
                type: 'text',
            },
            lifecycle: {
                type: '!struct',
                mode: 'struct',
                label: 'Lifecycle',
                subfields: {
                    id: {
                        label: 'Lifecycle Id',
                        type: 'text',
                    },
                    name: {
                        label: 'Lifecycle Name',
                        type: selectedSchemaLifecycle ? 'select' : 'text',
                        fieldSettings: selectedSchemaLifecycle
                            ? { listValues: selectedSchemaLifecycle.map((lifecycle) => lifecycle.lifeCycle.name) }
                            : {},
                    },
                },
            },
            owner: {
                type: '!struct',
                label: 'Owner',
                mode: 'struct',
                subfields: {
                    id: {
                        label: 'Owner Id',
                        type: 'text',
                    },
                    name: {
                        label: 'Owner Name',
                        type: 'text',
                    },
                    type: {
                        label: 'Owner Type',
                        type: 'text',
                    },
                },
            },
            permissions: DEFAULT_PERMISSION_FIELDS,
            state: DEFAULT_STATE_FIELDS,
        },
    };
    return isRelation
        ? {
              ...defaultConfig,
              type: '!struct',
              mode: 'struct',
              subfields: {
                  ...defaultConfig.subfields,
                  ...propertiesConfig.subfields,
              },
          }
        : {
              ...defaultConfig,
              subfields: {
                  ...defaultConfig.subfields,
                  properties: propertiesConfig,
              },
          };
};

export const buildDynamicRelationProperties = (selectedSchema, allSchema): FieldOrGroup => {
    if (!selectedSchema)
        return {
            type: '!group',
            label: 'Relations',
            subfields: {},
            mode: 'array',
        };
    const relEntityTypes = selectedSchema.relationTypes.map((rel) =>
        rel.direction === 'Incoming' ? rel.fromEntityType : rel.toEntityType
    );
    const relTypes = selectedSchema.relationTypes.map((rel) => ({
        value: rel.name,
        title: rel.displayName,
    }));
    let attributesMap = {};
    relEntityTypes.forEach((schema) => {
        attributesMap = {
            ...attributesMap,
            ...(allSchema[schema] ? allSchema[schema].attributes : {}),
        };
    });

    return {
        type: '!group',
        label: 'Relations',
        mode: 'array',
        subfields: {
            id: {
                type: 'text',
                label: 'Relation Id',
            },
            name: {
                type: 'select',
                label: 'Relation Name',
                fieldSettings: {
                    listValues: relTypes,
                },
            },
            relation: buildEntityConfig({ attributes: attributesMap }, 'Relation Entity', true),
        },
    };
};

const buildComponentsProperties = (schema, selectedSchemaLifecycle): FieldOrGroup => {
    const attributesConfig = schema ? buildDynamicPropertiesConfig(schema) : {};
    return {
        type: '!group',
        label: 'BOM Components',
        mode: 'array',
        subfields: {
            createdAt: {
                label: 'Created At',
                type: 'datetime',
            },
            createdBy: {
                label: 'Created By',
                type: 'text',
            },
            lifecycle: {
                type: '!struct',
                mode: 'struct',
                label: 'Lifecycle',
                subfields: {
                    id: {
                        label: 'Lifecycle Id',
                        type: 'text',
                    },
                    name: {
                        label: 'Lifecycle Name',
                        type: schema ? 'select' : 'text',
                        fieldSettings: selectedSchemaLifecycle
                            ? { listValues: selectedSchemaLifecycle.map((lifecycle) => lifecycle.lifeCycle.name) }
                            : {},
                    },
                },
            },
            permissions: DEFAULT_PERMISSION_FIELDS,
            state: DEFAULT_STATE_FIELDS,
            ...(attributesConfig?.subfields || {}),
        },
    };
};

export const buildFieldsConfig = (selectedSchema, allSchema, selectedSchemaLifecycle): Fields => {
    const hasAssemblyIncomingRelation = selectedSchema?.relationTypes?.filter(
        (relationType) =>
            relationType?.name === SYSTEM_RELATION.HAS_ASSEMBLY &&
            relationType?.direction === RELATION_DIRECTION.INCOMING
    );
    const fields: Fields = {
        entity: buildEntityConfig(selectedSchema, 'Entity', false, selectedSchemaLifecycle),
        metadata: {
            type: '!group',
            label: 'Metadata',
            mode: 'struct',
            subfields: {
                fromState: {
                    type: 'text',
                    label: 'From State',
                },
                toState: {
                    type: 'text',
                    label: 'To State',
                },
            },
        },
        relations: buildDynamicRelationProperties(selectedSchema, allSchema),
    };
    if (hasAssemblyIncomingRelation) {
        fields.components = buildComponentsProperties(selectedSchema, selectedSchemaLifecycle);
    }
    return fields;
};
