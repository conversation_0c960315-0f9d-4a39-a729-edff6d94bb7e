/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const RULE_ACTION_TYPE = {
    NOTIFY: 'NOTIFY',
    ADD_RELATION: 'ADD_RELATION',
    PROMOTE_RELATION: 'PROMOTE_RELATION',
};

export const RULE_ACTION_TYPE_LABEL = {
    NOTIFY: 'Notify',
    ADD_RELATION: 'Add Relation',
    PROMOTE_RELATION: 'Promote relation',
};

export const RULE_ACTION_TYPES = [
    {
        value: RULE_ACTION_TYPE.NOTIFY,
        label: RULE_ACTION_TYPE_LABEL[RULE_ACTION_TYPE.NOTIFY],
    },
    {
        value: RULE_ACTION_TYPE.ADD_RELATION,
        label: RULE_ACTION_TYPE_LABEL[RULE_ACTION_TYPE.ADD_RELATION],
    },
    {
        value: RULE_ACTION_TYPE.PROMOTE_RELATION,
        label: RULE_ACTION_TYPE_LABEL[RULE_ACTION_TYPE.PROMOTE_RELATION],
    },
];

export enum RuleStatus {
    Valid = 'Valid',
    InValid = 'InValid',
    Warning = 'Warning',
}
