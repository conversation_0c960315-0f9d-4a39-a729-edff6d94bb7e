/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, ruleUrls } from '@tripudiotech/admin-api';

export interface IBusinessRuleStore {
    isLoading: boolean;
    rule: Record<string, any>;
    getRuleDetail: (ruleId: string) => Promise<any>;
    createNewRule: (data: any, onSuccess: (res: any) => void, onFailed?: (error: any) => void) => void;
    updateRule: (ruleId: string, data: any, onSuccess: (res: any) => void, onFailed?: (e: any) => void) => void;
    updateLoading: (val: boolean) => void;
}

const parseRuleParameters = (data: any) => {
    return {
        ...data,
        actions: data?.actions?.map((action) => ({
            ...action,
            parameters: action?.parameters ? JSON.parse(action.parameters) : {},
        })),
    };
};

const useLocalStore = create<IBusinessRuleStore>((set, get) => ({
    isLoading: false,
    rule: {},
    updateLoading: (isLoading: boolean) => {
        set({
            isLoading,
        });
    },
    getRuleDetail: async (ruleId: string) => {
        if (get().rule[ruleId]) {
            return get().rule[ruleId];
        }
        get().updateLoading(true);
        try {
            const { data } = await fetch({
                ...ruleUrls.getRuleById,
                params: {
                    ruleId,
                },
            });
            set({
                rule: {
                    ...get().rule,
                    [ruleId]: parseRuleParameters(data),
                },
            });
            return data;
        } catch (err) {
            return false;
        } finally {
            get().updateLoading(false);
        }
    },
    createNewRule: async (data, onSuccess = (res: any) => {}, onFailed = (e: any) => {}) => {
        try {
            const reqBody = {
                name: data.name,
                description: data.description,
                eventType: data.eventType,
                entityType: data.entityType,
                ruleType: 'VALIDATION',
                then: {
                    status: data.then?.status,
                    message: data.then?.message,
                    modify: data.then?.modify,
                },
                expression: data.expression,
                actions: data.actions,
            };

            set({ isLoading: true });
            const res = await fetch({
                ...ruleUrls.createRule,
                data: reqBody,
            });
            onSuccess(res);
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
    updateRule: async (ruleId: string, data: any, onSuccess = (res: any) => {}, onFailed = (e: any) => {}) => {
        try {
            const reqBody = {
                name: data.name,
                description: data.description,
                eventType: data.eventType,
                entityType: data.entityType,
                ruleType: 'VALIDATION',
                then: {
                    status: data.then?.status,
                    message: data.then?.message,
                    modify: data.then?.modify,
                },
                expression: data.expression,
                actions: data.actions,
            };

            set({ isLoading: true });
            const res = await fetch({
                ...ruleUrls.updateRule,
                params: {
                    ruleId,
                },
                data: reqBody,
            });
            set({
                rule: {
                    ...get().rule,
                    [ruleId]: parseRuleParameters(res.data),
                },
            });
            onSuccess(res);
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
}));

export default useLocalStore;
