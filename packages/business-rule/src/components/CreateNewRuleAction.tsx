/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef } from 'react';
import { Box, Button, RightTray, Stack, SchemaFieldSection, LoadingOverlay } from 'ui-style';
import { useToggle } from 'ui-common';

import { notifySuccess } from '@tripudiotech/admin-styleguide';
import RuleForm from './RuleForm';
import useLocalStore from '../store';

const CreateProcessAction = () => {
    const [open, openToggle] = useToggle(false);
    const { isLoading, createNewRule } = useLocalStore();
    const formRef = useRef(null);

    const onCreateSuccess = (res) => {
        notifySuccess(<span>New Rule created successfully</span>);
        formRef.current.resetForm();
        openToggle.close();

        // Dispatch a custom event to refresh the data of business rule
        const refreshProcessListEvent = new CustomEvent('refresh-rule-table', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
    };

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <Button variant="contained" color="secondary" size="small" onClick={openToggle.open}>
                Create New Rule
            </Button>

            <RightTray
                title="Create Business Rule"
                componentName="create-business-rule"
                open={open}
                onClose={openToggle.close}
                onConfirm={async () => {
                    formRef.current.submitForm();
                    if (!formRef.current.isValid) return;
                    createNewRule(formRef.current.values, onCreateSuccess);
                }}
                confirmText="Create"
                disableCloseOnClickOutside
                defaultWidth={950}
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                        },
                    }}
                >
                    <Box
                        className="general-info--wrapper"
                        sx={{
                            width: '100%',
                        }}
                    >
                        <Box
                            className="general-info"
                            sx={{ display: 'flex', flexDirection: 'column', gap: '12px', padding: '12px' }}
                        >
                            <SchemaFieldSection>General Information</SchemaFieldSection>
                            <RuleForm formRef={formRef} />
                        </Box>
                    </Box>
                </Stack>
            </RightTray>

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default CreateProcessAction;
