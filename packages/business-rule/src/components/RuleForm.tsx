/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useState, useEffect, useMemo } from 'react';
import { Formik, FieldArray, Form, Field } from 'formik';
import { formHelper } from 'ui-common';
import {
    Box,
    FormikTextField,
    Autocomplete,
    TextField,
    MenuItem,
    Grid,
    Loading,
    Stack,
    FormControl,
    InputLabel,
    FormHelperText,
    Select,
    SchemaFieldSection,
    Button,
    PlusIcon,
    IconButton,
    DeleteIcon,
    Typography,
} from 'ui-style';
import { useEventTypes, useSchemaTree, useSchemaDetail, useAgent } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import find from 'lodash/find';
import { Utils as QbUtils, type JsonGroup, type Config } from '@react-awesome-query-builder/mui';

import QueryBuilder from './QueryBuilder';
import { buildFieldsConfig, DEFAULT_RULE_CONFIG, validationSchema } from '../utils/ruleBuilder';
import { RULE_ACTION_TYPES, RULE_ACTION_TYPE, RULE_ACTION_TYPE_LABEL, RuleStatus } from '../constants';
import orderBy from 'lodash/orderBy';

const queryValue: JsonGroup = { id: QbUtils.uuid(), type: 'group' };

const RuleForm = ({ formRef, ruleData }: any) => {
    const { eventTypes, isLoadingShemaDetail }: any = useEventTypes();
    const [schemaDetailMap, getSchema, schemaLifecycleMap, getLifecycle] = useSchemaDetail((state) => [
        state.schema,
        state.getSchema,
        state.schemaLifecycle,
        state.fetchSchemaLifecycle,
    ]);
    const [users, getUsers] = useAgent((state) => [state.users, state.getUsers]);
    const userEmailOptions = useMemo(() => {
        return (users || []).map((user) => user.email).sort();
    }, [users]);
    const initialValues = useMemo(
        () => ({
            name: get(ruleData, 'name', ''),
            description: get(ruleData, 'description', ''),
            entityType: get(ruleData, 'entityType', ''),
            eventType: get(ruleData, 'eventType', ''),
            expression: get(ruleData, 'expression', ''),
            then: get(ruleData, 'then', {
                status: '',
                message: '',
                modify: '',
            }),
            actions: get(ruleData, 'actions') || [],
        }),
        [ruleData]
    );

    const [selectedEventType, setSelectedEventType] = useState(null);
    const [selectedEntityType, setSelectedEntityType] = useState(null);

    const schemaTreeMap = useSchemaTree((state) => state.schemaTreeMap);

    const systemSchema = useMemo(() => (schemaTreeMap ? orderBy(Object.values(schemaTreeMap), item => item.displayName?.toLowerCase()) : []), [schemaTreeMap]);

    const selectedSchema = useMemo(
        () => schemaDetailMap[selectedEntityType?.name],
        [schemaDetailMap, selectedEntityType]
    );

    const relationTypes = useMemo(
        () =>  orderBy(selectedSchema?.relationTypes || [], item => (item.displayName || item.name)?.toLowerCase()), 
        [selectedSchema])

    const selectedSchemaLifecycle = useMemo(
        () => schemaLifecycleMap[selectedEntityType?.name],
        [schemaLifecycleMap, selectedEntityType]
    );
    const config: Config = useMemo(() => {
        const fieldsConfig = buildFieldsConfig(selectedSchema, schemaTreeMap, selectedSchemaLifecycle);
        return {
            ...DEFAULT_RULE_CONFIG,
            fields: fieldsConfig,
        };
    }, [schemaTreeMap, selectedSchema, selectedSchemaLifecycle]);

    const [state, setState] = useState({
        tree: QbUtils.checkTree(QbUtils.loadTree(queryValue), config),
        config,
    });

    useEffect(() => {
        if (formRef.current) {
            formRef.current?.setFieldValue(
                'expression',
                JSON.stringify(QbUtils.jsonLogicFormat(state.tree, state.config).logic)
            );
        }
    }, [state, formRef.current]);

    useEffect(() => {
        if (get(ruleData, 'expression', '')) {
            const expression = JSON.parse(ruleData.expression.replace(/\\n/g, '').replaceAll('\\', ''));
            setState({
                tree: QbUtils.checkTree(QbUtils.loadFromJsonLogic(expression, config), config),
                config,
            });
        }
    }, [ruleData, config]);

    useEffect(() => {
        const abortController = new AbortController();
        if (selectedEntityType) {
            const entityType = get(selectedEntityType, 'name');
            getSchema(entityType, true, abortController.signal);
            getLifecycle(entityType, abortController.signal);
        }
        return () => {
            abortController.abort();
        };
    }, [selectedEntityType]);

    useEffect(() => {
        if (get(ruleData, 'entityType', '')) {
            const entityType = find(systemSchema, (item: any) => item.name === get(ruleData, 'entityType', ''));
            setSelectedEntityType(entityType || null);
        }
    }, [ruleData, systemSchema]);

    useEffect(() => {
        if (get(ruleData, 'eventType', '')) {
            setSelectedEventType(eventTypes[get(ruleData, 'eventType', '')] || null);
        }
    }, [ruleData, eventTypes]);

    useEffect(() => {
        getUsers();
    }, []);

    return (
        <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={() => {}}
            innerRef={formRef}
        >
            {({ values }) => (
                <Form>
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Grid container spacing={2}>
                            <Grid item sm={12}>
                                <Field
                                    fullWidth
                                    component={FormikTextField}
                                    label="Rule Name"
                                    name="name"
                                    variant="outlined"
                                    required
                                />
                            </Grid>

                            <Grid item sm={6}>
                                <Field name="entityType">
                                    {({ field: { name }, form: { setFieldValue, setFieldTouched }, meta }) => {
                                        return (
                                            <Autocomplete
                                                disablePortal
                                                id="entity-type"
                                                value={selectedEntityType}
                                                onChange={async (event, newValue) => {
                                                    setSelectedEntityType(newValue);
                                                    await setFieldValue(name, newValue?.name || '');
                                                }}
                                                options={systemSchema || []}
                                                getOptionLabel={(option) => option?.displayName || ''}
                                                sx={{
                                                    '.MuiFormHelperText-root': {
                                                        display: !meta.value && meta.touched ? 'block' : 'none',
                                                        color: !meta.value && meta.touched ? '#d32f2f' : 'text.primary',
                                                    },
                                                    fieldset: {
                                                        borderColor:
                                                            !meta.value && meta.touched
                                                                ? '#d32f2f!important'
                                                                : 'rgba(0, 0, 0, 0.23)',
                                                    },
                                                    label: {
                                                        color:
                                                            !meta.value && meta.touched
                                                                ? '#d32f2f!important'
                                                                : 'rgba(0, 0, 0, 0.6)',
                                                    },
                                                }}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        required
                                                        label="Entity Type"
                                                        helperText={meta.error || ''}
                                                    />
                                                )}
                                                onClose={() => setFieldTouched(name, true)}
                                            />
                                        );
                                    }}
                                </Field>
                            </Grid>

                            <Grid item sm={6}>
                                <Field name="eventType">
                                    {({ field: { name }, form: { setFieldValue, setFieldTouched }, meta }) => {
                                        return (
                                            <Autocomplete
                                                disablePortal
                                                id="event-type"
                                                value={selectedEventType}
                                                onChange={async (event, newValue) => {
                                                    setSelectedEventType(newValue);
                                                    await setFieldValue(name, newValue?.name || '');
                                                }}
                                                options={Object.values(eventTypes) || []}
                                                getOptionLabel={(option) => option?.description || ''}
                                                sx={{
                                                    '.MuiFormHelperText-root': {
                                                        display: !meta.value && meta.touched ? 'block' : 'none',
                                                        color: !meta.value && meta.touched ? '#d32f2f' : 'transparent',
                                                    },
                                                    fieldset: {
                                                        borderColor:
                                                            !meta.value && meta.touched
                                                                ? '#d32f2f!important'
                                                                : 'rgba(0, 0, 0, 0.23)',
                                                    },
                                                    label: {
                                                        color:
                                                            !meta.value && meta.touched
                                                                ? '#d32f2f!important'
                                                                : 'rgba(0, 0, 0, 0.6)',
                                                    },
                                                }}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        required
                                                        label="Event Type"
                                                        helperText={meta.error || ''}
                                                    />
                                                )}
                                                onClose={() => setFieldTouched(name, true)}
                                            />
                                        );
                                    }}
                                </Field>
                            </Grid>

                            <Grid item sm={12}>
                                <Field
                                    fullWidth
                                    multiline
                                    minRows={2}
                                    component={FormikTextField}
                                    label="Description"
                                    name="description"
                                    variant="outlined"
                                />
                            </Grid>
                        </Grid>
                    </Box>
                    <Box className="rule-builder" sx={{ padding: '12px 0' }}>
                        {selectedEntityType?.name && (
                            <>
                                {isLoadingShemaDetail || !selectedSchema ? (
                                    <Loading
                                        sx={{
                                            textAlign: 'center',
                                            span: {
                                                width: '20px!important',
                                                height: '20px!important',
                                            },
                                        }}
                                    />
                                ) : (
                                    <QueryBuilder config={config} state={state} setState={setState} />
                                )}
                                <Box className="query-builder-result" sx={{ mt: '16px' }}>
                                    <SchemaFieldSection>Query String</SchemaFieldSection>
                                    <Box
                                        sx={{
                                            padding: '8px',
                                            backgroundColor: '#F7F8FC',
                                            border: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                            my: '8px',
                                            overflowX: 'auto',
                                        }}
                                    >
                                        <pre>{JSON.stringify(QbUtils.sqlFormat(state.tree, config))}</pre>
                                    </Box>
                                </Box>
                            </>
                        )}
                    </Box>
                    {selectedEntityType?.name && !isLoadingShemaDetail && (
                        <Box className="then-expression" sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                            <SchemaFieldSection>Validation Result</SchemaFieldSection>
                            <Stack direction="row" gap={2}>
                                <Box sx={{ width: '100%' }}>
                                    <Grid container spacing={2}>
                                        <Grid item sm={4}>
                                            <Field name="then.status">
                                                {({
                                                    field: { name, value },
                                                    form: { touched, setFieldValue, setTouched },
                                                    meta,
                                                }) => {
                                                    return (
                                                        <FormControl error={meta.touched && !value} required fullWidth>
                                                            <InputLabel id="status">Status</InputLabel>
                                                            <Select
                                                                labelId="status"
                                                                id="status-label"
                                                                label="Status"
                                                                value={value}
                                                                onChange={(e) => setFieldValue(name, e.target.value)}
                                                                onClose={() => {
                                                                    setTouched({
                                                                        ...touched,
                                                                        then: {
                                                                            status: true,
                                                                        },
                                                                    });
                                                                }}
                                                            >
                                                                {Object.values(RuleStatus).map((status) => (
                                                                    <MenuItem key={status} value={status}>
                                                                        {status}
                                                                    </MenuItem>
                                                                ))}
                                                            </Select>

                                                            {meta.touched && !value && (
                                                                <FormHelperText>
                                                                    {formHelper.buildRequiredMessage('Status')}
                                                                </FormHelperText>
                                                            )}
                                                        </FormControl>
                                                    );
                                                }}
                                            </Field>
                                        </Grid>

                                        <Grid item sm={8}>
                                            <Field
                                                fullWidth
                                                component={FormikTextField}
                                                label="Message"
                                                name="then.message"
                                                variant="outlined"
                                            />
                                        </Grid>
                                    </Grid>
                                </Box>
                            </Stack>
                            <Box sx={{ mt: '8px' }}>
                                <SchemaFieldSection>Action</SchemaFieldSection>
                                <FieldArray
                                    name="actions"
                                    render={(arrayHelpers) => {
                                        return (
                                            <Box
                                                sx={{
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    gap: '16px',
                                                    py: '8px',
                                                    mb: '32px',
                                                }}
                                            >
                                                {values.actions.map((action, index) => (
                                                    <Box
                                                        key={`action-${index}`}
                                                        sx={{
                                                            padding: '24px 8px',
                                                            backgroundColor: '#F7F8FC',
                                                            border: (theme) =>
                                                                `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                                            position: 'relative',
                                                        }}
                                                    >
                                                        <Box
                                                            sx={{
                                                                width: '100%',
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                marginTop: '-16px',
                                                                alignItems: 'center',
                                                                marginBottom: '8px',
                                                                paddingLeft: '8px',
                                                            }}
                                                        >
                                                            <Typography variant="label2-med">
                                                                {RULE_ACTION_TYPE_LABEL[
                                                                    values.actions[index]?.actionType
                                                                ] || 'Rule Action'}
                                                            </Typography>
                                                            <IconButton
                                                                color="error"
                                                                size="small"
                                                                onClick={() => arrayHelpers.remove(index)}
                                                            >
                                                                <DeleteIcon />
                                                            </IconButton>
                                                        </Box>
                                                        <Grid container spacing={2}>
                                                            <Grid item xs={6}>
                                                                <Field name={`actions.${index}.actionType`}>
                                                                    {({
                                                                        field: { name, value },
                                                                        form: { setFieldValue, setFieldTouched },
                                                                        meta,
                                                                    }) => {
                                                                        return (
                                                                            <FormControl
                                                                                error={meta.touched && !value}
                                                                                required
                                                                                fullWidth
                                                                            >
                                                                                <InputLabel id={`action-type-${index}`}>
                                                                                    Action Type
                                                                                </InputLabel>
                                                                                <Select
                                                                                    labelId={`action-type-${index}`}
                                                                                    id={`action-type-${index}-label`}
                                                                                    label="Action Type"
                                                                                    value={value}
                                                                                    onChange={(e) =>
                                                                                        setFieldValue(
                                                                                            name,
                                                                                            e.target.value
                                                                                        )
                                                                                    }
                                                                                    onClose={() => {
                                                                                        setFieldTouched(
                                                                                            name,
                                                                                            true,
                                                                                            true
                                                                                        );
                                                                                    }}
                                                                                >
                                                                                    {RULE_ACTION_TYPES.map(
                                                                                        ({ label, value }) => (
                                                                                            <MenuItem
                                                                                                value={value}
                                                                                                key={value}
                                                                                            >
                                                                                                {label}
                                                                                            </MenuItem>
                                                                                        )
                                                                                    )}
                                                                                </Select>

                                                                                {meta.touched && !value && (
                                                                                    <FormHelperText>
                                                                                        {formHelper.buildRequiredMessage(
                                                                                            'Action Type'
                                                                                        )}
                                                                                    </FormHelperText>
                                                                                )}
                                                                            </FormControl>
                                                                        );
                                                                    }}
                                                                </Field>
                                                            </Grid>

                                                            {values.actions[index].actionType ===
                                                                RULE_ACTION_TYPE.PROMOTE_RELATION && (
                                                                <>
                                                                    <Grid item xs={12}>
                                                                        <Field
                                                                            name={`actions.${index}.parameters.filterJsonPath`}
                                                                        >
                                                                            {({
                                                                                field: { name, value },
                                                                                form: {
                                                                                    setFieldValue,
                                                                                    setFieldTouched,
                                                                                },
                                                                                meta,
                                                                            }) => {
                                                                                // Parse relation type from JSONPath
                                                                                const relationTypeMatch =
                                                                                    value?.match(
                                                                                        /@\.name\s*==\s*"([^"]+)"/
                                                                                    );
                                                                                const parsedValue = relationTypeMatch
                                                                                    ? relationTypeMatch[1]
                                                                                    : '';

                                                                                return (
                                                                                    <FormControl
                                                                                        error={meta.touched && !value}
                                                                                        required
                                                                                        fullWidth
                                                                                    >
                                                                                        <InputLabel
                                                                                            id={`relation-type-${index}`}
                                                                                        >
                                                                                            Relation Type
                                                                                        </InputLabel>
                                                                                        <Select
                                                                                            labelId={`relation-type-${index}`}
                                                                                            id={`relation-type-${index}-label`}
                                                                                            label="Relation Type"
                                                                                            value={parsedValue}
                                                                                            onChange={(e) =>
                                                                                                setFieldValue(
                                                                                                    name,
                                                                                                    `$[?(@.name == "${e.target.value}")]`
                                                                                                )
                                                                                            }
                                                                                            onClose={() => {
                                                                                                setFieldTouched(
                                                                                                    name,
                                                                                                    true,
                                                                                                    true
                                                                                                );
                                                                                            }}
                                                                                        >
                                                                                            {relationTypes.map(
                                                                                                (relationType) => (
                                                                                                    <MenuItem
                                                                                                        value={
                                                                                                            relationType.name
                                                                                                        }
                                                                                                        key={
                                                                                                            relationType.id
                                                                                                        }
                                                                                                    >
                                                                                                        {relationType.displayName ||
                                                                                                            relationType.name}
                                                                                                    </MenuItem>
                                                                                                )
                                                                                            )}
                                                                                        </Select>

                                                                                        {meta.touched && !value && (
                                                                                            <FormHelperText>
                                                                                                {formHelper.buildRequiredMessage(
                                                                                                    'Relation Type'
                                                                                                )}
                                                                                            </FormHelperText>
                                                                                        )}
                                                                                    </FormControl>
                                                                                );
                                                                            }}
                                                                        </Field>
                                                                    </Grid>
                                                                    <Grid item sm={12}>
                                                                        <Field
                                                                            fullWidth
                                                                            component={FormikTextField}
                                                                            label="To State"
                                                                            name={`actions.${index}.parameters.toState`}
                                                                            variant="outlined"
                                                                            required
                                                                        />
                                                                    </Grid>
                                                                </>
                                                            )}
                                                            {values.actions[index].actionType ===
                                                                RULE_ACTION_TYPE.NOTIFY && (
                                                                <>
                                                                    <Grid item xs={12}>
                                                                        <Field
                                                                            name={`actions.${index}.parameters.recipients`}
                                                                        >
                                                                            {({
                                                                                field: { name, value },
                                                                                form: {
                                                                                    setFieldValue,
                                                                                    setFieldTouched,
                                                                                },
                                                                                meta,
                                                                            }) => {
                                                                                return (
                                                                                    <Autocomplete
                                                                                        disablePortal
                                                                                        value={
                                                                                            value
                                                                                                ? value.split(',')
                                                                                                : []
                                                                                        }
                                                                                        onChange={async (
                                                                                            event,
                                                                                            newValue
                                                                                        ) => {
                                                                                            setFieldValue(
                                                                                                name,
                                                                                                newValue.join(',')
                                                                                            );
                                                                                        }}
                                                                                        freeSolo
                                                                                        multiple
                                                                                        options={userEmailOptions}
                                                                                        getOptionLabel={(option) =>
                                                                                            option
                                                                                        }
                                                                                        sx={{
                                                                                            '.MuiFormHelperText-root': {
                                                                                                display:
                                                                                                    !meta.value &&
                                                                                                    meta.touched
                                                                                                        ? 'block'
                                                                                                        : 'none',
                                                                                                color:
                                                                                                    !meta.value &&
                                                                                                    meta.touched
                                                                                                        ? '#d32f2f'
                                                                                                        : 'text.primary',
                                                                                            },
                                                                                            fieldset: {
                                                                                                borderColor:
                                                                                                    !meta.value &&
                                                                                                    meta.touched
                                                                                                        ? '#d32f2f!important'
                                                                                                        : 'rgba(0, 0, 0, 0.23)',
                                                                                            },
                                                                                            label: {
                                                                                                color:
                                                                                                    !meta.value &&
                                                                                                    meta.touched
                                                                                                        ? '#d32f2f!important'
                                                                                                        : 'rgba(0, 0, 0, 0.6)',
                                                                                            },
                                                                                        }}
                                                                                        renderInput={(params) => (
                                                                                            <TextField
                                                                                                {...params}
                                                                                                required
                                                                                                label="Recipients"
                                                                                                helperText={
                                                                                                    meta.error || ''
                                                                                                }
                                                                                            />
                                                                                        )}
                                                                                        onClose={() =>
                                                                                            setFieldTouched(name, true)
                                                                                        }
                                                                                    />
                                                                                );
                                                                            }}
                                                                        </Field>
                                                                    </Grid>
                                                                    <Grid item sm={12}>
                                                                        <Field
                                                                            fullWidth
                                                                            component={FormikTextField}
                                                                            label="Template Name"
                                                                            name={`actions.${index}.parameters.templateName`}
                                                                            variant="outlined"
                                                                            required
                                                                        />
                                                                    </Grid>
                                                                </>
                                                            )}
                                                            {values.actions[index].actionType ===
                                                                RULE_ACTION_TYPE.ADD_RELATION && (
                                                                <>
                                                                    <Grid item xs={12}>
                                                                        <Field
                                                                            name={`actions.${index}.parameters.relationType`}
                                                                        >
                                                                            {({
                                                                                field: { name, value },
                                                                                form: {
                                                                                    setFieldValue,
                                                                                    setFieldTouched,
                                                                                },
                                                                                meta,
                                                                            }) => {
                                                                                return (
                                                                                    <FormControl
                                                                                        error={meta.touched && !value}
                                                                                        required
                                                                                        fullWidth
                                                                                    >
                                                                                        <InputLabel
                                                                                            id={`relation-type-${index}`}
                                                                                        >
                                                                                            Relation Type
                                                                                        </InputLabel>
                                                                                        <Select
                                                                                            labelId={`relation-type-${index}`}
                                                                                            id={`relation-type-${index}-label`}
                                                                                            label="Relation Type"
                                                                                            value={value}
                                                                                            onChange={(e) =>
                                                                                                setFieldValue(
                                                                                                    name,
                                                                                                    e.target.value
                                                                                                )
                                                                                            }
                                                                                            onClose={() => {
                                                                                                setFieldTouched(
                                                                                                    name,
                                                                                                    true,
                                                                                                    true
                                                                                                );
                                                                                            }}
                                                                                        >
                                                                                            {relationTypes.map(
                                                                                                (relationType) => (
                                                                                                    <MenuItem
                                                                                                        value={
                                                                                                            relationType.name
                                                                                                        }
                                                                                                        key={
                                                                                                            relationType.id
                                                                                                        }
                                                                                                    >
                                                                                                        {relationType.displayName ||
                                                                                                            relationType.name}
                                                                                                    </MenuItem>
                                                                                                )
                                                                                            )}
                                                                                        </Select>

                                                                                        {meta.touched && !value && (
                                                                                            <FormHelperText>
                                                                                                {formHelper.buildRequiredMessage(
                                                                                                    'Relation Type'
                                                                                                )}
                                                                                            </FormHelperText>
                                                                                        )}
                                                                                    </FormControl>
                                                                                );
                                                                            }}
                                                                        </Field>
                                                                    </Grid>
                                                                    <Grid item sm={6}>
                                                                        <Field
                                                                            fullWidth
                                                                            component={FormikTextField}
                                                                            label="From Entity"
                                                                            name={`actions.${index}.parameters.fromEntity`}
                                                                            variant="outlined"
                                                                            required
                                                                        />
                                                                    </Grid>
                                                                    <Grid item sm={6}>
                                                                        <Field
                                                                            fullWidth
                                                                            component={FormikTextField}
                                                                            label="To Entity"
                                                                            name={`actions.${index}.parameters.toEntity`}
                                                                            variant="outlined"
                                                                            required
                                                                        />
                                                                    </Grid>
                                                                </>
                                                            )}
                                                        </Grid>
                                                    </Box>
                                                ))}
                                                <div>
                                                    <Button
                                                        size="small"
                                                        variant="contained"
                                                        color="secondary"
                                                        endIcon={<PlusIcon />}
                                                        onClick={() => {
                                                            arrayHelpers.push({
                                                                actionType: null,
                                                                parameters: {},
                                                            });
                                                        }}
                                                    >
                                                        Add Action
                                                    </Button>
                                                </div>
                                            </Box>
                                        );
                                    }}
                                />
                            </Box>
                        </Box>
                    )}
                </Form>
            )}
        </Formik>
    );
};

export default RuleForm;
