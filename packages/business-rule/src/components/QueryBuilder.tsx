/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback } from 'react';
import type { JsonGroup, Config, ImmutableTree, BuilderProps } from '@react-awesome-query-builder/mui';
import { Query, Builder, Utils as QbUtils } from '@react-awesome-query-builder/mui';
import { Box, styled } from 'ui-style';

import '@react-awesome-query-builder/mui/css/styles.css';

const StyledBox = styled(Box)(({ theme }) => ({
    '& .query-builder': {
        margin: 0,
        '& .group-or-rule-container': {
            paddingRight: 0,
            '&:first-of-type': {
                // paddingRight: 0,
            },
        },
        '.group, .rule_group .group-or-rule': {
            background: theme.palette.glide.background.normal.inversePrimary,
            borderColor: theme.palette.glide.stroke.normal.primary,
            borderRadius: 0,
        },
        '.rule_group .rule_group--children .group-or-rule-container': {
            paddingRight: 0,
        },
        '.group--actions': {
            opacity: '1!important',
            button: {
                fontSize: '14px',
                textTransform: 'capitalize',
                fontFamily: 'Work Sans',
                display: 'flex',
                flexDirection: 'row-reverse',
                span: {
                    '&:first-of-type': {
                        margin: 0,
                        marginLeft: '5px',
                    },
                },
                '&:last-of-type': {
                    svg: {
                        fill: theme.palette.glide.text.normal.main,
                    },
                },
            },
        },
        '.group--children': {
            marginRight: '12px',
        },
        '.MuiInput-input': {
            fontSize: '14px',
            fontFamily: 'Work Sans',
            color: theme.palette.glide.text.normal.inverseTertiary,
        },
        '.MuiButtonGroup-root': {
            button: {
                fontSize: '14px',
                textTransform: 'capitalize',
                fontFamily: 'Work Sans',
                borderRadius: 0,
            },
        },
    },
    mb: 2,
    '.query-builder-container': {
        background: theme.palette.glide.background.normal.inversePrimary,
    },
}));

const QueryBuilder = ({ state, setState, config }) => {
    const onChange = useCallback(
        (immutableTree: ImmutableTree, config: Config) => {
            setState((prevState) => ({
                ...prevState,
                tree: immutableTree,
                config,
            }));
        },
        [config]
    );
    const renderBuilder = useCallback(
        (props: BuilderProps) => (
            <div className="query-builder-container">
                <div className="query-builder qb-lite">
                    <Builder {...props} />
                </div>
            </div>
        ),
        []
    );
    return (
        <StyledBox>
            <Query {...config} value={state.tree} onChange={onChange} renderBuilder={renderBuilder} />
        </StyledBox>
    );
};

export default QueryBuilder;
