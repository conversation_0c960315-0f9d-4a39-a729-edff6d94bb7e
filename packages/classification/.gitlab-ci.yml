.classification-variables:
  variables:
    PACKAGE_DIR: packages/classification

aws-stag-classification-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .classification-variables

aws-stag-classification-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .classification-variables
  needs:
    - aws-stag-classification-package

gcp-stag-classification-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .classification-variables

gcp-stag-classification-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .classification-variables
  needs:
    - gcp-stag-classification-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-classification-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .classification-variables

gcp-uat-classification-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .classification-variables
  needs:
    - gcp-uat-classification-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json