/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Box, styled } from 'ui-style';

const TreeWrapper = styled(Box)(({ theme }) => ({
    borderRight: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    display: 'flex',
    padding: '16px',
    paddingBottom: 0,
    flexDirection: 'column',
    gap: '16px',
    position: 'relative',
    height: '100%',
    '& .searchBox': {
        '& input': {
            paddingLeft: '4px !important',
            fontSize: '14px',
        },
        background: '#FFFFFF',
    },
    '& .ag-header': {
        border: 'none !important',
    },
    '& .ag-header-container': {
        height: 0,
    },
    '& .ag-theme-alpine .ag-row > .ag-cell-wrapper': {
        paddingLeft: 0,
        paddingRight: 0,
    },
    '& .ag-group-expanded, .ag-group-contracted': {
        marginRight: '4px !important',
    },
    '& .ag-theme-alpine .ag-ltr .ag-row-group-leaf-indent': {
        marginLeft: '8px',
    },
    '& .agContainer': {
        height: '100%',
    },
    '& .item': {
        display: 'flex',
        gap: '4px',
        color: theme.palette.glide.text.normal.inverseTertiary,
        textDecoration: 'none',
    },
    '& .ag-group-contracted, .ag-group-expanded': {
        marginLeft: '7px',
        '&.hidden': {
            marginLeft: 0,
        },
    },
}));

const TreeContainer = ({ children }) => {
    return <TreeWrapper className="treeContainer">{children}</TreeWrapper>;
};

export default TreeContainer;
