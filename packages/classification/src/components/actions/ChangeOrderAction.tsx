/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback } from 'react';
import { ISchemaDetail, AttributeSchema, schemaHelper } from 'ui-common';
import { AttributeOrderIcon, Button, CloseIcon, SaveIcon } from 'ui-style';
import { updateAttributeOrder } from '../../actions';
import useLocalStore from '../../store/useLocalStore';
import { clearTableStates, isAttributeOrderEqual } from '../../utils/helper';

export type ChangeOrderActionProps = {
    schemaDetail: ISchemaDetail;
    changingOrder: boolean;
    changingOrderToggle: any;
    attributes: AttributeSchema[];
    gridRef: any;
};

const ChangeOrderAction = ({ classificationDetail, changingOrder, changingOrderToggle, attributes, gridRef }) => {
    const [setIsLoading] = useLocalStore((state) => [state.setIsLoading]);

    const toggleChangeOrder = useCallback(async () => {
        if (changingOrder) {
            let attributeOrder: any = schemaHelper.parseOrderFromGridRows(gridRef);
            if (!isAttributeOrderEqual(classificationDetail.classification.attributeOrder, attributeOrder)) {
                setIsLoading(true);
                await updateAttributeOrder(classificationDetail.classification, attributeOrder);
                setIsLoading(false);
            }
        }
        changingOrderToggle.toggle();
        clearTableStates(gridRef);
    }, [changingOrder, classificationDetail]);

    const onCancel = useCallback(() => {
        changingOrderToggle.close();
    }, []);

    return (
        <>
            <Button
                variant="contained"
                color="secondary"
                size="xs"
                endIcon={changingOrder ? <SaveIcon /> : <AttributeOrderIcon />}
                onClick={toggleChangeOrder}
            >
                {changingOrder ? 'Save Order' : 'Change Order'}
            </Button>
            {changingOrder && (
                <Button onClick={onCancel} variant="contained" color="secondary" size="xs" endIcon={<CloseIcon />}>
                    Cancel
                </Button>
            )}
        </>
    );
};

export default ChangeOrderAction;
