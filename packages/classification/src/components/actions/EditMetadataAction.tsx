/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useMemo, useRef } from 'react';
import { IClassificationDetail, formHelper, useToggle } from 'ui-common';
import {
    Box,
    EditIcon,
    FormikCheckBox,
    FormikTextField,
    IconButton,
    MainTooltip,
    RightTray,
    Typography,
} from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import * as yup from 'yup';
import { Formik, Form, Field } from 'formik';
import { getClassificationInfo } from '../../utils/helper';
import { updateClassification } from '../../actions';
import useLocalStore from '../../store/useLocalStore';
import { useClassificationDetail, useClassificationTree } from '@tripudiotech/admin-caching-store';
import { useNavigate } from 'react-router-dom';

export type EditMetadataActionProps = {
    classificationDetail: IClassificationDetail;
};

const validationSchema = yup.object().shape({
    name: yup
        .string()
        .typeError(formHelper.buildRequiredMessage('Name'))
        .required(formHelper.buildRequiredMessage('Name')),
    description: yup
        .string()
        .typeError(formHelper.buildRequiredMessage('Description'))
        .required(formHelper.buildRequiredMessage('Description')),
});

const EditMetadataAction = ({ classificationDetail }: EditMetadataActionProps) => {
    const [open, openToggle] = useToggle(false);
    const getClassification = useClassificationDetail((state) => state.getClassification);
    const { getClassificationTree, classificationTreeMap } = useClassificationTree();
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const navigate = useNavigate();
    const formRef = useRef(null);
    const classificationName = useMemo(
        () => getClassificationInfo(classificationDetail, 'name'),
        [classificationDetail]
    );
    const initialValues = useMemo(
        () => ({
            name: classificationName,
            description: getClassificationInfo(classificationDetail, 'description'),
            allowAccessOverride: classificationTreeMap[classificationName]?.allowAccessOverride,
        }),
        [classificationDetail, classificationTreeMap?.[classificationName]?.allowAccessOverride]
    );
    const handleSubmit = useCallback(
        async (values: any) => {
            if (formRef.current.dirty) {
                setIsLoading(true);
                const res = await updateClassification(classificationName, values);
                await getClassification(values.name);
                setIsLoading(false);
                if (res) {
                    if (values.name !== classificationName) {
                        // Navigate to the new classification name
                        navigate(`/classification/${values.name}/metadata`);
                    }
                    getClassificationTree(true);
                    openToggle.close();
                }
            } else {
                openToggle.close();
            }
        },
        [classificationName]
    );

    return (
        <>
            <IconButton size="small" onClick={openToggle.open}>
                <EditIcon />
            </IconButton>
            <RightTray
                title={`Edit ${classificationName} Information`}
                componentName={COMPONENT_NAME.EDIT_METADATA}
                open={open}
                onClose={openToggle.close}
                onConfirm={() => formRef.current.submitForm()}
                confirmText="Save changes"
                disableCloseOnClickOutside
            >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', margin: '16px' }}>
                    <Typography
                        variant="label2-med"
                        sx={{
                            color: (theme) => theme.palette.info.main,
                        }}
                    >
                        General Information
                    </Typography>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        innerRef={formRef}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleSubmit(values as any);
                            setSubmitting(false);
                        }}
                    >
                        {({ values, setFieldValue, ...rest }) => {
                            return (
                                <Form
                                    id="metadata-form"
                                    style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
                                >
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Name"
                                        name="name"
                                        variant="outlined"
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Description"
                                        name="description"
                                        variant="outlined"
                                        multiline
                                        minRows={2}
                                    />
                                    <Field
                                        fullWidth
                                        name="allowAccessOverride"
                                        defaultChecked={classificationTreeMap[classificationName]?.allowAccessOverride}
                                    >
                                        {(fieldProps) => (
                                            <MainTooltip
                                                title={
                                                    classificationTreeMap[classificationName]?.accessOverrideDisabledBy
                                                        ? `Access override cannot be enabled as it has been disabled by ${classificationTreeMap[classificationName]?.accessOverrideDisabledBy}`
                                                        : 'Toggle Access Override. Disabling this would block updating classification access on all sub classification'
                                                }
                                            >
                                                <span style={{ width: 'fit-content' }}>
                                                    <FormikCheckBox
                                                        type="checkbox"
                                                        disabled={
                                                            classificationTreeMap[classificationName]
                                                                ?.accessOverrideDisabledBy
                                                        }
                                                        defaultChecked={
                                                            classificationTreeMap[classificationName]
                                                                ?.allowAccessOverride
                                                        }
                                                        {...fieldProps}
                                                        Label={{ label: 'Allow Access Override' }}
                                                    />
                                                </span>
                                            </MainTooltip>
                                        )}
                                    </Field>
                                </Form>
                            );
                        }}
                    </Formik>
                </Box>
            </RightTray>
        </>
    );
};

export default EditMetadataAction;
