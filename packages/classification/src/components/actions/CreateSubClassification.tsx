/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { formHelper, IClassificationDetail, useToggle } from 'ui-common';
import { Box, Button, FormikTextField, MainTooltip, PlusIcon, RightTray, Typography } from 'ui-style';
import { expandClassification, getClassificationInfo } from '../../utils/helper';
import * as yup from 'yup';
import { COMPONENT_NAME } from '../../constants/component-name';
import { Formik, Form, Field } from 'formik';
import { createSubClassification } from '../../actions';
import { useClassificationTree } from '@tripudiotech/admin-caching-store';
import useLocalStore from '../../store/useLocalStore';
import { useNavigate } from 'react-router-dom';

const CreateSubClassification = ({
    classificationDetail,
    isRoot = false,
}: {
    classificationDetail?: IClassificationDetail;
    isRoot?: boolean;
}) => {
    const getClassificationTree = useClassificationTree((state) => state.getClassificationTree);
    const [open, openToggle] = useToggle(false);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const navigate = useNavigate();
    const formRef = useRef(null);
    const classificationName = useMemo(
        () => getClassificationInfo(classificationDetail, 'name'),
        [classificationDetail]
    );

    const initialValues = useMemo(
        () => ({
            name: '',
            description: '',
        }),
        []
    );

    const validationSchema = useMemo(
        () =>
            yup.object().shape({
                name: yup
                    .string()
                    .typeError(formHelper.buildRequiredMessage('Name'))
                    .required(formHelper.buildRequiredMessage('Name')),
                description: yup
                    .string()
                    .required(formHelper.buildRequiredMessage('Description'))
                    .typeError(formHelper.buildRequiredMessage('Description')),
            }),
        []
    );

    const handleSubmit = useCallback(
        async (values) => {
            setIsLoading(true);
            const res = await createSubClassification(isRoot ? 'ROOT' : classificationName, values);
            if (res) {
                getClassificationTree(true);
                navigate(`/classification/${values.name}/metadata`);
                openToggle.close();
                if (!isRoot) {
                    expandClassification(classificationName);
                }
            }
            setIsLoading(false);
        },
        [classificationName, isRoot]
    );

    const title = isRoot
        ? `Create new root classification`
        : `Create sub classification of ${getClassificationInfo(classificationDetail, 'name')}`;
    return (
        <>
            <MainTooltip title={title}>
                <span>
                    <Button variant="contained-white" size="xs" endIcon={<PlusIcon />} onClick={openToggle.open}>
                        {isRoot ? 'Create Classification' : 'Create Sub Classification'}
                    </Button>
                </span>
            </MainTooltip>
            <RightTray
                title={title}
                componentName={COMPONENT_NAME.CREATE_SUB_TYPE}
                open={open}
                onClose={openToggle.close}
                onConfirm={() => formRef.current.submitForm()}
                confirmText="Create"
                disableCloseOnClickOutside
            >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', margin: '16px' }}>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        innerRef={formRef}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleSubmit(values as any);
                            setSubmitting(false);
                        }}
                    >
                        {({ values, setFieldValue, ...rest }) => {
                            return (
                                <Form
                                    id="metadata-form"
                                    style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
                                >
                                    <Typography
                                        variant="label2-med"
                                        sx={{
                                            color: (theme) => theme.palette.info.main,
                                        }}
                                    >
                                        General Information
                                    </Typography>
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Name"
                                        name="name"
                                        variant="outlined"
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Description"
                                        name="description"
                                        variant="outlined"
                                    />
                                </Form>
                            );
                        }}
                    </Formik>
                </Box>
            </RightTray>
        </>
    );
};

export default CreateSubClassification;
