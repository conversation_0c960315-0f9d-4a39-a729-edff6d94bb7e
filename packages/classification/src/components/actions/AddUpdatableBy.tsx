/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useState, useEffect, useCallback } from 'react';
import { type IClassificationDetail, useToggle } from 'ui-common';
import {
    Box,
    Button,
    PlusIcon,
    RightTray,
    Typography,
    Autocomplete,
    TextField,
    CircularProgress,
    tableIcons,
    tableStyles,
} from 'ui-style';
import debounce from 'lodash/debounce';
import { Formik, Form } from 'formik';
import useLocalStore from '../../store/useLocalStore';
import { AGENT_SUBTYPE, buildOrOperatorQuery, buildContainsQuery, fetch, entityUrls } from '@tripudiotech/admin-api';
import { getClassificationInfo } from '../../utils/helper';
import { AgGridReact } from '@ag-grid-community/react';
import { COMPONENT_NAME } from '../../constants/component-name';
import * as yup from 'yup';
import { ClassificationAccessRequest, createUpdatableBy } from '../../actions';
import { type ColDef, type GridOptions, type ICellRendererParams } from '@ag-grid-community/core';

interface CreateUpdatableByProps {
    classificationDetail: IClassificationDetail;
    disabled: boolean;
}

interface AgentOptionType {
    name: string;
    id: string;
}

const getOptions = (): AgentOptionType[] => {
    return Object.keys(AGENT_SUBTYPE).map((key) => ({
        name: AGENT_SUBTYPE[key],
        id: key,
    }));
};

const validationSchema = yup.object().shape({
    updatableByAgentIds: yup.array().min(1, 'You must select at least one agent name'),
    agentType: yup.string().required('Agent Type is required'),
});

interface CreateClassificationAccessAgent {
    classificationAccess: ClassificationAccessRequest[];
    agentType: string;
}

const initialValues: CreateClassificationAccessAgent = {
    classificationAccess: [],
    agentType: '',
};

const COL_DEFS: ColDef[] = [
    {
        field: 'agentName',
        headerName: 'Agent Name',
        filter: 'agTextColumnFilter',
    },
    {
        field: 'agentType',
        headerName: 'Agent Type',
        filter: 'agTextColumnFilter',
    },
    {
        field: 'canRead',
        headerName: 'Can Read',
        cellRenderer: (params: ICellRendererParams) => {
            return (
                <input
                    checked={params.data.canRead}
                    type="checkbox"
                    name="canRead"
                    onChange={(event) => {
                        params.node.setDataValue('canRead', event.target.checked);
                    }}
                />
            );
        },
    },
    {
        field: 'canEdit',
        headerName: 'Can Edit',
        cellRenderer: (params: ICellRendererParams) => {
            return (
                <input
                    checked={params.data.canEdit}
                    type="checkbox"
                    name="canEdit"
                    onChange={(event) => {
                        params.node.setDataValue('canEdit', event.target.checked);
                    }}
                />
            );
        },
    },
];

const GRID_OPTIONS: GridOptions = {
    animateRows: true,
    defaultColDef: {
        sortable: false,
        resizable: true,
        filter: true,
        floatingFilter: false,
        editable: false,
        cellStyle: () => ({
            display: 'block',
        }),
        flex: 1,
    },
    getRowId: (params) => {
        return params.data.agentId;
    },
    rowModelType: 'clientSide',
    rowSelection: 'multiple',
    headerHeight: 32,
    rowHeight: 32,
    pagination: false,
    suppressRowClickSelection: true,
    suppressPaginationPanel: true,
    suppressRowDrag: true,
    icons: tableIcons,
};

export const CreateUpdatableBy: React.FC<CreateUpdatableByProps> = ({ classificationDetail, disabled }) => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const classificationName = getClassificationInfo(classificationDetail, 'name');
    const [agents, setAgents] = useState<AgentOptionType[]>([]);
    const [agentType, setAgentType] = useState(null);
    const [isAgentsLoading, setAgentsLoading] = useState(false);
    const [agentSearchText, setAgentSearchText] = useState('');
    const gridRef = useRef<AgGridReact>(null);
    const [showPermissionsGrid, setShowPermissionGrid] = useState(false);

    const onSubmit = useCallback(
        (values, { setSubmitting }) => {
            if (formRef.current.dirty) {
                setIsLoading(true);
                const newClassificationAccessAgents: ClassificationAccessRequest[] =
                    gridRef.current.api
                        ?.getRenderedNodes()
                        .map((node) => node.data)
                        ?.map((agent) => ({
                            agentId: agent.agentId,
                            canRead: agent.canRead,
                            canEdit: agent.canEdit,
                        })) ?? [];
                if (newClassificationAccessAgents.length > 0) {
                    createUpdatableBy(classificationName, newClassificationAccessAgents)
                        .then((res) => {
                            if (res) {
                                openToggle.close();
                                const refreshProcessListEvent = new CustomEvent('refresh-updatable-by-grid', {
                                    detail: true,
                                });
                                window.dispatchEvent(refreshProcessListEvent);
                            }
                        })
                        .catch((error) => {
                            console.error(error);
                        })
                        .finally(() => {
                            setIsLoading(false);
                            setSubmitting(false);
                            setShowPermissionGrid(false);
                            gridRef.current.api.setRowData([]);
                            setAgents([]);
                            setAgentType(null);
                        });
                }
            } else {
                openToggle.close();
            }
        },
        [formRef, gridRef.current]
    );

    const buildSearchQuery = useCallback((search) => {
        const searchText = search || '';
        return buildOrOperatorQuery([
            buildContainsQuery('name', searchText),
            buildContainsQuery('description', searchText),
        ]);
    }, []);

    const getAgentsWithFilter = useCallback(
        debounce((searchText) => {
            setAgentsLoading(true);
            fetch({
                ...entityUrls.getListEntity,
                params: {
                    entityType: agentType,
                },
                qs: {
                    query: JSON.stringify(buildSearchQuery(searchText)),
                    limit: 100,
                },
            })
                .then((response) => {
                    if (response.status !== 200) {
                        setAgents([]);
                    } else {
                        const agentOptions =
                            response.data.data?.map((agent) => ({
                                name: agent.properties.name,
                                id: agent.id,
                            })) ?? [];
                        if (formRef.current !== null) {
                            setAgents([...agentOptions, ...formRef.current.values.classificationAccess]);
                        }
                    }
                })
                .finally(() => {
                    setAgentsLoading(false);
                });
        }, 400),
        [agentType]
    );

    return (
        <>
            <Button
                variant="contained"
                color="secondary"
                onClick={openToggle.open}
                size="xs"
                endIcon={<PlusIcon />}
                disabled={disabled}
            >
                Add New
            </Button>
            <RightTray
                title={`Update Classification Access for ${classificationName}`}
                componentName={COMPONENT_NAME.ADD_UPDATABLE_BY}
                open={open}
                onClose={() => {
                    openToggle.close();
                    setAgents([]);
                    setAgentType(null);
                }}
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={() => formRef.current.submitForm()}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <Typography
                        variant="label2-med"
                        sx={{
                            color: (theme) => theme.palette.info.main,
                        }}
                    >
                        Select Agent Type
                    </Typography>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        innerRef={formRef}
                        validationSchema={validationSchema}
                        onSubmit={onSubmit}
                    >
                        {({ setFieldValue, errors, touched }) => (
                            <Form id="metadata-form" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                                <Autocomplete
                                    options={getOptions()}
                                    getOptionLabel={(option: AgentOptionType) => option.name}
                                    isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
                                    id="agent-type-updatable-by"
                                    onChange={(e, value: AgentOptionType) => {
                                        setFieldValue('agentType', value.name);
                                        if (value !== null) {
                                            setAgentType(value.name);
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            label="Agent Type"
                                            size="small"
                                            error={Boolean(errors.agentType && touched.agentType)}
                                            helperText={errors.agentType && touched.agentType ? errors.agentType : ''}
                                            required
                                        />
                                    )}
                                />

                                <Typography
                                    variant="label2-med"
                                    sx={{
                                        color: (theme) => theme.palette.info.main,
                                    }}
                                >
                                    Select Agents
                                </Typography>
                                <Autocomplete
                                    disabled={agentType === null}
                                    options={agents}
                                    inputValue={agentSearchText}
                                    onInputChange={(event, value) => {
                                        setAgentSearchText(value);
                                        if (value?.length > 2) {
                                            getAgentsWithFilter(value);
                                        }
                                    }}
                                    filterOptions={(x) => x}
                                    getOptionLabel={(option: AgentOptionType) => option.name}
                                    isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
                                    id=""
                                    multiple
                                    onChange={(e, values) => {
                                        const newAgentIdsToNameMap = new Map();
                                        values.forEach((value) => newAgentIdsToNameMap.set(value.id, value.name));
                                        const newAgentIds = Array.from(newAgentIdsToNameMap.keys());
                                        const existingAgentIds = gridRef.current.api
                                            .getRenderedNodes()
                                            .map((row) => row.data.agentId);
                                        const rowData =
                                            gridRef.current.api
                                                .getRenderedNodes()
                                                .filter((row) => newAgentIds.includes(row.data?.agentId))
                                                .map((row) => row.data) ?? [];
                                        newAgentIds.forEach((newAgentId) => {
                                            if (!existingAgentIds.includes(newAgentId)) {
                                                rowData.push({
                                                    agentName: newAgentIdsToNameMap.get(newAgentId),
                                                    agentType: agentType,
                                                    agentId: newAgentId,
                                                    canRead: false,
                                                    canEdit: false,
                                                });
                                            }
                                        });
                                        if (rowData?.length > 0) {
                                            setShowPermissionGrid(true);
                                        } else {
                                            setShowPermissionGrid(false);
                                        }
                                        gridRef.current.api.setRowData(rowData);
                                    }}
                                    noOptionsText={
                                        agentSearchText?.length < 2
                                            ? 'Enter at least two characters for options'
                                            : 'No Options'
                                    }
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            label="Agent Name"
                                            size="small"
                                            value={agentSearchText}
                                            error={Boolean(errors.classificationAccess && touched.classificationAccess)}
                                            helperText={
                                                errors.classificationAccess && touched.classificationAccess
                                                    ? errors.classificationAccess
                                                    : ''
                                            }
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <React.Fragment>
                                                        {isAgentsLoading ? (
                                                            <CircularProgress color="info" size={20} />
                                                        ) : null}
                                                        {params.InputProps.endAdornment}
                                                    </React.Fragment>
                                                ),
                                            }}
                                            required
                                        />
                                    )}
                                />
                            </Form>
                        )}
                    </Formik>
                    <Box
                        sx={{
                            height: '100%',
                            display: showPermissionsGrid ? 'flex' : 'none',
                            flexDirection: 'column',
                            ...tableStyles,
                        }}
                    >
                        <Typography
                            variant="label2-med"
                            sx={{
                                color: (theme) => theme.palette.info.main,
                                marginBottom: '12px',
                            }}
                        >
                            Set Permissions
                        </Typography>
                        <div className=" ag-theme-alpine" style={{ height: '100%' }}>
                            <AgGridReact ref={gridRef} columnDefs={COL_DEFS} {...GRID_OPTIONS} />
                        </div>
                    </Box>
                </Box>
            </RightTray>
        </>
    );
};
