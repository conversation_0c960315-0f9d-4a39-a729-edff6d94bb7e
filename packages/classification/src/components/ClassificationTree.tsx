/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CloseIcon, InputAdornment, SearchIcon, TextField, Typography, ClassificationIcon } from 'ui-style';
import { AgGridReact } from '@ag-grid-community/react';
import isEmpty from 'lodash/isEmpty';
import { getContextMenuCallback, getIndentClass } from '../utils/ClassificationTreeGridConfig';
import get from 'lodash/get';
import TreeContainer from './TreeContainer';
import { generatePath, Link, useMatch } from 'react-router-dom';
import { RowNode } from '@ag-grid-community/core';
import { ClassificationTreeNode } from 'ui-common';
import { EXPAND_CLASSIFICATION } from '../constants';
import debounce from 'lodash/debounce';
const PATH_PATTERN = 'classification/:classificationName/*';

const ClassificationItemRenderer = ({ value, data }) => {
    const classificationName = get(data, 'name');
    const matched = useMatch(PATH_PATTERN);
    const params: any = get(matched, 'params', {});
    const path = generatePath(PATH_PATTERN, {
        ...params,
        classificationName,
    });
    return (
        <Link className="item" to={`/${path}`}>
            <ClassificationIcon style={{ width: '16px', height: '16px' }} />
            <Typography variant="label2-sem">{value}</Typography>
        </Link>
    );
};

const ClassificationTree = ({ classificationTree }) => {
    const [searchText, setSearchText] = useState('');
    const gridRef = useRef<AgGridReact>();
    const matched = useMatch('classification/:classificationName/:view');
    const selectedType = get(matched, ['params', 'classificationName']);

    const onSearchChanged = useCallback((e) => {
        const { value } = e.target;
        setSearchText(value);
    }, []);

    const getDataPath = useCallback((data) => {
        return data.path || [];
    }, []);

    const isGroupOpenByDefault = useCallback((params) => {
        return params.level === 0;
    }, []);

    const onRowGroupOpened = useCallback(() => {
        gridRef.current.columnApi.autoSizeAllColumns();
    }, []);

    const autoGroupColumnDef = useMemo(
        () => ({
            field: 'name',
            headerName: 'Name',
            cellClass: getIndentClass,
            minWidth: 308,
            filter: 'agTextColumnFilter',
            cellRendererParams: {
                innerRenderer: ClassificationItemRenderer,
                // onClick: onSelectChange,
                suppressCount: true,
            },
            cellStyle: (params) => {
                const { level } = params.node;
                const groupCell = params.value === params.node.key;
                const indent = 24; // change this value to your liking

                return groupCell
                    ? {
                          border: 'none',
                          paddingLeft: '-' + indent * (level + 1) + 'px',
                      }
                    : {
                          border: 'none',
                      };
            },
        }),
        []
    );

    const expandSelectedType = useCallback(
        debounce(() => {
            if (selectedType && gridRef.current?.api) {
                let selectedNode: RowNode<ClassificationTreeNode>;
                gridRef.current.api.forEachNode((node: RowNode) => {
                    if (node.data.name === selectedType) {
                        selectedNode = node;
                        return;
                    }
                });
                selectedNode.setSelected(true);
                if (selectedNode) {
                    let parent = selectedNode;
                    while (parent) {
                        parent.setExpanded(true);
                        parent = parent.parent;
                    }
                }
            }
        }, 80),
        [selectedType]
    );

    const onFirstDataRendered = useCallback(() => {
        // expand and select by default
        expandSelectedType();
        gridRef.current.columnApi.applyColumnState({
            state: [{ colId: 'name', sort: 'asc' }],
        });
        onRowGroupOpened();
    }, [selectedType]);

    const gridOptions: any = useMemo(
        () => ({
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                filter: true,
                floatingFilter: false,
            },
            getRowId: (params) => {
                return params.data.name;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    hide: true,
                },
            ],
            components: {},
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            isGroupOpenByDefault,
            onRowGroupOpened,
            onExpandOrCollapseAll: onRowGroupOpened,
            getContextMenuItems: getContextMenuCallback(gridRef),
            getDataPath,
            treeData: true,
            onFirstDataRendered,
            getRowStyle: () => ({
                background: ' #FFFFFF',
                border: 'none',
            }),
            autoGroupColumnDef,
            gridOptions: {
                headerHeight: 0,
                rowHeight: 26,
            },
            enableCellChangeFlash: true,
        }),

        [gridRef]
    );

    useEffect(() => {
        if (gridRef.current?.api) {
            gridRef.current.api.setFilterModel({
                name: {
                    filterType: 'text',
                    type: 'contains',
                    filter: searchText,
                },
            });
        }
    }, [searchText]);

    useEffect(() => {
        if (classificationTree && selectedType && gridRef.current?.api) {
            gridRef.current.api.forEachNode((node) => {
                if (node.data.name === selectedType) {
                    gridRef.current.api.deselectAll();
                    node.setSelected(true);
                    return;
                }
            });
        }
    }, [classificationTree, selectedType]);

    useEffect(() => {
        const expandEntityType = (e) => {
            if (gridRef.current) {
                gridRef.current.api.forEachNode((node) => {
                    if (node.data.name === e?.detail.name) {
                        node.setExpanded(true);
                        return;
                    }
                });
            }
        };
        window.addEventListener(EXPAND_CLASSIFICATION, expandEntityType);

        return () => window.removeEventListener(EXPAND_CLASSIFICATION, expandEntityType);
    }, []);

    return (
        <TreeContainer>
            <TextField
                className="searchBox"
                size="medium"
                value={searchText}
                onChange={onSearchChanged}
                fullWidth
                placeholder="Type to search"
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <SearchIcon />
                        </InputAdornment>
                    ),
                    endAdornment: (
                        <InputAdornment
                            position="end"
                            style={{
                                visibility: isEmpty(searchText) ? 'hidden' : 'visible',
                                cursor: 'pointer',
                            }}
                            onClick={() => setSearchText('')}
                        >
                            <CloseIcon sx={{ width: '16px', height: '16px' }} />
                        </InputAdornment>
                    ),
                }}
            />
            <div className="agContainer ag-theme-alpine">
                <AgGridReact ref={gridRef} {...gridOptions} rowData={classificationTree} />
            </div>
        </TreeContainer>
    );
};

export default ClassificationTree;
