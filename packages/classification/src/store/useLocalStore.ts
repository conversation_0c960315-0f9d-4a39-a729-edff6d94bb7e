/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { ILifeCycleDetail, IAppliedProcess } from 'ui-common';
import { create } from 'zustand';
import { fetch, schemaUrls } from '@tripudiotech/admin-api';

type SchemaLocalStore = {
    isLoading: boolean;
    isLoadingLifecycles: boolean;
    isLoadingProcess: boolean;
    lifecycles: ILifeCycleDetail[];
    appliedProcess: IAppliedProcess[];
    setIsLoading: (value: boolean) => void;
    getSchemaLifecycles: (entityType: string, signal?: any, force?: boolean) => void;
    getSchemaAppliedProcess: (entityType: string, signal?: any, force?: boolean) => void;
    clearStore: () => void;
};

const initialStates = {
    isLoading: false,
    isLoadingLifecycles: false,
    isLoadingProcess: false,
    lifecycles: null,
    appliedProcess: null,
};

const useLocalStore = create<SchemaLocalStore>((set, get) => ({
    ...initialStates,
    setIsLoading: (isLoading: boolean) => {
        set({ isLoading });
    },
    getSchemaLifecycles: async (entityTypeName: string, signal?: any, force = false) => {
        if (get().lifecycles && !force) return;

        set({ isLoadingLifecycles: true });

        try {
            const { data } = await fetch({
                ...schemaUrls.fetchEntityLifecycle,
                params: { entityTypeName },
            });
            set({ lifecycles: data.data });
        } catch (error) {
            console.error(error);
        } finally {
            set({ isLoadingLifecycles: false });
        }
    },
    getSchemaAppliedProcess: async (entityType: string, signal?: any, force = false) => {
        if (get().appliedProcess && !force) return;
        console.log('LOADING PRROCESS');
        set({ isLoadingProcess: true });

        try {
            const { data } = await fetch({
                ...schemaUrls.getAppliedProcess,
                params: { entityType },
                signal,
            });
            set({ appliedProcess: data });
        } catch (error) {
            console.error(error);
        } finally {
            set({ isLoadingProcess: false });
        }
    },
    clearStore: () => {
        set({ ...initialStates });
    },
}));

export default useLocalStore;
