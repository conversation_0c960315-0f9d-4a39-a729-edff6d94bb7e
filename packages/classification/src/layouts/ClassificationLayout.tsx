/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo } from 'react';
import { AnimatedPage, Loading, MoreIcon, styled, Typography, TabGroup, MainMenuList, DeleteIcon } from 'ui-style';
import { IDialog, IClassificationDetail } from 'ui-common';
import { useDialog, useClassificationTree, useClassificationDetail } from '@tripudiotech/admin-caching-store';
import { Outlet, useMatch, useNavigate, useParams } from 'react-router-dom';
import get from 'lodash/get';
import { CLASSIFICATION_DETAIL_TABS } from '../constants/tabs';
import useLocalStore from '../store/useLocalStore';
import { getClassificationInfo } from '../utils/helper';
import CreateSubClassification from '../components/actions/CreateSubClassification';
import { deleteClassification } from '../actions';

const ClassificationDetailContainer = styled('div')(({ theme }) => {
    const glideTheme = theme.palette.glide;
    return {
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        '& .headerContainer': {
            '& .primaryContent': {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
            },
            padding: '16px',
            borderBottom: `1px solid ${glideTheme.stroke.normal.primary}`,
            '& .subActions': {
                display: 'flex',
                gap: '8px',
                '& .moreBtn': {
                    backgroundColor: glideTheme.background.normal.blue2,
                },
            },
        },
        '& .mainContent': {
            height: '100%',
        },
    };
});

const ClassificationLayout = () => {
    const { classificationName } = useParams();
    const [detailClassification, getClassification]: [
        IClassificationDetail,
        (entityName: string, signal?: any) => Promise<IClassificationDetail>
    ] = useClassificationDetail((state) => [state.classification[classificationName], state.getClassification]);
    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const [setIsLoading, clearLocalStore] = useLocalStore((state) => [state.setIsLoading, state.clearStore]);

    const [isLoadedClassificationTree, getClassificationTree, classificationTreeMap] = useClassificationTree(
        (state) => [state.isLoaded, state.getClassificationTree, state.classificationTreeMap]
    );

    const hasSubTypes = useMemo(() => {
        return Object.values(classificationTreeMap).some(
            (classification: any) =>
                classification.path.includes(classificationName) && classificationName !== classification.name
        );
    }, [classificationTreeMap, classificationName]);

    const matched = useMatch('classification/:classificationName/:view/*');
    const navigate = useNavigate();
    const view = get(matched, ['params', 'view']);
    const isSystem = getClassificationInfo(detailClassification, 'system');

    useEffect(() => {
        let abortController = new AbortController();
        if (classificationName) {
            getClassification(classificationName, abortController.signal);
        }
        return () => {
            abortController.abort();
            clearLocalStore();
        };
    }, [classificationName]);

    const handleDelete = useCallback(async () => {
        setIsLoading(true);
        const res = await deleteClassification(classificationName);

        if (res) {
            navigate('../');
            await getClassificationTree(true);
        }
        setIsLoading(false);
    }, [classificationName]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete entity type',
            `Do you really want to delete the classification ${classificationName}`,
            handleDelete,
            'error'
        );
    }, [detailClassification]);

    return detailClassification ? (
        <AnimatedPage style={{ height: '100%' }}>
            <ClassificationDetailContainer id="classification-detail">
                <div className="headerContainer">
                    <div className="primaryContent">
                        <div className="info">
                            <Typography variant="label1-sem">{classificationName}</Typography>
                        </div>
                        <div className="subActions">
                            <CreateSubClassification classificationDetail={detailClassification} />

                            <MainMenuList
                                btnIcon={<MoreIcon sx={{ transform: 'rotate(90deg)' }} />}
                                items={[
                                    {
                                        id: 'delete-menu-item',
                                        label: 'Delete',
                                        icon: (
                                            <DeleteIcon
                                                id="delete-icon"
                                                key="delete-icon"
                                                sx={{ ml: '16px' }}
                                                color="error"
                                            />
                                        ),
                                        onClick: onDelete,
                                        disabled: isSystem || hasSubTypes,
                                        title: isSystem
                                            ? `You are not allowed to delete system Classification`
                                            : hasSubTypes
                                            ? `You are not allowed to delete Classification which has sub classifications`
                                            : ``,
                                    },
                                ]}
                            />
                        </div>
                    </div>
                    <TabGroup
                        value={view}
                        variant="scrollable"
                        scrollButtons="auto"
                        allowScrollButtonsMobile
                        aria-label="classification navigation tabs"
                        items={CLASSIFICATION_DETAIL_TABS}
                        size="small"
                        style={{
                            marginTop: '8px',
                        }}
                    />
                </div>
                <div className="mainContent">
                    <Outlet />
                </div>
            </ClassificationDetailContainer>
        </AnimatedPage>
    ) : (
        isLoadedClassificationTree && <Loading />
    );
};

export default ClassificationLayout;
