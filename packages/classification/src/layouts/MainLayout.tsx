/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo } from 'react';
import {
    <PERSON>,
    Button,
    ContentHeader,
    FixedHeightContainer,
    styled,
    AnimatedPage,
    LoadingOverlay,
    tableStyles,
} from 'ui-style';
import { useClassificationTree } from '@tripudiotech/admin-caching-store';
import SchemaTree from '../components/ClassificationTree';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { Outlet } from 'react-router-dom';
import useLocalStore from '../store/useLocalStore';
import CreateSubClassification from '../components/actions/CreateSubClassification';

const SchemaActions = () => {
    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <CreateSubClassification isRoot />
            {/* <Button variant="contained" color="secondary" size="small">
                Import Schema
            </Button>
            <Button variant="contained" color="secondary" size="small">
                Export Schema
            </Button> */}
        </Box>
    );
};

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const MainLayout = () => {
    const { isLoaded, getClassificationTree, classificationTreeMap } = useClassificationTree();
    const [isLoading] = useLocalStore((state) => [state.isLoading]);

    const schemaTree = useMemo(
        () => (classificationTreeMap ? Object.values(classificationTreeMap) : null),
        [classificationTreeMap]
    );

    useEffect(() => {
        getClassificationTree();
    }, []);
    return isLoaded ? (
        <AnimatedPage>
            {isLoading && <LoadingOverlay />}
            <FixedHeightContainer>
                <ContentHeader title="Classification" actionsRenderer={SchemaActions} />
                <ContentWrapper>
                    <PanelGroup direction="horizontal" autoSaveId="schema-panels">
                        <Panel
                            id="classification-tree-panel"
                            defaultSize={33}
                            minSize={5}
                            maxSize={80}
                            style={{ height: '100%', position: 'relative' }}
                        >
                            <SchemaTree classificationTree={schemaTree} />
                            <PanelResizeHandle className="handle"></PanelResizeHandle>
                        </Panel>

                        <Panel id="classification-detail-panel" style={{ height: '100%', position: 'relative' }}>
                            <Outlet />
                        </Panel>
                    </PanelGroup>
                </ContentWrapper>
            </FixedHeightContainer>
        </AnimatedPage>
    ) : (
        <LoadingOverlay />
    );
};

export default MainLayout;
