/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { fetch, schemaUrls, classificationUrls } from '@tripudiotech/admin-api';

export interface ClassificationAccessRequest {
    agentId: string;
    canRead: boolean;
    canEdit: boolean;
}

export const updateAttributeOrder = async (classification, newOrder: string[]) => {
    try {
        const request = {
            attributeOrder: newOrder,
            name: classification.name,
            description: classification.description,
            disabled: classification.disabled,
        };

        const response = await updateClassification(classification.name, request);
        return response;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const updateClassification = async (name, classification) => {
    try {
        await fetch({
            ...classificationUrls.updateClassification,
            params: { name },
            data: classification,
            successMessage: `Successfully updated classification ${name}`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    }
};

export const applyClassification = async (entityType: string, data) => {
    try {
        await fetch({
            ...schemaUrls.applyClassification,
            data,
            params: {
                entityType,
            },
            successMessage: `Successfully applied classifications for <b>${entityType}</b>`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    }
};

export const deleteIdentifier = async (entityType, attributeName) => {
    try {
        await fetch({
            ...schemaUrls.deleteAttributeIdentifier,
            params: { entityType, attributeName },
            successMessage: `Auto generated value for attribute <b>${attributeName}</b> has been deleted successfully.`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};
export const createIdentifier = async (entityType, attributeName, values) => {
    try {
        await fetch({
            ...schemaUrls.createAttributeIdentifier,
            params: { entityType, attributeName },
            data: values,
            successMessage: `Auto generated value for attribute <b>${attributeName}</b> has been created successfully.`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const updateIdentifier = async (entityType, attributeName, values) => {
    try {
        await fetch({
            ...schemaUrls.updateAttributeIdentifier,
            params: { entityType, attributeName },
            data: values,
            successMessage: `Auto generated value for attribute <b>${attributeName}</b> has been updated successfully.`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const createClassificationAttribute = async (classificationName, attributes) => {
    try {
        const attributeNames = attributes.map((attribute) => attribute.displayName).join(', ');
        await fetch({
            ...classificationUrls.createClassificationAttribute,
            params: { classificationName },
            data: attributes,
            successMessage: `Successfully added attribute ${attributeNames} to ${classificationName}`,
        });
        return true;
    } catch (e) {
        console.error(e);
        return false;
    }
};

export const updateClassificationAttribute = async (classificationName, attributeName, attribute) => {
    try {
        await fetch({
            ...classificationUrls.updateAttribute,
            params: { classificationName, attributeName },
            data: attribute,
            successMessage: `Successfully updated attribute ${attributeName} of ${classificationName}`,
        });
        return true;
    } catch (e) {
        console.error(e);
        return false;
    }
};

export const deleteClassificationAttribute = async (classificationName, attributeName) => {
    try {
        await fetch({
            ...classificationUrls.deleteAttribute,
            params: { classificationName, attributeName },
            successMessage: `Successfully deleted attribute ${attributeName} to ${classificationName}`,
        });
        return true;
    } catch (e) {
        console.error(e);
    }
};

export const deleteClassification = async (name: string) => {
    try {
        await fetch({
            ...classificationUrls.deleteClassification,
            params: { name },
            successMessage: `Classification <b>${name}<b> has been deleted successfully`,
        });
        return true;
    } catch (err) {
        console.error(err);
        return false;
    }
};

export const createSubClassification = async (parent: string, classification) => {
    try {
        await fetch({
            ...classificationUrls.createClassification,
            params: { name: parent },
            data: classification,
            successMessage: `Classification <b>${classification.name}</b> has been created successfully`,
        });
        return true;
    } catch (err) {
        console.error(err);
        return false;
    }
};

export const createUpdatableBy = async (name: string, agents: ClassificationAccessRequest[]) => {
    try {
        await fetch({
            ...classificationUrls.createUpdatableBy,
            params: { classificationName: name },
            data: agents,
            successMessage: `Successfuly updated classification access for <b>${name}</b>`,
        });
        return true;
    } catch (err) {
        console.error(err);
        return false;
    }
};

export const deleteUpdatableBy = async (name: string, agents: string[]) => {
    try {
        await fetch({
            ...classificationUrls.deleteClassificationUpdatableBy,
            params: { classificationName: name },
            data: agents,
            successMessage: `Successfuly removed agent(s) from acessing classification <b>${name}</b>`,
            errorMessage: `Error removing agent(s) from accessing classification for <b>${name}</b>`,
        });
        return true;
    } catch (err) {
        console.error(err);
        return false;
    }
};
