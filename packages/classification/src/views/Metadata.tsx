/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { AnimatedPage, BooleanField, Grid, SchemaField, styled, Typography } from 'ui-style';
import { useClassificationDetail, useClassificationTree } from '@tripudiotech/admin-caching-store';
import { useParams } from 'react-router-dom';
import { formatDateTime, IClassificationDetail } from 'ui-common';
import { getClassificationInfo } from '../utils/helper';
import EditMetadataAction from '../components/actions/EditMetadataAction';

const MetaDataContainer = styled('div')(({ theme }) => ({
    margin: '16px',
    '& .sectionHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        gap: '16px',
    },
}));

const Metadata = () => {
    const { classificationName } = useParams();
    const classificationDetail: IClassificationDetail = useClassificationDetail(
        (state) => state.classification[classificationName]
    );
    const { classificationTreeMap } = useClassificationTree();
    return (
        <AnimatedPage>
            <MetaDataContainer>
                <div className="sectionHeader">
                    <Typography variant="ol1" style={{ marginBottom: '8px' }}>
                        GENERAL INFORMATION
                    </Typography>
                    <EditMetadataAction classificationDetail={classificationDetail} />
                </div>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <SchemaField label="Name" value={getClassificationInfo(classificationDetail, 'name')} />
                        <SchemaField
                            label="Description"
                            value={getClassificationInfo(classificationDetail, 'description')}
                        />
                        <BooleanField label="System" value={getClassificationInfo(classificationDetail, 'system')} />
                        <BooleanField
                            label="Allow Access Override"
                            labelTitle="Indicates if the existing accessors can be overriden for sub classifications"
                            value={classificationTreeMap[classificationName]?.allowAccessOverride}
                        />
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <SchemaField
                            label="Created Date"
                            value={formatDateTime(getClassificationInfo(classificationDetail, 'createdAt'))}
                        />
                        <SchemaField
                            label="Updated Date"
                            value={formatDateTime(getClassificationInfo(classificationDetail, 'updatedAt'))}
                        />
                        <SchemaField
                            label="Created By"
                            value={getClassificationInfo(classificationDetail, 'createdBy')}
                        />
                    </Grid>
                </Grid>
            </MetaDataContainer>
        </AnimatedPage>
    );
};

export default Metadata;
