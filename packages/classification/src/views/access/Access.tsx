/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import {
    AnimatedPage,
    styled,
    Button,
    MinusIcon,
    Loading,
    tableIcons,
    AGGridTablePagination,
    CircularProgress,
    NoRowsOverlay,
    Typography,
} from 'ui-style';
import { AgGridReact } from '@ag-grid-community/react';
import get from 'lodash/get';
import { DEFAULT_TABLE_PAGINATION_SIZE, buildQueryBasedOnFilter, IClassificationDetail } from 'ui-common';

import { useClassificationDetail, useClassificationTree } from '@tripudiotech/admin-caching-store';
import {
    type GridOptions,
    type ColDef,
    type IServerSideGetRowsParams,
    type ITooltipParams,
    type CheckboxSelectionCallbackParams,
    type ICellRendererParams,
    type RowClassParams,
} from '@ag-grid-community/core';
import { classificationUrls, fetch } from '@tripudiotech/admin-api';
import { useParams } from 'react-router-dom';
import { CreateUpdatableBy } from '../../components/actions/AddUpdatableBy';
import { ClassificationAccessRequest, createUpdatableBy, deleteUpdatableBy } from '../../actions';

const gridOptions: GridOptions = {
    animateRows: true,
    getContextMenuItems: () => ['expandAll', 'contractAll', 'separator', 'copy', 'export'],
    defaultColDef: {
        sortable: false,
        resizable: true,
        filter: true,
        floatingFilter: false,
        editable: false,
        cellStyle: () => ({
            display: 'block',
        }),
        flex: 1,
    },
    getRowId: (params) => {
        return params.data.id;
    },
    rowModelType: 'serverSide',
    rowSelection: 'multiple',
    headerHeight: 32,
    rowHeight: 32,
    enableGroupEdit: true,
    suppressRowClickSelection: true,
    suppressPaginationPanel: true,
    pagination: true,
    loadingOverlayComponent: Loading,
    suppressRowDrag: true,
    cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
    icons: tableIcons,
};

const AccessControlContainer = styled('div')(({ theme }) => ({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    '& .sectionHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        gap: '16px',
    },
    '& .toolbar': {
        gap: '8px',
        display: 'flex',
        margin: '8px 16px',
        [theme.breakpoints.down('md')]: {
            '& .MuiButton-endIcon': {
                display: 'none',
            },
        },
    },
    '& .access': {
        height: '100%',
        width: '100%',
    },
}));

const Access = () => {
    const { classificationName } = useParams();
    const classificationDetail: IClassificationDetail = useClassificationDetail(
        (state) => state.classification[classificationName]
    );
    const [totalRows, setTotalRows] = useState<number>(0);
    const gridRef = useRef<AgGridReact>(null);
    const [selectedRows, setSelectedRows] = useState([]);
    const [edits, setEdits] = useState(new Map<string, ClassificationAccessRequest>());
    const { getClassificationTree } = useClassificationTree();
    const [classificationTree, setClassificationTree] = useState();
    const [isEditsSubmitted, setEditsSubmitted] = useState(false);

    const createServerSideDataSource = useCallback(
        (classificationTree) => {
            const buildParams = (params) => {
                const filterModel = params.filterModel;
                let queryParams = {
                    offset: params.startRow || 0,
                    limit: gridRef?.current?.api.paginationGetPageSize(),
                    eventType: get(filterModel, ['eventType', 'values'], []),
                };
                if (filterModel && Object.keys(filterModel).length > 0) {
                    const filterConditions = [];
                    queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
                }
                return queryParams;
            };
            return {
                getRows: (params: IServerSideGetRowsParams) => {
                    gridRef.current?.api.showLoadingOverlay();
                    fetch({
                        ...classificationUrls.getClassificationUpdatableBy,
                        params: {
                            classificationName,
                        },
                        qs: buildParams(params?.request),
                    })
                        .then((response) => {
                            if (response.status !== 200) {
                                params.fail();
                                return;
                            }
                            const rowsThisPage = response.data.data;
                            const path = [...classificationTree?.[classificationName]?.path].reverse();
                            const rowData = rowsThisPage.map((row) => {
                                const classificationNames =
                                    row.classifications?.map((classification) => classification.name) ?? [];
                                const classificationNameToReadGranularPermissions = path?.find((parent: string) =>
                                    classificationNames.includes(parent)
                                );
                                const classificationToReadGranularPermissions = row.classifications?.find(
                                    (classification) =>
                                        classification.name === classificationNameToReadGranularPermissions
                                );

                                return {
                                    ...row,
                                    properties: {
                                        ...row.properties,
                                        canRead: classificationToReadGranularPermissions?.canRead,
                                        canEdit: classificationToReadGranularPermissions?.canEdit,
                                    },
                                };
                            });
                            const rowCount = response.data.pageInfo.total;
                            params.success({ rowData, rowCount });
                            setTotalRows(rowCount);
                        })
                        .finally(() => {
                            gridRef.current?.api.hideOverlay();
                            if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                                gridRef.current?.api.showNoRowsOverlay();
                            }
                        });
                },
            };
        },
        [classificationName, classificationTree]
    );

    const columnDefs: ColDef[] = useMemo(() => {
        return [
            {
                field: 'isEdited',
                headerName: 'Is Edited',
                hide: true,
            },
            {
                field: 'properties.name',
                headerName: 'Name',
                filter: 'agTextColumnFilter',
                flex: 1,
                tooltipValueGetter: (p: ITooltipParams) => {
                    if (!p.node.selectable) {
                        return `${p.value} cannot be deleted as it is inherited`;
                    }
                    return p.value;
                },
                checkboxSelection: (params: CheckboxSelectionCallbackParams) => {
                    return params.node.selectable;
                },
            },
            {
                field: 'properties.type',
                headerName: 'Type',
                filter: 'agTextColumnFilter',
                flex: 1,
            },
            {
                field: 'properties.canRead',
                headerName: 'Can Read',
                cellRenderer: (params: ICellRendererParams) => {
                    return (
                        <input
                            type="checkbox"
                            name="canRead"
                            checked={
                                edits.has(params.data?.id)
                                    ? edits.get(params.data?.id)?.canRead
                                    : params.data.properties.canRead
                            }
                            onChange={(event) => {
                                if (params.data.properties?.canRead !== event.target.checked) {
                                    if (edits.has(params.data.id)) {
                                        edits.set(params.data?.id, {
                                            ...edits.get(params.data?.id),
                                            canRead: event.target.checked,
                                        });
                                    } else {
                                        edits.set(params.data?.id, {
                                            agentId: params.data?.id,
                                            canEdit: params.data?.properties?.canEdit,
                                            canRead: event.target.checked,
                                        });
                                    }
                                    params.node.setDataValue('isEdited', true);
                                    setEdits(new Map(edits));
                                } else if (edits.has(params.data?.id)) {
                                    if (params.data.properties?.canEdit === edits.get(params.data?.id).canEdit) {
                                        edits.delete(params.data?.id);
                                        params.node.setDataValue('isEdited', false);
                                        setEdits(new Map(edits));
                                    } else {
                                        edits.set(params.data?.id, {
                                            ...edits.get(params.data?.id),
                                            canRead: event.target.checked,
                                        });
                                        setEdits(new Map(edits));
                                    }
                                }
                            }}
                        />
                    );
                },
            },
            {
                field: 'properties.canEdit',
                headerName: 'Can Edit',
                cellRenderer: (params: ICellRendererParams) => {
                    return (
                        <input
                            type="checkbox"
                            name="canEdit"
                            checked={
                                edits.has(params.data?.id)
                                    ? edits.get(params.data?.id)?.canEdit
                                    : params.data.properties.canEdit
                            }
                            onChange={(event) => {
                                if (params.data.properties?.canEdit !== event.target.checked) {
                                    if (edits.has(params.data.id)) {
                                        edits.set(params.data?.id, {
                                            ...edits.get(params.data?.id),
                                            canEdit: event.target.checked,
                                        });
                                    } else {
                                        edits.set(params.data?.id, {
                                            agentId: params.data?.id,
                                            canRead: params.data?.properties?.canRead,
                                            canEdit: event.target.checked,
                                        });
                                    }
                                    params.node.setDataValue('isEdited', true);
                                    setEdits(new Map(edits));
                                } else if (edits.has(params.data?.id)) {
                                    if (params.data.properties?.canRead === edits.get(params.data?.id).canRead) {
                                        edits.delete(params.data?.id);
                                        params.node.setDataValue('isEdited', false);
                                        setEdits(new Map(edits));
                                    } else {
                                        edits.set(params.data?.id, {
                                            ...edits.get(params.data?.id),
                                            canEdit: event.target.checked,
                                        });
                                        setEdits(new Map(edits));
                                    }
                                }
                            }}
                        />
                    );
                },
            },
        ];
    }, [classificationName, classificationTree, edits]);

    const handleSetDataSource = useCallback(
        (event) => {
            getClassificationTree().then((tree) => {
                setClassificationTree(tree);
                const dataSource = createServerSideDataSource(tree);
                event?.api.setServerSideDatasource(dataSource);
            });
        },
        [classificationName]
    );

    useEffect(() => {
        // Listen the custom event fighting after new UPDATABLE_BY created
        window.addEventListener('refresh-updatable-by-grid', (e) => {
            gridRef.current?.api?.refreshServerSide();
            gridRef.current?.api.redrawRows();
        });
    }, [gridRef]);

    useEffect(() => {
        if (classificationName && gridRef?.current.api) {
            gridRef.current.api?.setServerSideDatasource(createServerSideDataSource(classificationTree));
            gridRef.current?.api?.refreshServerSide();
        }
    }, [classificationName]);

    return (
        <AnimatedPage style={{ height: '100%' }}>
            <AccessControlContainer>
                <div className="toolbar">
                    <CreateUpdatableBy classificationDetail={classificationDetail} disabled={false} />
                    <Button
                        variant="contained"
                        color="error"
                        onClick={() => {
                            const agentIdsToDelete = selectedRows?.map((row) => row?.id);
                            if (agentIdsToDelete?.length > 0) {
                                deleteUpdatableBy(classificationName, agentIdsToDelete)
                                    .then((res) => {
                                        if (res) {
                                            gridRef?.current.api.deselectAll();
                                            gridRef.current.api.refreshServerSide();
                                        }
                                    })
                                    .catch((error) => {
                                        console.error(error);
                                    });
                            }
                        }}
                        size="xs"
                        endIcon={<MinusIcon />}
                        disabled={!(selectedRows.length > 0)}
                    >
                        Remove {selectedRows.length > 0 ? selectedRows.length + ' Agent(s)' : ''}
                    </Button>
                    {edits.size > 0 && (
                        <Button
                            variant="contained"
                            color={isEditsSubmitted ? 'secondary' : 'primary'}
                            disabled={isEditsSubmitted}
                            size="xs"
                            onClick={() => {
                                setEditsSubmitted(true);
                                createUpdatableBy(classificationName, Array.from(edits.values())).then(() => {
                                    setEditsSubmitted(false);
                                    setEdits(new Map<string, ClassificationAccessRequest>());
                                    gridRef.current?.api?.refreshServerSide();
                                });
                            }}
                        >
                            {isEditsSubmitted ? <CircularProgress size={20} /> : `Save ${edits.size} Edits`}
                        </Button>
                    )}
                </div>
                <div className="access ag-theme-alpine">
                    <AgGridReact
                        onSelectionChanged={() => {
                            setSelectedRows(gridRef?.current?.api.getSelectedRows());
                        }}
                        ref={gridRef}
                        onGridReady={handleSetDataSource}
                        columnDefs={columnDefs}
                        isRowSelectable={(node) => {
                            return (
                                node.data?.classifications?.filter(
                                    (classification) => classification?.name === classificationName
                                )?.length === 1
                            );
                        }}
                        getRowStyle={(params: RowClassParams) => {
                            if (params.data?.isEdited === true) {
                                return {
                                    backgroundColor: '#B8C6E5',
                                };
                            }
                        }}
                        noRowsOverlayComponent={NoRowsOverlay}
                        noRowsOverlayComponentParams={{
                            message: 'Currently, no user has access to Read or Edit attributes of this Classification',
                        }}
                        {...gridOptions}
                    />
                </div>
                <AGGridTablePagination gridRef={gridRef} totalRows={totalRows} />
            </AccessControlContainer>
        </AnimatedPage>
    );
};

export default Access;
