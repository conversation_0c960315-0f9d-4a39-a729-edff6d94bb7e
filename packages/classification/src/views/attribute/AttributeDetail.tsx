/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
    useToggle,
    IClassificationDetail,
    schema<PERSON><PERSON>per,
    AttributeSchema,
    UI_SEARCH_PARAM,
    SCHEMA_VIEW,
} from 'ui-common';
import { Box, Button, MainTooltip, RightTray, AttributeForm } from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { useClassificationDetail, useDialog, useUnitOfMeasure, useSchemaTree } from '@tripudiotech/admin-caching-store';
import { getClassificationInfo } from '../../utils/helper';
import useLocalStore from '../../store/useLocalStore';
import AttributeInformation from '../../components/attribute/AttributeInformation';
import { deleteClassificationAttribute, updateClassification, updateClassificationAttribute } from '../../actions';

const AttributeDetail = ({ classificationDetail }: { classificationDetail: IClassificationDetail }) => {
    const getClassification = useClassificationDetail((state) => state.getClassification);
    const formRef = useRef(null);
    const [searchParams, setSearchParams] = useSearchParams();
    const attributeName = searchParams.get(UI_SEARCH_PARAM.ATTRIBUTE);
    const open = attributeName && searchParams.get(UI_SEARCH_PARAM.VIEW) === SCHEMA_VIEW.DETAIL;
    const { schemaTreeMap } = useSchemaTree();
    const [isEditing, isEditingToggle] = useToggle(false);
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const classificationName = getClassificationInfo(classificationDetail, 'name');

    /**
     * Unit of measure cache
     */
    const [quantityKind, quantityUnit, qdtUnitMapper, qdtKindMapper, getQuantityUnit] = useUnitOfMeasure((state) => [
        state.quantityKind,
        state.quantityUnit,
        state.qdtUnitMapper,
        state.qdtKindMapper,
        state.getQuantityUnit,
    ]);

    /**
     * Attribute Detail from schema
     */
    const attributeDetail: AttributeSchema = useMemo(() => {
        if (attributeName && classificationDetail) {
            return classificationDetail.attributes[attributeName];
        }
    }, [attributeName, classificationDetail]);

    const handleClose = useCallback(() => {
        // clear all search params
        schemaHelper.closeAttributeDetail(searchParams, setSearchParams);
        isEditingToggle.close();
    }, [searchParams, setSearchParams]);

    const handleDelete = useCallback(async () => {
        setIsLoading(true);
        const res = await deleteClassificationAttribute(classificationName, attributeName);
        if (res) {
            handleClose();
        }

        await getClassification(classificationName);
        setIsLoading(false);
    }, [classificationName, attributeDetail]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete attribute',
            `Do you really want to delete the attribute <b>${attributeDetail?.displayName}</b> from the entity type <b>${classificationName}</b>?`,
            handleDelete,
            'error'
        );
    }, [onOpenDialog, handleDelete]);

    const handleSubmit = useCallback(
        async (values) => {
            if (!formRef.current.dirty) {
                isEditingToggle.close();
                return;
            }
            setIsLoading(true);

            const res = await updateClassificationAttribute(classificationName, attributeName, values);
            await getClassification(classificationName);
            if (res) {
                if (values.name !== attributeName) {
                    searchParams.set(UI_SEARCH_PARAM.ATTRIBUTE, values.name);
                    setSearchParams(searchParams);
                }
                isEditingToggle.close();
            }
            setIsLoading(false);
        },
        [classificationName, attributeName]
    );

    const isSystem = attributeDetail?.system;
    const attributeOf = attributeDetail?.attributeOf;
    const isInherited = attributeOf !== getClassificationInfo(classificationDetail, 'name');

    return (
        <RightTray
            title={`Attribute ${attributeName}`}
            componentName={COMPONENT_NAME.SCHEMA_ATTRIBUTE_DETAIL}
            open={Boolean(open)}
            onClose={handleClose}
            disableCloseOnClickOutside={isEditing}
            hideConfirm
        >
            {attributeDetail && (
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    {isEditing ? (
                        <AttributeForm
                            formRef={formRef}
                            attributeDetail={attributeDetail}
                            schemaDetail={classificationDetail}
                            onSubmit={handleSubmit}
                            quantityKind={quantityKind}
                            quantityUnit={quantityUnit}
                            qdtKindMapper={qdtKindMapper}
                            qdtUnitMapper={qdtUnitMapper}
                            schemaTreeMap={schemaTreeMap}
                            getQuantityUnit={getQuantityUnit}
                            isUpdating
                        />
                    ) : (
                        <AttributeInformation attributeDetail={attributeDetail} />
                    )}
                </Box>
            )}
            <Box sx={{ mt: 'auto', display: 'flex', gap: '8px', justifyContent: 'flex-end', margin: '16px 24px' }}>
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={() => (isEditing ? isEditingToggle.close() : handleClose())}
                >
                    Cancel
                </Button>
                {!isEditing && (
                    <MainTooltip
                        title={
                            isInherited ? (
                                <span>
                                    This attribute is inherited from <b>{attributeOf}</b>. Hence you cannot delete it
                                    from the current entity type.
                                </span>
                            ) : isSystem ? (
                                'You are not allowed to modify system attribute'
                            ) : (
                                ''
                            )
                        }
                    >
                        <span>
                            <Button
                                sx={{
                                    width: '136px',
                                    justifyContent: 'flex-start',
                                }}
                                variant="outlined"
                                disabled={isSystem || isInherited}
                                onClick={onDelete}
                                color="error"
                            >
                                Delete
                            </Button>
                        </span>
                    </MainTooltip>
                )}
                {/* TODO: Implement attribute modification restrictions for system entity types once they are consolidated under an abstract parent type. */}
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        maxWidth: '260px',
                    }}
                    variant="contained"
                    onClick={() => (isEditing ? formRef.current.submitForm() : isEditingToggle.open())}
                    color="primary"
                >
                    {isEditing ? 'Save' : 'Edit'}
                </Button>
            </Box>
        </RightTray>
    );
};
export default AttributeDetail;
