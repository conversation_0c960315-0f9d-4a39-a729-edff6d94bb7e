/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import { IClassificationDetail } from 'ui-common';
import isEqual from 'lodash/isEqual';
import { EXPAND_CLASSIFICATION } from '../constants';

export const getClassificationInfo = (classificationDetail: IClassificationDetail, key: string) => {
    return get(classificationDetail, ['classification', key]);
};

export const clearTableStates = (gridRef) => {
    gridRef.current.api.redrawRows();
    gridRef.current.api.setFilterModel(null);
    gridRef.current.columnApi.applyColumnState({
        defaultState: { sort: null },
    });
    gridRef.current.api.onFilterChanged();
};

export const isAttributeOrderEqual = (oldOrder, newOrder) => {
    return isEqual(oldOrder, newOrder);
};

export const getCreateSubTypeTooltipMsg = (extendable: boolean, isMaster: boolean, entityName?: string) => {
    return isMaster
        ? `You are not allowed to create sub type directly under a Master entity type`
        : extendable
        ? ''
        : `${entityName || 'The current Entity Type'} is not extendable to create a new sub type`;
};

export const setDefaultValuesForMaster = (values, setFieldValue) => {
    if (values.name) {
        setFieldValue('masterName', values.name + 'Master', false);
    }
    if (values.displayName) {
        setFieldValue('masterDisplayName', values.displayName + ' Master', false);
    }
    if (values.description) {
        setFieldValue('masterDescription', values.description, false);
    }
};

export const clearDefaultValuesForMaster = (setFieldValue) => {
    setFieldValue('masterName', null);
    setFieldValue('masterDisplayName', null);
    setFieldValue('masterDescription', null);
};

export const handleWithMasterChanged = (e, values, setFieldValue) => {
    if (e.target.value === 'withMaster') {
        setDefaultValuesForMaster(values, setFieldValue);
    } else {
        clearDefaultValuesForMaster(setFieldValue);
    }
};

export const expandClassification = (classificationName) => {
    window.dispatchEvent(new CustomEvent(EXPAND_CLASSIFICATION, { detail: { name: classificationName } }));
};

export const isRegexPatternValid = (pattern: string) => {
    try {
        new RegExp(pattern);
        return true;
    } catch {
        return false;
    }
};
