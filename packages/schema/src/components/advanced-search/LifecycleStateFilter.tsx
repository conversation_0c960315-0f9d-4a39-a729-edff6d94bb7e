/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Autocomplete, FormControl, FormHelperText, MenuItem, Select, TextField } from 'ui-style';
import { QUERY, AttributeType } from '@tripudiotech/admin-api';
import { FormikProps } from 'formik';
import { useMemo } from 'react';
import { SchemaWithLifeCycleDetail } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import { LIFECYCLE_STATE_FILTER_OPTIONS, LIFECYCLE_STATE_OPTION_LABEL } from '../../constants';
import {
    isError,
    isLifecycleStateAttributeSelected,
    isLifecycleStateFilterWithInputValue,
} from '../../utils/advancedSearchHelper';
import flattenDeep from 'lodash/flattenDeep';
import {
    AdvancedFilterFormProps,
    Condition,
    FilterOperator,
    FilterValue,
    getDefaultValueForAttribute,
    MENU_PROPS,
} from './FilterComponents';
import StyledPaper from '../StyledPaper';

const LifecycleStateFilter = ({
    name,
    formProps,
    schemaDetail,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
}) => {
    const { errors, values } = formProps;
    const filter = get(values, name);
    const filterField = get(filter, 'values.field');
    const filterOperator = get(filter, 'operator');
    const filterType = get(filter, 'filterType', '');
    const filterValue: any = get(values, `${name}.values.value`);
    const stateOptions = useMemo(() => {
        const lifecycles = get(schemaDetail, 'lifecycles', []);
        return flattenDeep(
            lifecycles.map((lifecycle: any) => {
                return Object.values(lifecycle.states).map((state: any) => ({
                    label: state.name,
                    value: state.name,
                    group: lifecycle.lifeCycle.name,
                }));
            })
        );
    }, [schemaDetail]);
    return (
        <>
            <FormControl error={isError(formProps, `${name}.values.field`)}>
                <Select
                    size="small"
                    name={`${name}.values.field`}
                    MenuProps={MENU_PROPS}
                    value={filterField}
                    inputProps={{ 'aria-label': 'Without label' }}
                    onChange={(e) => {
                        const value = e.target.value;
                        formProps.handleChange(e);
                        formProps.setFieldValue(
                            `${name}.values.value`,
                            getDefaultValueForAttribute({
                                type: value === 'state.name' ? AttributeType.STRING : AttributeType.BOOLEAN,
                            } as any)
                        );
                        if (value !== 'state.name') {
                            formProps.setFieldValue(`${name}.operator`, QUERY.EXACT);
                        } else {
                            formProps.setFieldValue(`${name}.operator`, QUERY.IN);
                        }
                    }}
                    displayEmpty
                    renderValue={(selected: any) => {
                        return selected ? LIFECYCLE_STATE_OPTION_LABEL[selected] : 'Select Attribute';
                    }}
                >
                    {Object.values(LIFECYCLE_STATE_FILTER_OPTIONS).map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                            {option.label || option.value}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            {isLifecycleStateAttributeSelected(filterType, filterField) && (
                <FilterOperator
                    name={`${name}.operator`}
                    value={filterOperator}
                    formProps={formProps}
                    type={filterField === 'state.name' ? AttributeType.STRING : AttributeType.BOOLEAN}
                />
            )}
            {filterField === 'state.name' &&
            [QUERY.IN, QUERY.NOT_IN, QUERY.EQUAL, QUERY.EXACT, QUERY.NOT_EQUAL].includes(filterOperator) ? (
                <>
                    <Autocomplete
                        PaperComponent={StyledPaper}
                        multiple={[QUERY.IN, QUERY.NOT_IN].includes(filterOperator)}
                        size="small"
                        isOptionEqualToValue={(option, value) => option.value === value}
                        slotProps={{
                            popper: {
                                style: { zIndex: 1301 }, // Higher than the dialog's default z-index
                            },
                        }}
                        options={stateOptions}
                        value={
                            [QUERY.IN, QUERY.NOT_IN].includes(filterOperator)
                                ? Array.isArray(filterValue)
                                    ? filterValue
                                    : filterValue?.split(',').filter(Boolean) || []
                                : filterValue || ''
                        }
                        sx={{
                            '& .MuiIconButton-root': {
                                height: '16px',
                                width: '16px',
                            },
                            '& .MuiOutlinedInput-root': {
                                minWidth: 140,
                                padding: '4px 6px !important',
                            },
                        }}
                        groupBy={(option) => option.group}
                        onChange={(_, newValue: any) => {
                            if ([QUERY.IN, QUERY.NOT_IN].includes(filterOperator)) {
                                formProps.setFieldValue(
                                    `${name}.values.value`,
                                    newValue?.map((v) => v.value || v).join(',')
                                );
                            } else {
                                formProps.setFieldValue(`${name}.values.value`, newValue?.value);
                            }
                        }}
                        disableClearable
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                size="small"
                                InputLabelProps={{ shrink: true }}
                                error={isError(formProps, `${name}.values.value`)}
                                helperText={get(errors, `${name}.values.value`)}
                            />
                        )}
                    />
                    {isError(formProps, `${name}.values.value`) && (
                        <FormHelperText>{get(errors, `${name}.values.value`)}</FormHelperText>
                    )}
                </>
            ) : (
                isLifecycleStateFilterWithInputValue(filterType, filterField, filterOperator) && (
                    <FilterValue
                        name={`${name}.values.value`}
                        attributeSchemaType={
                            filterField === 'state.name' ? AttributeType.STRING : AttributeType.BOOLEAN
                        }
                        handleChange={formProps.handleChange}
                        formProps={formProps}
                        filterOperator={filterOperator}
                    />
                )
            )}
        </>
    );
};

export default LifecycleStateFilter;
