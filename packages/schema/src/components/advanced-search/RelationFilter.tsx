/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Box,
    Button,
    CircularProgress,
    Collapse,
    InputAdornment,
    Loading,
    MainTooltip,
    Menu,
    SearchIcon,
    tableStyles,
    TextField,
    Typography,
    ExpandMoreIcon,
} from 'ui-style';
import { Form, Formik, FormikProps } from 'formik';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { SchemaWithLifeCycleDetail, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import { RELATION_FILTER_OPTIONS, INITIAL_CONDITION, DEFAULT_FILTER_VALUE } from '../../constants';
import {
    buildRelationTreeData,
    extractRelationsFromRelationFilter,
    getRelationFilterAsText,
    getRelationQuery,
} from '../../utils/advancedSearchHelper';
import { AgGridReact } from '@ag-grid-community/react';
import { v4 as uuidv4 } from 'uuid';
import { GridOptions, ICellRendererParams } from '@ag-grid-community/core';
import last from 'lodash/last';
import isEmpty from 'lodash/isEmpty';
import { useSearchParams } from 'react-router-dom';
import { AdvancedFilterFormProps, Condition, Group, RelationRowData, validationSchema } from './FilterComponents';

const getAutoColumnGroupDef: any = (setRowData) => {
    return {
        field: 'name',
        header: 'Relation Name',
        cellRenderer: (props: ICellRendererParams<RelationRowData>) => {
            const expanded = props?.node?.expanded;
            const data = props.node?.data;
            const [isLoading, setIsLoading] = useState(false);
            const level = props.node.level;
            const getSchema = useSchemaDetail((state) => state.getSchema);
            const onToggle = async () => {
                if (isLoading) return;

                if (!expanded) {
                    setIsLoading(true);
                    const schema = await getSchema(data.entityType, true);
                    if (schema) {
                        let newRowData: RelationRowData[] = [];
                        props.api.forEachNode((node: any) => {
                            if (node.id === props.node.id) {
                                newRowData.push({
                                    ...props.data,
                                    isLoaded: true,
                                });
                            } else {
                                newRowData.push({ ...node.data });
                            }
                        });
                        schema.relationTypes.forEach((relationType) => {
                            const rowId = uuidv4();
                            const relEntityType =
                                relationType.direction === 'Incoming'
                                    ? relationType.fromEntityType
                                    : relationType.toEntityType;
                            newRowData.push({
                                id: rowId,
                                path: [...data.path, rowId],
                                name: relationType.name,
                                displayName: relationType.displayName,
                                entityType: relEntityType,
                                isLoaded: false,
                                relationQuery: `${data.relationQuery}.relation.${relationType.name}:${relEntityType}`,
                                fromEntityType: relationType.fromEntityType,
                                toEntityType: relationType.toEntityType,
                            });
                        });
                        setRowData(newRowData);
                    }
                }
                setIsLoading(false);
                props.node?.setExpanded(!expanded);
                props.api.refreshCells({ rowNodes: [props.node], force: true });
            };
            return (
                <Box
                    sx={{
                        display: 'flex',
                        gap: '4px',
                        height: '100%',
                        alignItems: 'center',
                        paddingLeft: `${(20 * level).toString()}px`,
                    }}
                >
                    <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={onToggle}>
                        {level === 0 ? (
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M15 8L11 12L10.3 11.3L13.6 8L10.3 4.7L11 4L15 8Z" fill="#161616" />
                                <path d="M10 7.5H6V8.5H10V7.5Z" fill="#161616" />
                                <path d="M1 8L5 4L5.7 4.7L2.4 8L5.7 11.3L5 12L1 8Z" fill="#161616" />
                            </svg>
                        ) : isLoading ? (
                            <CircularProgress size={12} />
                        ) : expanded ? (
                            <ExpandMoreIcon sx={{ transform: 'rotate(180deg)' }} />
                        ) : (
                            <ExpandMoreIcon />
                        )}
                    </Box>
                    <Typography
                        variant="label2-med"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => {
                            if (props.node.data?.relationQuery) {
                                props.node.setSelected(true);
                            }
                        }}
                    >
                        {level === 0
                            ? props.node.data?.displayName
                            : `${props.node.data?.displayName} [${props.node.data?.entityType}]`}
                    </Typography>
                </Box>
            );
        },
    };
};
const relationFilterGridOptions: GridOptions = {
    loadingOverlayComponent: Loading,
    rowDragMultiRow: true,
    animateRows: true,
    defaultColDef: {
        sortable: false,
        resizable: false,
        flex: 1,
        filter: true,
        floatingFilter: false,
        autoHeight: false,
        wrapText: false,
    },
    components: {},
    cacheBlockSize: 100,
    rowModelType: 'clientSide',
    rowSelection: 'single',
    headerHeight: 0,
    pagination: false,
    suppressDragLeaveHidesColumns: true,
    getDataPath: (data) => data.path,
    getRowId: (params) => params.data.id,
    treeData: true,
    columnDefs: [
        {
            field: 'name',
            headerName: 'Relation Name',
            flex: 1,
            cellStyle: {},
            editable: false,
            hide: true,
        },
        {
            field: 'displayName',
            headerName: 'Relation Display Name',
            flex: 1,
            cellStyle: {},
            editable: false,
            hide: true,
        },
    ],
    rowHeight: 32,
    groupDefaultExpanded: 1,
    suppressContextMenu: true,
    suppressRowClickSelection: true,
};
const RelationFilter = ({
    name,
    formProps,
    schemaDetail,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
}) => {
    const [selectedRelation, setSelectedRelation] = useState<RelationRowData>(null);
    const [rowData, setRowData] = useState<RelationRowData[]>([]);
    const gridRef = useRef<AgGridReact>();
    const [open, setOpen] = useState(false);
    const [relationSchemaDetail, getSchema] = useSchemaDetail((state) => [
        selectedRelation ? state.schema[selectedRelation.entityType] : null,
        state.getSchema,
    ]);
    const [searchParams] = useSearchParams();
    const queryOpen: boolean = useMemo(
        () => Boolean(searchParams.get('queryBuilder')),
        [searchParams.get('queryBuilder')]
    );
    const [isGridReady, setIsGridReady] = useState(false);
    const formRef = useRef<any>();
    const [initialValues, setInitialValues] = useState<{ condition: Condition }>({
        condition: INITIAL_CONDITION,
    });
    const handleClose = () => setOpen(false);
    const handleSearch = (e) => {
        if (gridRef.current) {
            gridRef.current.api.setQuickFilter(e.target.value);
        }
    };
    const [isLoaded, setIsLoaded] = useState(false);
    const resizeColumns = useCallback(() => {
        gridRef.current.columnApi.autoSizeAllColumns();
    }, []);

    useEffect(() => {
        if (!open) {
            setIsGridReady(false);
        }
    }, [open]);
    useEffect(() => {
        if (isGridReady && selectedRelation && gridRef.current?.api && queryOpen) {
            const selectedPaths = selectedRelation.path;
            gridRef.current.api.forEachNode((node) => {
                if (selectedPaths.includes(node.id)) {
                    node.setExpanded(true);
                }
                if (last(selectedPaths) === node.id) {
                    setTimeout(() => gridRef.current.api.ensureNodeVisible(node, 'top'), 300);
                    node.setSelected(true, false, true);
                }
            });
        }
    }, [isGridReady, selectedRelation, queryOpen]);
    useEffect(() => {
        const values = get(formProps.values, `${name}`);
        if (values && values?.filterType === 'relation' && schemaDetail) {
            setIsLoaded(false);
            const relationQuery = getRelationQuery(values);
            const relations = extractRelationsFromRelationFilter(relationQuery);
            buildRelationTreeData(schemaDetail, relations).then((data) => {
                setRowData(data);
                setInitialValues({
                    condition: { ...get(formProps.values, name, { ...INITIAL_CONDITION, filterType: 'relation' }) },
                });
                const attributeDotIndex = relationQuery?.lastIndexOf('.');
                const relationQueryWithoutAttribute =
                    attributeDotIndex !== -1 ? relationQuery.substring(0, attributeDotIndex) : relationQuery;
                const selectedRel = data.find(
                    (row) => row.relationQuery === relationQueryWithoutAttribute && row.relationQuery
                );
                if (selectedRel) {
                    setSelectedRelation(selectedRel);
                }
                setIsLoaded(true);
            });
        }
    }, [schemaDetail?.entityType?.name, get(formProps.values, `${name}`)]);

    useEffect(() => {
        if (selectedRelation) {
            getSchema(selectedRelation.entityType, true);
            formRef.current?.resetForm();
        }
    }, [selectedRelation]);
    const autoGroupColumnDef = useMemo(() => getAutoColumnGroupDef(setRowData), []);
    const onGridReady = useCallback(() => setIsGridReady(true), []);
    const onSelectionChanged = useCallback(() => {
        setSelectedRelation(gridRef.current.api.getSelectedRows()?.[0]);
        setInitialValues({
            condition: {
                ...INITIAL_CONDITION,
                values: [
                    {
                        ...DEFAULT_FILTER_VALUE,
                        filterType: 'attribute',
                    },
                ],
            },
        });
    }, []);

    const onSubmit = (values) => {
        const relationFilter = {
            ...values.condition,
            filterType: 'relation',
        };
        formProps.setFieldValue(name, relationFilter);
        handleClose();
    };
    const isError = !isEmpty(get(formProps.errors, name)) && !isEmpty(get(formProps.touched, name));
    const relationFilterText = getRelationFilterAsText(get(formProps.values, `${name}`));
    return (
        <Box
            sx={{
                '& .MuiList-root': { height: '100%' },
                '& .actionBtn': {
                    paddingRight: '32px',
                },
                flexGrow: 1,
            }}
        >
            <MainTooltip enterDelay={400} title={relationFilterText || ''}>
                <TextField
                    size="small"
                    aria-readonly="true"
                    fullWidth
                    value={relationFilterText}
                    onClick={() => {
                        setOpen((prev) => !prev);
                        formProps.setFieldTouched(name, true);
                    }}
                    error={isError}
                    helperText={isError ? 'Relation Filter is invalid' : ''}
                    sx={{ '& .MuiOutlinedInput-root': { width: '100%' } }}
                />
            </MainTooltip>
            <Collapse
                unmountOnExit
                in={open}
                sx={{
                    background: '#FFFFFF',
                    height: '100%',
                    display: 'flex',
                    marginLeft: '-118px',
                    marginTop: '8px',
                    border: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                }}
            >
                {isLoaded ? (
                    <Box sx={{ display: 'flex', height: '320px' }}>
                        <Box
                            sx={{
                                width: '320px',
                                minWidth: '320px',
                                borderRight: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                height: '100%',
                                padding: '16px',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '8px',
                            }}
                        >
                            <Typography variant="label1-med">Relation Browser</Typography>
                            <TextField
                                sx={{
                                    '& input': {
                                        paddingLeft: '4px !important',
                                        fontSize: '14px',
                                        height: '24px',
                                    },
                                }}
                                onChange={handleSearch}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon />
                                        </InputAdornment>
                                    ),
                                }}
                                placeholder="Search relation"
                                fullWidth
                                variant="outlined"
                                size="small"
                            />
                            <Box
                                sx={{
                                    height: '100%',
                                    width: '100%',
                                    ...tableStyles,
                                    '& .ag-header': {
                                        '&.ag-pivot-off': {
                                            display: 'none',
                                        },
                                    },
                                    '& .ag-theme-alpine': {
                                        '& .ag-row-selected:before': {
                                            background: '#B8C6E5',
                                        },
                                        '& .ag-row': {
                                            border: 'none !important',
                                        },
                                        '& .ag-root-wrapper': {
                                            border: 'none',
                                        },
                                    },
                                }}
                            >
                                <div style={{ height: '100%', width: '100%' }} className="ag-theme-alpine">
                                    <AgGridReact
                                        ref={gridRef}
                                        gridOptions={relationFilterGridOptions}
                                        rowData={rowData}
                                        suppressRowClickSelection
                                        treeData
                                        autoGroupColumnDef={autoGroupColumnDef}
                                        onFirstDataRendered={resizeColumns}
                                        onRowGroupOpened={resizeColumns}
                                        onSelectionChanged={onSelectionChanged}
                                        onGridReady={onGridReady}
                                    />
                                </div>
                            </Box>
                        </Box>
                        <Box
                            sx={{
                                width: '100%',
                                padding: '16px',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '8px',
                                overflow: 'hidden',
                            }}
                        >
                            <Typography variant="label1-med">Criteria</Typography>
                            <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
                                <Formik
                                    validateOnBlur
                                    onSubmit={onSubmit}
                                    enableReinitialize
                                    initialValues={initialValues}
                                    validationSchema={validationSchema}
                                    innerRef={formRef}
                                >
                                    {(relFormProps) => {
                                        return (
                                            <Form id="advanced-search-relation-form" className="advanced-search-form">
                                                {relationSchemaDetail ? (
                                                    <Box className="query-container">
                                                        <Group
                                                            name={'condition'}
                                                            schemaDetail={relationSchemaDetail}
                                                            formProps={relFormProps}
                                                            sx={{
                                                                overflow: 'auto',
                                                                maxHeight: 'calc(100vh - 468px)',
                                                                width: 'max-content',
                                                            }}
                                                            fieldPrefix={`${selectedRelation?.relationQuery}.`}
                                                            filterOptions={RELATION_FILTER_OPTIONS}
                                                            selectedRelation={selectedRelation}
                                                        />
                                                    </Box>
                                                ) : selectedRelation ? (
                                                    <Box
                                                        sx={{
                                                            display: 'flex',
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                        }}
                                                    >
                                                        <Loading />
                                                    </Box>
                                                ) : (
                                                    <Box
                                                        sx={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            marginTop: '16px',
                                                        }}
                                                    >
                                                        <Typography variant="label2-med">
                                                            Select a Relation to create filter criteria
                                                        </Typography>
                                                    </Box>
                                                )}
                                            </Form>
                                        );
                                    }}
                                </Formik>
                            </Box>
                            <Box
                                sx={{
                                    marginTop: 'auto',
                                    width: '100%',
                                    display: 'flex',
                                    gap: '8px',
                                    alignItems: 'center',
                                    justifyContent: 'flex-end',
                                }}
                            >
                                <Button
                                    size="small"
                                    variant="contained-blue"
                                    className="actionBtn"
                                    onClick={handleClose}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={() => formRef.current.submitForm()}
                                    size="small"
                                    variant="contained"
                                    color="primary"
                                    className="actionBtn"
                                >
                                    Save
                                </Button>
                            </Box>
                        </Box>
                    </Box>
                ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <Loading />
                    </Box>
                )}
            </Collapse>
        </Box>
    );
};

export default RelationFilter;
