/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Autocomplete, CircularProgress, FormControl, TextField } from '@mui/material';
import { FormikProps } from 'formik';
import get from 'lodash/get';
import {
    getDataType,
    getEnumRange,
    isAttributeSelected,
    isError,
    isFilterWithInputValue,
} from '../../utils/advancedSearchHelper';
import {
    AdvancedFilterFormProps,
    FilterOperator,
    FilterValue,
    getDefaultConditionForAttribute,
    getDefaultValueForAttribute,
    RelationRowData,
} from './FilterComponents';
import { useEffect, useMemo, useState } from 'react';
import { SchemaWithLifeCycleDetail } from '@tripudiotech/admin-caching-store';
import { fetch, RelationSchema, schemaUrls } from '@tripudiotech/admin-api';
import StyledPaper from '../StyledPaper';

const RelationAttributeFilter = ({
    name,
    formProps,
    schemaDetail,
    fieldPrefix = '',
    selectedRelation,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
    fieldPrefix?: string;
    selectedRelation: RelationRowData;
}) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [relationDetail, setRelationDetail] = useState<RelationSchema>(null);
    const { errors, values } = formProps;
    const filter = get(values, name);
    const filterField = `${get(filter, 'values.field')}`;
    const filterFieldWithoutPrefix = filterField?.replace(fieldPrefix, '');
    const filterOperator = get(filter, 'operator');
    const filterType = get(filter, 'filterType', '');

    const dataType = getDataType(relationDetail?.attributes?.[filterFieldWithoutPrefix]);
    const enumRange = getEnumRange(relationDetail?.attributes?.[filterFieldWithoutPrefix]);
    const attributes = relationDetail?.attributes || {};
    const attributeOptions = useMemo(() => {
        if (relationDetail?.attributes) {
            return Object.values(relationDetail?.attributes)
                .map((attr) => ({
                    label: attr.displayName,
                    value: `${fieldPrefix}${attr.name}`,
                }))
                .sort((a, b) => a.label.localeCompare(b.label));
        }
    }, [relationDetail]);
    useEffect(() => {
        if (selectedRelation?.fromEntityType && selectedRelation?.toEntityType) {
            fetch({
                ...schemaUrls.getRelation,
                params: {
                    fromEntityType: selectedRelation.fromEntityType,
                    toEntityType: selectedRelation.toEntityType,
                    relationType: selectedRelation.name,
                },
            })
                .then(({ data }) => {
                    setRelationDetail(data);
                })
                .catch((e) => {
                    console.error(e);
                })
                .finally(() => setIsLoaded(true));
        }
    }, [selectedRelation]);
    return (
        <>
            <FormControl
                error={isError(formProps, `${name}.values.field`)}
                size="small"
                sx={{ display: 'flex', alignItems: 'center' }}
            >
                {isLoaded ? (
                    <Autocomplete
                        sx={{
                            '& .MuiOutlinedInput-root': { minWidth: 280 },
                            '& button': { width: 24, height: 24 },
                        }}
                        size="small"
                        PaperComponent={StyledPaper}
                        value={attributeOptions?.find((option) => option.value === filterField)}
                        onChange={(e, newValue) => {
                            const attributeSchema = attributes[newValue?.value?.replace(fieldPrefix, '')];
                            formProps.setFieldValue(
                                `${name}.values.value`,
                                getDefaultValueForAttribute(attributeSchema),
                                false
                            );
                            formProps.setFieldValue(
                                `${name}.operator`,
                                getDefaultConditionForAttribute(attributeSchema),
                                false
                            );
                            formProps.setFieldValue(`${name}.values.field`, `${newValue?.value}` || '', true);
                        }}
                        options={attributeOptions}
                        getOptionLabel={(option) => option.label || ''}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                placeholder="Select Attribute"
                                error={isError(formProps, `${name}.values.field`)}
                                helperText={
                                    isError(formProps, `${name}.values.field`) && get(errors, `${name}.values.field`)
                                }
                            />
                        )}
                    />
                ) : (
                    <CircularProgress size={24} color="info" />
                )}
            </FormControl>
            {isAttributeSelected(filterType, filterFieldWithoutPrefix, attributes) && (
                <FilterOperator
                    name={name}
                    value={filterOperator}
                    formProps={formProps}
                    dataType={dataType}
                    enumRange={enumRange}
                    type={attributes[filterFieldWithoutPrefix].type}
                />
            )}
            {isFilterWithInputValue(filterType, filterFieldWithoutPrefix, attributes, filterOperator) &&
                filterOperator && (
                    <FilterValue
                        name={`${name}.values.value`}
                        attributeSchemaType={attributes[filterFieldWithoutPrefix]?.type}
                        dataType={dataType}
                        enumRange={enumRange}
                        handleChange={formProps.handleChange}
                        formProps={formProps}
                        filterOperator={filterOperator}
                        options={enumRange}
                    />
                )}
        </>
    );
};

export default RelationAttributeFilter;
