/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Autocomplete,
    Box,
    Button,
    Checkbox,
    FormControl,
    FormHelperText,
    IconButton,
    MenuItem,
    Select,
    SxProps,
    TextField,
    TrashIcon,
    GroupIcon,
    PlusIcon,
} from 'ui-style';
import { AttributeSchema } from 'ui-common';
import { EntitySelect, formatDate, formatDateTime } from '@tripudiotech/admin-styleguide';
import { QUERY, AttributeType } from '@tripudiotech/admin-api';
import { FieldArray, FormikProps } from 'formik';
import { SchemaWithLifeCycleDetail } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { parse as dateFnsParse } from 'date-fns';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
    DATA_TYPE_OPERATORS,
    DEFAULT_FILTER_OPERATORS,
    DEFAULT_FILTER_VALUE,
    DEFAULT_GROUP_FILTER_VALUE,
    EMPTY_FILTER_VALUE,
    FILTER_OPTION_LABEL,
    FILTER_OPTIONS,
    GROUP_OPERATORS,
    INITIAL_CONDITION,
    OPERATORS_BY_TYPE,
} from '../../constants';
import {
    getDataType,
    getEnumRange,
    isArrayAttribute,
    isAttributeFilter,
    isClassificationFilter,
    isError,
    isGroupFilter,
    isLifecycleStateFilter,
    isRelationAttributeFilter,
    isRelationFilter,
} from '../../utils/advancedSearchHelper';
import * as Yup from 'yup';
import RelationFilter from './RelationFilter';
import LifecycleStateFilter from './LifecycleStateFilter';
import AttributeFilter from './AttributeFilter';
import ClassificationFilter from './ClassificationFilter';
import { DateArrayInput, MultiValueInput } from '../ArrayAttribute';
import RelationAttributeFilter from './RelationAttributeFilter';

export const MENU_PROPS: any = {
    anchorOrigin: {
        vertical: 'bottom',
        horizontal: 'left',
    },
    transformOrigin: {
        vertical: 'top',
        horizontal: 'left',
    },
    PaperProps: {
        style: {
            maxHeight: '320px',
        },
    },
};

export const getDefaultValueForAttribute = (attribute: AttributeSchema) => {
    if (attribute) {
        switch (attribute.type) {
            case AttributeType.BOOLEAN:
                return false;
            case AttributeType.INTEGER:
            case AttributeType.FLOAT:
            case AttributeType.LONG:
            case AttributeType.STRING:
            case AttributeType.TEXT:
            case AttributeType.DATE:
            case AttributeType.DATE_TIME:
                return '';
        }
    }
    return '';
};

export const getDefaultConditionForAttribute = (attribute: AttributeSchema) => {
    if (attribute) {
        switch (attribute.type) {
            case AttributeType.BOOLEAN:
                return QUERY.EXACT;
            case AttributeType.INTEGER:
            case AttributeType.FLOAT:
            case AttributeType.LONG:
                return QUERY.EQUAL;
            case AttributeType.STRING:
            case AttributeType.TEXT:
                if (getDataType(attribute) || getEnumRange(attribute)) {
                    return QUERY.EXACT;
                }
                return QUERY.CONTAINS;
            case AttributeType.DATE:
            case AttributeType.DATE_TIME:
                return '';
            case AttributeType.DATE_ARRAY:
            case AttributeType.STRING_ARRAY:
            case AttributeType.DATE_TIME_ARRAY:
            case AttributeType.INTEGER_ARRAY:
            case AttributeType.FLOAT_ARRAY:
                return QUERY.CONTAINS;
        }
    }
    return '';
};
export const FilterOperator = ({
    name,
    value,
    formProps,
    type,
    dataType,
    enumRange,
}: {
    name: string;
    value: any;
    formProps: FormikProps<AdvancedFilterFormProps>;
    type: any;
    dataType?: string;
    enumRange?: string[];
}) => {
    const operators: any =
        dataType && !isArrayAttribute(type)
            ? DATA_TYPE_OPERATORS
            : Object.values(OPERATORS_BY_TYPE[type] || DEFAULT_FILTER_OPERATORS);
    return (
        <FormControl error={isError(formProps, name)}>
            <Select
                size="small"
                name={name}
                MenuProps={MENU_PROPS}
                value={value}
                inputProps={{ 'aria-label': 'Without label' }}
                onChange={formProps.handleChange}
                displayEmpty
                renderValue={(selected: any) => {
                    return selected
                        ? operators.find((option: any) => option.value === selected)?.label
                        : 'Select Operator';
                }}
            >
                {operators.map((filter: any) => (
                    <MenuItem key={filter.value} value={filter.value}>
                        {filter.label}
                    </MenuItem>
                ))}
            </Select>
            {isError(formProps, name) && <FormHelperText>{get(formProps.errors, name)}</FormHelperText>}
        </FormControl>
    );
};

export type RelationRowData = {
    id: string;
    path: string[];
    displayName: string;
    name: string;
    entityType: string;
    isLoaded: boolean;
    relationQuery: string;
    fromEntityType?: string;
    toEntityType?: string;
};
export type FilterType =
    | 'relation'
    | 'relationAttribute'
    | 'lifecycleState'
    | 'attribute'
    | 'group'
    | 'classification'
    | 'relationExists'
    | '';

export type Condition = {
    operator: string | any;
    values: { field: string; value: any; classification?: string } | Condition[];
    filterType?: FilterType;
};
export type AdvancedFilterFormProps = {
    condition: Condition;
};
const filterTypeSchema = Yup.string()
    .oneOf([
        'attribute',
        'lifecycleState',
        'relation',
        'relationExists',
        'relationNotExists',
        'classification',
        'relationAttribute',
    ])
    .required('Filter type is required');

export const filterSchema = Yup.object({
    operator: Yup.string().required('Operator is required'),
    values: Yup.mixed().when('operator', {
        is: (operator) => operator !== QUERY.IS_NULL && operator !== QUERY.IS_NON_NULL,
        then: Yup.object({
            field: Yup.string().required('Field is required'),
            value: Yup.mixed().required('Value is required'),
        }).required('Value is required'),
        otherwise: Yup.mixed().notRequired(),
    }),
    filterType: filterTypeSchema,
});

export const conditionSchema = Yup.lazy((value) => {
    if (value?.filterType === 'group' || value?.filterType === 'relation') {
        return groupSchema;
    }
    if (['relationExists', 'relationNotExists'].includes(value?.filterType)) {
        return Yup.mixed().notRequired();
    }
    return filterSchema;
});

export const classificationSchema = Yup.object({
    operator: Yup.string().required('Operator is required'),
    values: Yup.mixed().when('operator', {
        is: (operator) => operator !== QUERY.IS_NULL && operator !== QUERY.IS_NON_NULL,
        then: Yup.object({
            field: Yup.string().required('Field is required'),
            value: Yup.mixed().required('Value is required'),
            classification: Yup.string().required('Please select a classification'),
        }).required('Value is required'),
        otherwise: Yup.mixed().notRequired(),
    }),
    filterType: filterTypeSchema,
});

// Schema for group filters (recursive)
export const groupSchema = Yup.object({
    operator: Yup.string().oneOf([QUERY.AND, QUERY.OR]).required('Operator is required'),
    values: Yup.array()
        .of(conditionSchema)
        .min(1, 'Group Filter must have at least one value')
        .required('Values are required'),
    filterType: Yup.string().oneOf(['group', 'relation']).required('Filter type is required'),
});

// Root schema with the condition key
export const validationSchema = Yup.object({
    condition: Yup.object({
        operator: Yup.string().oneOf([QUERY.AND, QUERY.OR]).required('Operator is required'),
        values: Yup.array()
            .of(conditionSchema)
            .min(1, 'Group Filter must have at least one element')
            .required('Values is required'),
    }).required('Condition is required'),
});

export const Group = ({
    name,
    schemaDetail,
    formProps,
    sx = {},
    fieldPrefix = '',
    filterOptions,
    selectedRelation,
}: {
    name: string;
    formProps: FormikProps<any>;
    schemaDetail: SchemaWithLifeCycleDetail;
    sx?: SxProps;
    fieldPrefix?: string;
    filterOptions?: any[];
    selectedRelation?: RelationRowData;
}) => {
    const { values, errors } = formProps;
    const filter = get(values, name);
    const onAddFilter = () => {
        const newValues = [...get(filter, 'values', []), { ...DEFAULT_FILTER_VALUE }];
        formProps.setFieldValue(`${name}.values`, newValues, false);
    };
    const onAddGroupFilter = () => {
        const newValues = [...get(filter, 'values', [{ ...DEFAULT_FILTER_VALUE }]), { ...DEFAULT_GROUP_FILTER_VALUE }];
        formProps.setFieldValue(`${name}.values`, newValues, false);
    };
    return (
        <Box
            sx={{
                background: (theme) => theme.palette.glide.background.normal.inversePrimary,
                padding: '8px',
                borderRadius: '4px',
                border: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                marginTop: '8px',
                position: 'relative',
                width: '100%',
                ...sx,
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    gap: '8px',
                    mt: '8px',
                }}
            >
                <Box>
                    <FormControl error={isError(formProps, `${name}.filterType`)}>
                        <Select
                            size="small"
                            name={`${name}.operator`}
                            value={filter?.operator}
                            MenuProps={MENU_PROPS}
                            onChange={formProps.handleChange}
                        >
                            {GROUP_OPERATORS.map(({ value, label }) => (
                                <MenuItem value={value} key={`op-${value}`}>
                                    {label}
                                </MenuItem>
                            ))}
                        </Select>
                        <FormHelperText>{get(errors, `${name}.filterType`)}</FormHelperText>
                    </FormControl>
                </Box>
                <Box sx={{ display: 'flex', gap: '8px', flexDirection: 'column', flexGrow: 1 }}>
                    <FieldArray
                        name={`${name}.values`}
                        render={(arrayHelpers) => {
                            return get(filter, 'values', []).map((value, idx) => {
                                if (isGroupFilter(value)) {
                                    return (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                gap: '4px',
                                                justifyContent: 'space-between',
                                                width: '100%',
                                            }}
                                            key={`${name}.values[${idx}]`}
                                        >
                                            <Group
                                                name={`${name}.values[${idx}]`}
                                                schemaDetail={schemaDetail}
                                                formProps={formProps}
                                            />
                                            <IconButton size="small" onClick={() => arrayHelpers.remove(idx)}>
                                                <TrashIcon />
                                            </IconButton>
                                        </Box>
                                    );
                                }
                                return (
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            gap: '4px',
                                            justifyContent: 'space-between',
                                        }}
                                        key={`${name}.values[${idx}]`}
                                    >
                                        <Filter
                                            name={`${name}.values[${idx}]`}
                                            schemaDetail={schemaDetail}
                                            formProps={formProps}
                                            fieldPrefix={fieldPrefix}
                                            filterOptions={filterOptions}
                                            selectedRelation={selectedRelation}
                                        />
                                        <IconButton size="small" onClick={() => arrayHelpers.remove(idx)}>
                                            <TrashIcon />
                                        </IconButton>
                                    </Box>
                                );
                            });
                        }}
                    />
                </Box>
            </Box>
            {isError(formProps, `${name}.values`) && (
                <FormHelperText error={isError(formProps, `${name}.values`)}>
                    {get(errors, `${name}.values`)}
                </FormHelperText>
            )}
            <Box sx={{ marginTop: '8px', display: 'flex', gap: '8px' }}>
                <Button size="small" endIcon={<PlusIcon />} variant="contained-blue" onClick={onAddFilter}>
                    Add Filter
                </Button>
                <Button size="small" endIcon={<GroupIcon />} variant="contained-blue" onClick={onAddGroupFilter}>
                    Add Group Filter
                </Button>
            </Box>
        </Box>
    );
};

const parseArrayValue = (value: string | any[]) => {
    if (Array.isArray(value)) {
        return value;
    }
    if (typeof value === 'string' && value) {
        return value.split(',').map((item) => item.trim());
    }
    return [];
};
export const FilterValue = ({
    name,
    formProps,
    attributeSchemaType,
    handleChange,
    filterOperator,
    options = [],
    autocompleteProps = {},
    dataType,
    enumRange,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    attributeSchemaType: string;
    handleChange: any;
    filterOperator: any;
    options?: any[];
    autocompleteProps?: any;
    dataType?: string;
    enumRange?: string[];
}) => {
    const { errors, values } = formProps;
    if ([QUERY.IN, QUERY.NOT_IN].includes(filterOperator) || (enumRange && filterOperator === QUERY.EXACT)) {
        switch (attributeSchemaType) {
            case AttributeType.INTEGER_ARRAY:
            case AttributeType.FLOAT_ARRAY:
            case AttributeType.STRING_ARRAY:
                return (
                    <MultiValueInput
                        fullWidth={false}
                        type={attributeSchemaType}
                        displayName=""
                        sx={{
                            '& .MuiInputBase-root': {
                                padding: '0 6px !important',
                                minHeight: '36px',
                            },
                            '& .MuiAutocomplete-clearIndicator, .MuiAutocomplete-popupIndicator': {
                                height: '24px',
                                width: '24px',
                            },
                            '& .MuiAutocomplete-input': {
                                paddingLeft: '36px',
                            },
                        }}
                        value={parseArrayValue(get(values, name))}
                        onChange={(value) => formProps.setFieldValue(name, value)}
                        error={isError(formProps, name) ? get(errors, name) : ''}
                        onBlur={() => {
                            formProps.setFieldTouched(name);
                        }}
                        enumRange={enumRange}
                    />
                );
            case AttributeType.DATE_ARRAY:
            case AttributeType.DATE_TIME_ARRAY:
                return (
                    <DateArrayInput
                        fullWidth={false}
                        sx={{
                            '& .MuiAutocomplete-inputRoot': {
                                padding: '0 6px !important',
                                minHeight: '36px',
                            },
                        }}
                        isDateTime={attributeSchemaType === AttributeType.DATE_TIME_ARRAY}
                        displayName=""
                        value={parseArrayValue(get(values, name))}
                        onChange={(value) => formProps.setFieldValue(name, value)}
                        error={isError(formProps, name) ? get(errors, name) : ''}
                        onBlur={() => {
                            formProps.setFieldTouched(name);
                        }}
                    />
                );
            default:
                return (
                    <Autocomplete
                        multiple={![QUERY.EQUAL, QUERY.EXACT].includes(filterOperator)}
                        size="small"
                        freeSolo
                        options={options}
                        value={parseArrayValue(get(values, name))}
                        sx={{
                            '& .MuiIconButton-root': {
                                height: '24px',
                                width: '24px',
                            },
                            '& .MuiOutlinedInput-root': {
                                minWidth: 200,
                            },
                        }}
                        onChange={(_, newValue: any) => formProps.setFieldValue(name, newValue)}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                size="small"
                                InputLabelProps={{ shrink: true }}
                                error={isError(formProps, name)}
                                helperText={get(errors, name)}
                            />
                        )}
                        {...autocompleteProps}
                    />
                );
        }
    }
    switch (attributeSchemaType) {
        case AttributeType.STRING:
        case AttributeType.STRING_ARRAY:
        case AttributeType.TEXT:
            if (dataType) {
                return (
                    <Box sx={{ minWidth: 200 }}>
                        <EntitySelect
                            label={''}
                            entityType={dataType}
                            onChange={(newValue) => {
                                handleChange({
                                    target: { name, value: newValue.value },
                                });
                            }}
                            defaultValue={get(values, name)}
                        />
                        {isError(formProps, name) && <FormHelperText>{get(errors, name)}</FormHelperText>}
                    </Box>
                );
            }
            return (
                <TextField
                    size="small"
                    name={name}
                    value={get(values, name)}
                    placeholder="Input value"
                    onChange={handleChange}
                    error={isError(formProps, name)}
                    helperText={get(errors, name)}
                />
            );
        case AttributeType.BOOLEAN:
            return (
                <FormControl error={isError(formProps, name)}>
                    <Checkbox
                        name={name}
                        checked={get(values, name, false)}
                        onChange={(_, checked) => handleChange({ target: { name, value: checked } })}
                        color="secondary"
                        sx={{
                            width: 'min-content',
                        }}
                    />
                    {isError(formProps, name) && <FormHelperText>{get(errors, name)}</FormHelperText>}
                </FormControl>
            );
        case AttributeType.INTEGER:
        case AttributeType.FLOAT:
        case AttributeType.LONG:
        case AttributeType.INTEGER_ARRAY:
        case AttributeType.FLOAT_ARRAY:
            return (
                <TextField
                    size="small"
                    name={name}
                    type="number"
                    value={get(values, name)}
                    placeholder="Input value"
                    onChange={handleChange}
                    error={isError(formProps, name)}
                    helperText={get(errors, name)}
                />
            );
        case AttributeType.DATE:
        case AttributeType.DATE_ARRAY:
            return (
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                        value={dateFnsParse(get(values, name), 'yyyy-MM-dd', new Date())}
                        onChange={(newValue) => {
                            handleChange({
                                target: {
                                    name,
                                    value: newValue ? formatDate(newValue.toUTCString()) : newValue,
                                },
                            });
                        }}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                size="small"
                                InputLabelProps={{ shrink: true }}
                                error={isError(formProps, name)}
                                helperText={get(errors, name)}
                            />
                        )}
                    />
                </LocalizationProvider>
            );
        case AttributeType.DATE_TIME:
        case AttributeType.DATE_TIME_ARRAY:
            return (
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateTimePicker
                        value={dateFnsParse(get(values, name), 'yyyy-MM-DD HH:mm:ss', new Date())}
                        onChange={(newValue) => {
                            handleChange({
                                target: {
                                    name,
                                    value: newValue ? formatDateTime(newValue.toUTCString()) : newValue,
                                },
                            });
                        }}
                        disabled={false}
                        renderInput={(params) => (
                            <TextField
                                size="small"
                                InputLabelProps={{ shrink: true }}
                                error={isError(formProps, name)}
                                helperText={get(errors, name)}
                            />
                        )}
                    />
                </LocalizationProvider>
            );
        default:
            return (
                <TextField
                    size="small"
                    name={name}
                    value={get(values, name)}
                    placeholder="Input value"
                    onChange={handleChange}
                    error={isError(formProps, name)}
                    helperText={get(errors, name)}
                />
            );
    }
};
export const Filter = ({
    name,
    formProps,
    schemaDetail,
    fieldPrefix = '',
    filterOptions,
    selectedRelation,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
    fieldPrefix?: string;
    filterOptions?: any[];
    selectedRelation?: RelationRowData;
}) => {
    const { errors, values } = formProps;
    const filter = get(values, name);
    const filterType = get(filter, 'filterType', '');
    return (
        <Box sx={{ display: 'flex', gap: '8px', position: 'relative', width: '100%' }}>
            <FormControl error={isError(formProps, `${name}.filterType`)}>
                <Select
                    size="small"
                    name={`${name}.filterType`}
                    MenuProps={MENU_PROPS}
                    inputProps={{ 'aria-label': 'Without label' }}
                    onChange={(e) => {
                        const selectedFilterType = e.target.value;
                        if (selectedFilterType === 'relation') {
                            formProps.setFieldValue(
                                name,
                                {
                                    ...INITIAL_CONDITION,
                                    filterType: 'relation',
                                },
                                false
                            );
                        } else if (selectedFilterType === 'relationExists') {
                            formProps.setFieldValue(name, {
                                filterType: selectedFilterType,
                                operator: QUERY.IS_NON_NULL,
                                values: {
                                    field: `${fieldPrefix}id`,
                                },
                            });
                        } else {
                            formProps.setFieldValue(
                                name,
                                {
                                    values: EMPTY_FILTER_VALUE,
                                    filterType: selectedFilterType,
                                },
                                false
                            );
                        }
                    }}
                    displayEmpty
                    value={filterType}
                    renderValue={(selected: any) => {
                        return selected ? FILTER_OPTION_LABEL[selected] : 'Select Filter';
                    }}
                >
                    {(filterOptions || FILTER_OPTIONS).map(({ value, label }) => (
                        <MenuItem value={value} key={`filter-${value}`}>
                            {label}
                        </MenuItem>
                    ))}
                </Select>
                {isError(formProps, `${name}.filterType`) && (
                    <FormHelperText>{get(errors, `${name}.filterType`)}</FormHelperText>
                )}
            </FormControl>
            {isAttributeFilter(filterType) && (
                <AttributeFilter
                    fieldPrefix={fieldPrefix}
                    formProps={formProps}
                    name={name}
                    schemaDetail={schemaDetail}
                />
            )}
            {isRelationAttributeFilter(filterType) && (
                <RelationAttributeFilter
                    fieldPrefix={`${fieldPrefix}relationAttr:`}
                    formProps={formProps}
                    name={name}
                    schemaDetail={schemaDetail}
                    selectedRelation={selectedRelation}
                />
            )}
            {isRelationFilter(filterType) && (
                <>
                    <RelationFilter formProps={formProps} name={name} schemaDetail={schemaDetail} />
                </>
            )}

            {isLifecycleStateFilter(filterType) && (
                <LifecycleStateFilter name={name} formProps={formProps} schemaDetail={schemaDetail} />
            )}

            {isClassificationFilter(filterType) && (
                <ClassificationFilter name={name} formProps={formProps} schemaDetail={schemaDetail} />
            )}
        </Box>
    );
};
