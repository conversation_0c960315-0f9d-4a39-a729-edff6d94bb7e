/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { FormControl, FormHelperText, MenuItem, Select } from 'ui-style';
import { FormikProps } from 'formik';
import { SchemaWithLifeCycleDetail } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import {
    getDataType,
    getEnumRange,
    isAttributeSelected,
    isError,
    isFilterWithInputValue,
} from '../../utils/advancedSearchHelper';
import {
    AdvancedFilterFormProps,
    FilterOperator,
    FilterValue,
    getDefaultConditionForAttribute,
    getDefaultValueForAttribute,
    MENU_PROPS,
} from './FilterComponents';

const AttributeFilter = ({
    name,
    formProps,
    schemaDetail,
    fieldPrefix = '',
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
    fieldPrefix?: string;
}) => {
    const { errors, values } = formProps;
    const filter = get(values, name);
    const filterField = `${get(filter, 'values.field')}`;
    const filterFieldWithoutPrefix = filterField?.replace(fieldPrefix, '');
    const filterOperator = get(filter, 'operator');
    const filterType = get(filter, 'filterType', '');

    const attributes = schemaDetail?.attributes;
    const dataType = getDataType(attributes[filterFieldWithoutPrefix]);
    const enumRange = getEnumRange(attributes[filterFieldWithoutPrefix]);

    return (
        <>
            <FormControl error={isError(formProps, `${name}.values.field`)}>
                <Select
                    size="small"
                    name={`${name}.values.field`}
                    MenuProps={MENU_PROPS}
                    value={filterField}
                    inputProps={{ 'aria-label': 'Without label' }}
                    onChange={(e) => {
                        const attributeSchema = attributes[e.target.value?.replace(fieldPrefix, '')];
                        formProps.setFieldValue(
                            `${name}.values.value`,
                            getDefaultValueForAttribute(attributeSchema),
                            false
                        );
                        formProps.setFieldValue(
                            `${name}.operator`,
                            getDefaultConditionForAttribute(attributeSchema),
                            false
                        );
                        formProps.handleChange(e);
                    }}
                    displayEmpty
                    renderValue={(selected: any) => {
                        return selected
                            ? get(
                                  attributes,
                                  `${selected?.replace(fieldPrefix, '')}.displayName`,
                                  selected?.replace(fieldPrefix, '')
                              )
                            : 'Select Attribute';
                    }}
                >
                    {Object.values(attributes).map((attribute) => (
                        <MenuItem key={attribute.name} value={`${fieldPrefix}${attribute.name}`}>
                            {attribute.displayName || attribute.name}
                        </MenuItem>
                    ))}
                </Select>
                {isError(formProps, `${name}.values.field`) && (
                    <FormHelperText>{get(errors, `${name}.values.field`)}</FormHelperText>
                )}
            </FormControl>
            {isAttributeSelected(filterType, filterFieldWithoutPrefix, attributes) && (
                <FilterOperator
                    name={`${name}.operator`}
                    value={filterOperator}
                    formProps={formProps}
                    dataType={dataType}
                    enumRange={enumRange}
                    type={attributes[filterFieldWithoutPrefix].type}
                />
            )}
            {isFilterWithInputValue(filterType, filterFieldWithoutPrefix, attributes, filterOperator) && (
                <FilterValue
                    name={`${name}.values.value`}
                    attributeSchemaType={attributes[filterFieldWithoutPrefix]?.type}
                    dataType={dataType}
                    enumRange={enumRange}
                    handleChange={formProps.handleChange}
                    formProps={formProps}
                    filterOperator={filterOperator}
                    options={enumRange}
                />
            )}
        </>
    );
};

export default AttributeFilter;
