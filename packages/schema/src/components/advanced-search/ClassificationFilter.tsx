/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, CircularProgress, FormControl, FormHelperText, MenuItem, Select } from 'ui-style';
import { FormikProps } from 'formik';
import { AdvancedFilterFormProps, MENU_PROPS } from './FilterComponents';
import { SchemaWithLifeCycleDetail, useClassificationDetail } from '@tripudiotech/admin-caching-store';
import { isError } from '../../utils/advancedSearchHelper';
import get from 'lodash/get';
import { useEffect, useMemo, useState } from 'react';
import { ClassificationDetail, Classification } from '@tripudiotech/admin-api';
import { Loading } from '@tripudiotech/admin-styleguide';
import keyBy from 'lodash/keyBy';
import AttributeFilter from './AttributeFilter';

const isAccessible = (classification) => classification?.permissions?.canEdit || classification?.permissions?.canRead;

const ClassificationFilter = ({
    name,
    formProps,
    schemaDetail,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [availableClassifications, getClassification, getSchemaClassification] = useClassificationDetail((state) => [
        state.schemaClassification[schemaDetail?.entityType?.name],
        state.getClassification,
        state.getSchemaClassification,
    ]);
    const [classificationSchema, setClassificationSchema] = useState<ClassificationDetail>();
    const classificationMap = useMemo<Record<string, Classification>>(
        () => (availableClassifications ? keyBy(availableClassifications, 'id') : null),
        [availableClassifications]
    );

    const classificationField = `${name}.values.classification`;
    const selectedClassificationId = useMemo(
        () => get(formProps.values, `${name}.values.classification`),
        [formProps.values]
    );
    const availableAttributes = classificationSchema?.attributes;

    useEffect(() => {
        if (schemaDetail?.entityType?.name) {
            getSchemaClassification(schemaDetail.entityType.name);
        }
    }, [schemaDetail?.entityType?.name]);

    useEffect(() => {
        if (selectedClassificationId && classificationMap) {
            const selectedClassification = classificationMap[selectedClassificationId];

            // Opened from saved advanced search, and the viewer doesn't have permissions to access the classification
            if (selectedClassification && !isAccessible(selectedClassification)) {
                formProps.setFieldValue(`${name}.values.field`, '');
                formProps.setFieldError(
                    `${name}.values.field`,
                    `You don't have permission to view ${selectedClassification.name} classification`
                );
                return;
            }

            // Fetch classification schema to set attributes
            if (selectedClassification) {
                const getClassificationDetail = async () => {
                    setIsLoading(true);
                    const data = await getClassification(classificationMap[selectedClassificationId].name);
                    setClassificationSchema(data);
                    setIsLoading(false);
                };
                getClassificationDetail();
                return;
            }

            // Classification might be removed
            formProps.setFieldValue(`${name}.values.field`, '');
            formProps.setFieldError(`${name}.values.field`, 'Classification has been removed');
        }
    }, [selectedClassificationId, classificationMap]);

    return (
        <>
            <FormControl error={isError(formProps, classificationField)}>
                <Select
                    size="small"
                    name={classificationField}
                    MenuProps={MENU_PROPS}
                    value={selectedClassificationId}
                    onChange={(e) => {
                        formProps.setFieldValue(`${name}.values.field`, '');
                        formProps.setFieldValue(`${name}.values.value`, null);
                        formProps.handleChange(e);
                    }}
                    displayEmpty
                    renderValue={(selected: any) => {
                        return !availableClassifications
                            ? 'Loading...'
                            : selected
                            ? classificationMap[selected]?.name || 'Classification has been removed'
                            : 'Select Classification';
                    }}
                >
                    {availableClassifications ? (
                        availableClassifications
                            .filter((classification) => isAccessible(classification))
                            .map((classification) => (
                                <MenuItem key={classification.name} value={classification.id}>
                                    {classification.name}
                                </MenuItem>
                            ))
                    ) : (
                        <Box sx={{ padding: '24px' }}>
                            <Loading />
                        </Box>
                    )}
                </Select>
                {isError(formProps, classificationField) && (
                    <FormHelperText>{get(formProps.errors, classificationField)}</FormHelperText>
                )}
            </FormControl>
            {get(formProps.values, classificationField) && isLoading && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CircularProgress size={24} />
                </Box>
            )}
            {get(formProps.values, classificationField) && availableAttributes && (
                <AttributeFilter
                    schemaDetail={{ attributes: availableAttributes } as any}
                    fieldPrefix={`classification.${selectedClassificationId}.`}
                    name={name}
                    formProps={formProps}
                />
            )}
        </>
    );
};

export default ClassificationFilter;
