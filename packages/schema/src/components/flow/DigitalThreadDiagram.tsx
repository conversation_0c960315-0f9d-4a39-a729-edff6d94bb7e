/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactFlow, { getConnectedEdges, OnSelectionChangeParams, useReactFlow } from 'reactflow';
import OrthogonalEdge from './OrthogonalEdge';
import BaseNode from './BaseNode';
import {
    Box,
    CenterIcon,
    DocumentIcon,
    Typography,
    DownloadIcon,
    IconButton,
    MinusIcon,
    PlusIcon,
    TextField,
    InputAdornment,
    SearchIcon,
    CloseIcon,
} from 'ui-style';
import { ZOOM_OPTIONS } from '../../constants';
import { toPng } from 'html-to-image';
import download from 'downloadjs';
import { AgGridReact } from '@ag-grid-community/react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { useSchemaTree } from '@tripudiotech/admin-caching-store';
import { notifyError, getContextMenuCallback, getIndentClass, TreeContainer } from '@tripudiotech/admin-styleguide';
import { getLayoutedElements } from '../../utils/graphUtils';

const EntityTypeRenderer = ({ value, data }) => {
    const entityType = get(data, 'name');
    return (
        <Box className="item" sx={{ display: 'flex', alignItems: 'center' }}>
            <DocumentIcon />
            <Typography variant="label2-sem">{value}</Typography>
            <Typography variant="label2-reg">({entityType})</Typography>
        </Box>
    );
};

const SchemaTree = () => {
    const [searchText, setSearchText] = useState('');
    const gridRef = useRef<AgGridReact>();
    const { schemaTreeMap } = useSchemaTree();
    const { setNodes, getNodes, fitView, getEdges } = useReactFlow();
    const schemaTree = useMemo(() => (schemaTreeMap ? Object.values(schemaTreeMap) : null), [schemaTreeMap]);

    const onSearchChanged = useCallback((e) => {
        const { value } = e.target;
        setSearchText(value);
    }, []);

    const getDataPath = useCallback((data) => {
        return data.path || [];
    }, []);

    const isGroupOpenByDefault = useCallback((params) => {
        return params.level === 0;
    }, []);

    const onRowGroupOpened = useCallback(() => {
        gridRef.current.columnApi.autoSizeAllColumns();
    }, []);

    const autoGroupColumnDef = useMemo(
        () => ({
            field: 'displayName',
            headerName: 'Name',
            cellClass: getIndentClass,
            minWidth: 308,
            filter: 'agTextColumnFilter',
            rowDrag: true,
            cellRendererParams: {
                innerRenderer: EntityTypeRenderer,
                // onClick: onSelectChange,
                suppressCount: true,
            },
            cellStyle: (params) => {
                const { level } = params.node;
                const groupCell = params.value === params.node.key;
                const indent = 24; // change this value to your liking

                return groupCell
                    ? {
                          border: 'none',
                          paddingLeft: '-' + indent * (level + 1) + 'px',
                      }
                    : {
                          border: 'none',
                      };
            },
        }),
        []
    );

    const addDropZone = useCallback(
        (params) => {
            const dropZone = {
                getContainer: () => document.querySelector(`#digital-thread-diagram`),
                onDragStop: (e) => {
                    const nodeData = e.node.data;
                    const entityType = nodeData.name;
                    if (getNodes().some((n) => n.id === entityType)) {
                        notifyError(`Node ${nodeData.displayName || entityType} already exists`);
                    } else {
                        const newNode = {
                            id: entityType,
                            data: {
                                label: nodeData.displayName || entityType,
                                name: entityType,
                            },
                            position: { x: 0, y: 0 },
                            type: 'baseNode',
                        };
                        const newNodes = [...getNodes(), newNode];
                        const edges = getEdges();
                        const layout = getLayoutedElements(newNodes, edges, 'LR');

                        setNodes(layout.nodes);
                        setTimeout(() => {
                            fitView({ duration: 300, padding: 0.2 });
                        }, 200);
                    }
                },
            };
            params.api.addRowDropZone(dropZone);
        },
        [fitView]
    );

    const gridOptions: any = useMemo(
        () => ({
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                filter: true,
                floatingFilter: false,
                flex: 1,
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'displayName',
                    headerName: 'Display Name',
                    filter: 'agTextColumnFilter',
                    hide: true,
                },
            ],
            components: {},
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            isGroupOpenByDefault,
            onRowGroupOpened,
            onExpandOrCollapseAll: onRowGroupOpened,
            getContextMenuItems: getContextMenuCallback(gridRef),
            getDataPath,
            treeData: true,
            getRowStyle: () => ({
                background: ' #FFFFFF',
                border: 'none',
            }),
            autoGroupColumnDef,
            gridOptions: {
                headerHeight: 0,
                rowHeight: 26,
            },
            enableCellChangeFlash: true,
            onGridReady: addDropZone,
        }),

        [addDropZone]
    );

    useEffect(() => {
        if (gridRef.current?.api) {
            gridRef.current.api.setFilterModel({
                displayName: {
                    filterType: 'text',
                    type: 'contains',
                    filter: searchText,
                },
            });
        }
    }, [searchText]);

    return (
        <TreeContainer
            sx={{
                minWidth: '300px',
                background: '#FFFFFF',
                margin: '16px',
                height: '95%',
                borderRadius: '8px',
                '& .ag-theme-alpine .ag-root-wrapper': {
                    border: 'none',
                },
                border: 'none',
                paddingBottom: '16px',
            }}
        >
            <TextField
                className="searchBox"
                size="medium"
                value={searchText}
                onChange={onSearchChanged}
                fullWidth
                placeholder="Type to search"
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <SearchIcon />
                        </InputAdornment>
                    ),
                    endAdornment: (
                        <InputAdornment
                            position="end"
                            style={{
                                visibility: isEmpty(searchText) ? 'hidden' : 'visible',
                                cursor: 'pointer',
                            }}
                            onClick={() => setSearchText('')}
                        >
                            <CloseIcon sx={{ width: '16px', height: '16px' }} />
                        </InputAdornment>
                    ),
                }}
            />
            <div className="agContainer ag-theme-alpine">
                <AgGridReact ref={gridRef} {...gridOptions} rowData={schemaTree} />
            </div>
        </TreeContainer>
    );
};

const DigitalThreadDiagram = ({
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    id = 'detail-view',
    isReadOnly = false,
}) => {
    const config = useMemo(
        () => ({
            fitView: true,
            nodeTypes: { baseNode: BaseNode },
            elementsSelectable: true,
            edgeTypes: {
                orthogonalEdge: OrthogonalEdge,
            },
        }),
        []
    );
    const { setEdges, getEdges, zoomIn, zoomOut, fitView } = useReactFlow();
    const onSelectionChange = useCallback(
        (e: OnSelectionChangeParams) => {
            const { nodes: selectedNodes, edges: selectedEdges } = e;
            if (selectedNodes.length === 0 && edges.length === 0) {
                setEdges(getEdges().map((edge) => ({ ...edge, animated: false, zIndex: 1, style: {} })));
                return;
            }

            if (selectedNodes.length > 0) {
                const connectedEdges = getConnectedEdges(selectedNodes, getEdges());
                setEdges(
                    getEdges().map((edge) => {
                        const isSelected = connectedEdges.some((ce) => ce.id === edge.id);
                        return {
                            ...edge,
                            animated: isSelected,
                            zIndex: isSelected ? 2 : 1,
                            style: isSelected ? { stroke: '#2E54EB', strokeWidth: 2 } : {},
                        };
                    })
                );
            }

            if (selectedEdges.length > 0) {
                setEdges(
                    getEdges().map((edge) => {
                        const isSelected = selectedEdges.some((ce) => ce.id === edge.id);
                        return {
                            ...edge,
                            animated: isSelected,
                            zIndex: isSelected ? 2 : 1,
                            style: isSelected ? { stroke: '#2E54EB', strokeWidth: 2 } : {},
                        };
                    })
                );
            }
        },
        [setEdges, getEdges]
    );
    const downloadImage = () => {
        toPng(document.querySelector(`#${id}`) as any, {
            filter: (node) => {
                // we don't want to add the minimap and the controls to the image
                if (
                    node?.classList?.contains('react-flow__minimap') ||
                    node?.classList?.contains('react-flow__controls') ||
                    node?.classList?.contains('hiden-on-export') ||
                    node?.classList?.contains('toolbar-reactflow')
                ) {
                    return false;
                }

                return true;
            },
        }).then((blob) => {
            download(blob, `exported-digital-thread.png`);
        });
    };
    return (
        <>
            {!isReadOnly && <SchemaTree />}
            <ReactFlow
                {...config}
                edges={edges}
                nodes={nodes}
                selectionOnDrag
                fitView
                onSelectionChange={onSelectionChange}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                style={{ width: '100%' }}
                id="digital-thread-diagram"
            >
                <Box
                    sx={{
                        position: 'absolute',
                        bottom: 16,
                        left: 16,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '8px',
                        zIndex: 1001,
                        '& .actionBtn': {
                            color: (theme) => theme.palette.glide.text.normal.main,
                        },
                    }}
                    className="toolbar-reactflow"
                >
                    <IconButton className="actionBtn" size="small" onClick={() => zoomIn(ZOOM_OPTIONS)}>
                        <PlusIcon />
                    </IconButton>
                    <IconButton className="actionBtn" size="small" onClick={() => zoomOut(ZOOM_OPTIONS)}>
                        <MinusIcon />
                    </IconButton>
                    <IconButton className="actionBtn" size="small" onClick={() => fitView(ZOOM_OPTIONS)}>
                        <CenterIcon />
                    </IconButton>
                    <IconButton className="actionBtn" size="small" onClick={downloadImage}>
                        <DownloadIcon />
                    </IconButton>
                </Box>
            </ReactFlow>
        </>
    );
};

export default DigitalThreadDiagram;
