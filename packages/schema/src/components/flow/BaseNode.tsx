/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { getConnectedEdges, Handle, NodeProps, NodeToolbar, Position, useReactFlow } from 'reactflow';
import { Box, Chip, DeleteIcon, IconButton, Menu, MenuItem, SchemaFieldSection, TreeIcon, Typography } from 'ui-style';
import useDigitalThreadStore from '../../store/useDigitalThreadStore';
import { RelationType } from 'ui-common';
import { getLayoutedElements } from '../../utils/graphUtils';

const Toolbar = ({ schemaDetail, nodeProps }: { schemaDetail: any; nodeProps: NodeProps }) => {
    const relations = useMemo(() => {
        return schemaDetail ? schemaDetail.relationTypes : [];
    }, [schemaDetail]);
    const { getNodes, getEdges, fitView, getNode, setNodes, setEdges } = useReactFlow();
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const onClose = () => {
        setAnchorEl(null);
    };
    const onOpen = (e: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(e.currentTarget);
    };

    const fit = useCallback(() => {
        setTimeout(() => {
            fitView({ duration: 300, padding: 0.2 });
        }, 200);
    }, [fitView]);
    const expand = useCallback(
        (relation: RelationType) => {
            onClose();
            const isOutgoing = relation.direction?.toLowerCase() === 'outgoing';
            const nodeName = isOutgoing ? relation.toEntityType : relation.fromEntityType;
            const newNode = {
                id: nodeName,
                data: {
                    name: nodeName,
                    label: nodeName,
                },
                position: { x: 0, y: 0 },
                type: 'baseNode',
            };
            const fromEntityType = isOutgoing ? nodeProps.data.name : newNode.data.name;
            const toEntityType = !isOutgoing ? nodeProps.data.name : newNode.data.name;
            const newEdge = {
                id: `${fromEntityType}-${relation.name}-${toEntityType}`,
                source: isOutgoing ? nodeProps.id : newNode.id,
                target: isOutgoing ? newNode.id : nodeProps.id,
                data: {
                    ...relation,
                    fromEntityType,
                    toEntityType,
                },
                label: relation.displayName,
                type: 'orthogonalEdge',
                markerEnd: { type: 'arrow' },
            };
            const newNodes = [...getNodes(), newNode];
            const newEdges = [...getEdges(), newEdge];
            const layout = getLayoutedElements(newNodes, newEdges, 'LR');

            setNodes(layout.nodes);
            setEdges(layout.edges);
            fit();
        },
        [nodeProps, setNodes, setEdges, getNodes, getEdges, fit]
    );

    const handleExpand = useCallback(
        (relation) => {
            return () => expand(relation);
        },
        [nodeProps, expand]
    );

    const onDelete = () => {
        const currentNode = getNode(nodeProps.id);
        const connectedEdges = getConnectedEdges([currentNode], getEdges());
        const newNodes: any = [...getNodes()].filter((node) => node.id !== currentNode.id);
        const newEdges: any = [...getEdges()].filter((edge) =>
            connectedEdges.every((connectedEdge) => connectedEdge.id !== edge.id)
        );
        setNodes(newNodes);
        setEdges(newEdges);
        fit();
    };

    return (
        <>
            <IconButton size="small" color="error" onClick={onDelete}>
                <DeleteIcon />
            </IconButton>
            <IconButton onClick={onOpen} disabled={!schemaDetail} size="small" color="secondary">
                <TreeIcon />
            </IconButton>
            <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={onClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
                sx={{
                    '& .MuiPaper-root': {
                        minWidth: '200px',
                        maxHeight: '400px',
                    },
                }}
            >
                {open && (
                    <>
                        <Box
                            onKeyDown={(e) => e.stopPropagation()}
                            sx={{
                                position: 'sticky',
                                zIndex: 2000,
                                top: 0,
                                background: (theme) => theme.palette.glide.background.normal.quarternary,
                                padding: '8px 16px',
                                '> *': {
                                    color: 'white',
                                },
                            }}
                        >
                            <SchemaFieldSection>Select a relationship to expand</SchemaFieldSection>
                        </Box>
                        {relations.map((rel) => (
                            <MenuItem
                                key={`${rel.fromEntityType}-${rel.name}-${rel.toEntityType}`}
                                sx={{ display: 'flex', gap: '8px' }}
                                onClick={handleExpand(rel)}
                            >
                                <Chip size="small" variant="status-info" label={rel.fromEntityType} />
                                <Typography variant="bo2">{rel.displayName}</Typography>
                                <Chip size="small" variant="status-success" label={rel.toEntityType} />
                            </MenuItem>
                        ))}
                    </>
                )}
            </Menu>
        </>
    );
};

const BaseNode = (props: NodeProps) => {
    const selected = props.selected;
    const isReadOnly = useDigitalThreadStore((state) => state.isReadOnly && !state.isOpenCreate);
    const label = props.data.label;
    const [schemaDetail, getSchema] = useSchemaDetail((state) => [state.schema[props.data.name], state.getSchema]);

    useEffect(() => {
        getSchema(props.data.name);
    }, [getSchema]);

    return (
        <Box
            sx={{
                '& .nodeBody': {
                    width: '120px',
                    height: '56px',
                    border: (theme) => `2px solid ${theme.palette.glide.info}`,
                    position: 'relative',
                    overflow: 'hidden',
                    borderRadius: '8px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    boxShadow: selected ? '0px 0px 24px rgba(47, 84, 235, 0.23)' : 'none',
                    background: (theme) =>
                        selected ? theme.palette.info.main : theme.palette.glide.background.normal.white,
                },
                '& .sourceHandle': {
                    width: '8px',
                    height: '8px',
                    borderRadius: '4px',
                    backgroundColor: (theme) =>
                        selected ? theme.palette.info.main : theme.palette.glide.background.normal.inverseTertiary,
                },
                '& .targetHandle': {
                    width: 0,
                    height: 0,
                    left: 0,
                    opacity: 0,
                },
                '& .label': {
                    textAlign: 'center',
                    color: (theme) =>
                        selected ? theme.palette.glide.text.normal.secondary : theme.palette.glide.text.normal.main,
                    wordBreak: 'break-word',
                    fontSize: '12px',
                },
            }}
        >
            <Box className="nodeBody">
                <Typography variant="label2-med" className="label">
                    {schemaDetail?.entityType?.displayName || label}
                </Typography>
            </Box>
            <Handle className="targetHandle" type="target" position={Position.Left} />
            <Handle className="sourceHandle" type="source" position={Position.Right} id="b" />
            {!isReadOnly && (
                <NodeToolbar isVisible={selected} position={Position.Bottom}>
                    <Toolbar schemaDetail={schemaDetail} nodeProps={props} />
                </NodeToolbar>
            )}
        </Box>
    );
};

export default BaseNode;
