/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useSchemaTree } from '@tripudiotech/admin-caching-store';
import { matchSorter } from 'match-sorter';
import React, { useCallback, useMemo, useState } from 'react';
import { Box, Button, CircularProgress, Menu, MenuItem, PlusIcon, TextField, useDebounce } from 'ui-style';
import { useNodes, useReactFlow } from 'reactflow';
import { EntityType } from 'ui-common';

const DefaultEmptyNode = () => {
    const [anchorEl, setAnchorEl] = useState(null);
    const nodes = useNodes();
    const { setNodes } = useReactFlow();
    const isVisible = nodes.length === 0;
    const open = Boolean(anchorEl);
    const [searchText, setSearchText] = useState(null);
    const debouncedSearchText = useDebounce(searchText, 200);
    const { fitView } = useReactFlow();
    const [isLoaded, entityTypes] = useSchemaTree((state) => [
        state.isLoaded,
        Object.values(state.schemaTreeMap || {}),
    ]);
    const options = useMemo(() => {
        return debouncedSearchText
            ? matchSorter(entityTypes, debouncedSearchText, { keys: ['name', 'description', 'displayName'] })
            : entityTypes;
    }, [debouncedSearchText, entityTypes]);

    const onClose = () => {
        setAnchorEl(null);
    };
    const onOpen = (e: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(e.currentTarget);
    };

    const onSearchChange = (e) => {
        setSearchText(e.target.value);
    };

    const fit = useCallback(() => {
        setTimeout(() => {
            fitView({ duration: 300, padding: 0.2 });
        }, 100);
    }, [fitView]);

    const add = useCallback(
        (entityType: EntityType) => {
            const name = entityType.name;
            setNodes([
                {
                    id: name,
                    data: {
                        name,
                        label: name,
                    },
                    position: {
                        x: 0,
                        y: 0,
                    },
                    type: 'baseNode',
                },
            ]);
            fit();
            setAnchorEl(null);
        },
        [fit]
    );

    return (
        <Box>
            {isVisible && (
                <>
                    <Button
                        onClick={onOpen}
                        color="primary"
                        variant="contained"
                        disabled={!isLoaded}
                        endIcon={isLoaded ? <PlusIcon /> : <CircularProgress size={12} />}
                    >
                        Select an Entity Type
                    </Button>
                    <Menu
                        id="basic-menu"
                        anchorEl={anchorEl}
                        open={open}
                        disableAutoFocus
                        disableAutoFocusItem
                        onClose={onClose}
                        MenuListProps={{
                            'aria-labelledby': 'basic-button',
                        }}
                        sx={{
                            '& .MuiPaper-root': {
                                minWidth: '200px',
                                maxHeight: '400px',
                            },
                        }}
                    >
                        <Box
                            onKeyDown={(e) => e.stopPropagation()}
                            sx={{ position: 'sticky', zIndex: 2000, top: 0, background: '#FFFFFF' }}
                        >
                            <TextField
                                inputProps={{ 'aria-autocomplete': 'none' }}
                                placeholder="Type to search..."
                                value={searchText}
                                onChange={onSearchChange}
                                fullWidth
                                focused={false}
                            />
                        </Box>
                        {options.map((entityType) => (
                            <MenuItem onClick={() => add(entityType)} key={entityType.name}>
                                {entityType.displayName}
                            </MenuItem>
                        ))}
                    </Menu>
                </>
            )}
        </Box>
    );
};

export default DefaultEmptyNode;
