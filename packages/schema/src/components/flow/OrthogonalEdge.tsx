/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { BaseEdge, useNodes } from 'reactflow';
import { calculateOrthogonalPath } from '../../utils/graphUtils';

const OrthogonalEdge = ({
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    selected,
    style = {},
    markerEnd,
    data,
    label = '',
    ...others
}: any) => {
    const nodes = useNodes();
    const { path, labelX, labelY } = calculateOrthogonalPath(sourceX, sourceY, targetX, targetY, nodes);
    return (
        <>
            <BaseEdge
                id={id}
                path={path}
                style={style}
                markerEnd={markerEnd}
                label={label}
                labelY={labelY}
                labelX={labelX}
                labelStyle={{
                    fontSize: '10px',
                    fontWeight: 500,
                }}
                labelBgStyle={{
                    fill: '#F7F8FC',
                }}
            />
        </>
    );
};

export default OrthogonalEdge;
