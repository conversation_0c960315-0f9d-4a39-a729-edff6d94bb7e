/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { AttributeSchema, formatDateTime, ISchemaDetail } from 'ui-common';
import { AttributeSchemaDetail, Chip, Grid, SchemaField, SchemaFieldSection, Box, Typography, tableStyles, tableIcons, Loading, NoRowsOverlay, styled, AttributeContext } from 'ui-style';
import { SchemaReference } from '@tripudiotech/admin-api';
import get from 'lodash/get';
import { useUnitOfMeasure } from '@tripudiotech/admin-caching-store';
import { Link } from 'react-router-dom';
import { AgGridReact } from '@ag-grid-community/react';

const ContentWrapper = styled(Box)(() => ({
    ...tableStyles,
    minHeight: '200px',
    height: '100%',
    width: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const getShemaDetailUrl = (schema: SchemaReference) => {
    switch (schema.type) {
        case 'Classification':
            return `/classification/${schema.name}/metadata`;
        case 'EntityType':
            return `/schema/${schema.name}/metadata`;
        case 'RelationType':
            return `/schema/${schema.fromEntityType}/relationship/Outgoing?relation=${schema.name}&view=detail&fromEntityType=${schema.fromEntityType}&toEntityType=${schema.toEntityType}`;
        default:
            return null;
    }
};

const SchemaNameRenderer = ({ data }: { data: SchemaReference }) => {
    const schemaDetailUrl = getShemaDetailUrl(data);
    return (
        <Typography
            sx={{
                fontSize: '0.875rem',
                color: (theme) => theme.palette.info.main,
                alignItems: 'center',
                display: 'flex',
                height: '100%',
            }}
        >
            <Link to={schemaDetailUrl} style={{ color: 'inherit' }}>
                {data.displayName || data.name}
            </Link>
        </Typography>
    );
}

const SchemaTable = ({ schemas }: { schemas: SchemaReference[] }) => {
    const gridOptions = {
        headerHeight: 34,
        rowHeight: 34,
        loadingOverlayComponent: Loading,
        animateRows: true,
        defaultColDef: {
            sortable: true,
            resizable: true,
            flex: 1,
            filter: true,
            floatingFilter: false,
            cellStyle: () => ({
                display: 'block',
            }),
        },
        columnDefs: [
            {
                field: 'displayName',
                headerName: 'Name',
                filter: 'agTextColumnFilter',
                cellRenderer: SchemaNameRenderer,
            },
            {
                field: 'type',
                headerName: 'Type',
                filter: 'agTextColumnFilter',
            },
            {
                field: 'description',
                headerName: 'Description',
                filter: 'agTextColumnFilter',
                flex: 2,
            },
        ],
        icons: tableIcons,
        suppressRowClickSelection: true,
    }

    return (
        <ContentWrapper>
            <Box className="ag-theme-alpine" sx={{ height: '100%' }}>
                <AgGridReact
                    {...gridOptions}
                    rowData={schemas}
                    noRowsOverlayComponent={NoRowsOverlay}
                    noRowsOverlayComponentParams={{
                        message: 'The attribute is not used by any schema.',
                    }}
                />
            </Box>
        </ContentWrapper>
    )
}

const AttributeInformation = ({
    attributeDetail,
    schemaDetail,
    schemas,
    context = 'entity',
}: {
    attributeDetail: AttributeSchema;
    schemaDetail?: ISchemaDetail;
    schemas?: SchemaReference[];
    context?: AttributeContext;
}) => {
    const [qdtUnitMapper, qdtKindMapper] = useUnitOfMeasure((state) => [state.qdtUnitMapper, state.qdtKindMapper]);
    return (
        <>
            <SchemaFieldSection>General Information</SchemaFieldSection>
            <Grid container spacing={2}>
                <Grid item xs={12}>
                    <SchemaField label="Name" value={get(attributeDetail, 'name')} />
                    <SchemaField label="Display Name" value={get(attributeDetail, 'displayName')} />
                    <SchemaField
                        label="Type"
                        value={
                            <Chip variant="status-info-outlined" size="small" label={get(attributeDetail, 'type')} />
                        }
                    />
                    <SchemaField label="Description" value={get(attributeDetail, 'description')} />
                    <SchemaField label="Created Date" value={formatDateTime(get(attributeDetail, 'createdAt'))} />
                    <SchemaField label="Updated Date" value={formatDateTime(get(attributeDetail, 'updatedAt'))} />
                    <SchemaField label="Created By" value={get(attributeDetail, 'createdBy')} />
                </Grid>
            </Grid>

            <AttributeSchemaDetail
                attribute={attributeDetail}
                qdtUnitMapper={qdtUnitMapper}
                qdtKindMapper={qdtKindMapper}
                schemaDetail={schemaDetail}
                context={context}
            />

            {schemas &&
                <>
                    <SchemaFieldSection>Referenced By</SchemaFieldSection>
                    <SchemaTable schemas={schemas} />
                </>
            }
        </>
    );
};
export default AttributeInformation;
