/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useRef } from 'react';
import { RelationType, RelationTypeEvent, useToggle } from 'ui-common';
import { EditIcon, IconButton, MainTooltip, RightTray } from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { updateRelation } from '../../actions';
import useLocalStore from '../../store/useLocalStore';
import { useSchemaDetail, useSchemaTree } from '@tripudiotech/admin-caching-store';
import RelationForm from '../relation/RelationForm';
import { buildAndOperatorQuery, buildInQuery } from '@tripudiotech/admin-api';
import { convertFormValuesToQuery } from '../../utils/advancedSearchHelper';

const EditRelationAction = ({
    relationType,
    isInherited,
    otherSide,
}: {
    relationType: RelationType;
    isInherited: boolean;
    otherSide: string;
}) => {
    const [open, openToggle] = useToggle(false);
    const getSchema = useSchemaDetail((state) => state.getSchema);
    const { getSchemaTree } = useSchemaTree();
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const formRef = useRef(null);
    const handleSubmit = useCallback(
        async (values: any) => {
            if (formRef.current.dirty) {
                setIsLoading(true);
                const request = {
                    description: values.description,
                    isRequired: values.isRequired,
                    isVisible: values.isVisible,
                    isSingleRelation: values.isSingleRelation,
                    displayName: values.displayName,
                    onFromSideRevise: values[RelationTypeEvent.onFromSideRevise]?.type
                        ? values[RelationTypeEvent.onFromSideRevise]
                        : null,
                    onFromSideClone: values[RelationTypeEvent.onFromSideClone]?.type
                        ? values[RelationTypeEvent.onFromSideClone]
                        : null,
                    onToSideRevise: values[RelationTypeEvent.onToSideRevise]?.type
                        ? values[RelationTypeEvent.onToSideRevise]
                        : null,
                    onToSideClone: values[RelationTypeEvent.onToSideClone]?.type
                        ? values[RelationTypeEvent.onToSideClone]
                        : null,
                    constraint: values.withSearchCriteria
                        ? {
                              searchCriteria: JSON.stringify(
                                  buildAndOperatorQuery([
                                      buildInQuery('schemaType', [relationType?.toEntityType]),
                                      convertFormValuesToQuery(values.condition),
                                  ])
                              ),
                          }
                        : null,
                };
                const res = await updateRelation(
                    relationType.fromEntityType,
                    relationType.name,
                    relationType.toEntityType,
                    request
                );
                await getSchema(relationType.fromEntityType);
                setIsLoading(false);
                if (res) {
                    getSchemaTree(true);
                    openToggle.close();
                }
            } else {
                openToggle.close();
            }
        },
        [relationType]
    );

    return (
        <>
            <MainTooltip
                title={
                    relationType?.system
                        ? 'You are not allowed to modify system relationship'
                        : isInherited
                        ? `This relationship is inherited from the entity type ${relationType?.fromEntityType}. Hence you cannot modify it`
                        : ''
                }
            >
                <span>
                    <IconButton size="small" onClick={openToggle.open} disabled={relationType?.system || isInherited}>
                        <EditIcon />
                    </IconButton>
                </span>
            </MainTooltip>
            <RightTray
                title={`Update relationship`}
                componentName={COMPONENT_NAME.ADD_RELATIONSHIP}
                open={open}
                onClose={openToggle.close}
                onConfirm={() => formRef.current.submitForm()}
                confirmText="Save Changes"
                disableCloseOnClickOutside
                debounceSearch={40}
            >
                <RelationForm
                    formRef={formRef}
                    selectedTypes={[relationType?.toEntityType]}
                    handleSubmit={handleSubmit}
                    defaultInitialValues={relationType}
                />
            </RightTray>
        </>
    );
};

export default EditRelationAction;
