/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback } from 'react';
import { IDialog, ISchemaDetail } from 'ui-common';
import { Button, MinusIcon } from 'ui-style';
import { useDialog, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { getSchemaInfo } from '../../utils/helper';
import { unassignRoles } from '../../actions';
import { useParams } from 'react-router-dom';
import useLocalStore from '../../store/useLocalStore';

const UnassignRoleAction = ({
    selectedRows,
    schemaDetail,
    successMessage,
    errorMessage,
}: {
    selectedRows: string[];
    schemaDetail: ISchemaDetail;
    successMessage?: string;
    errorMessage?: string;
}) => {
    const { schemaName } = useParams();
    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const [setIsLoading, getAssignedRoles] = useLocalStore((state) => [state.setIsLoading, state.getAssignedRoles]);
    const { getSchema } = useSchemaDetail();

    const handleUnassign = useCallback(async () => {
        setIsLoading(true);
        const success = await unassignRoles(schemaName, selectedRows, successMessage, errorMessage);

        if (success) {
            await getAssignedRoles(schemaName, null, true);
            await getSchema(schemaName);
        }
        setIsLoading(false);
    }, [selectedRows, schemaDetail, successMessage, errorMessage]);

    const onClick = useCallback(() => {
        onOpenDialog(
            'Unassign role',
            `Do you want to unassign selected roles from the entity type <b>${getSchemaInfo(
                schemaDetail,
                'displayName'
            )}</b>`,
            handleUnassign,
            'error'
        );
    }, [selectedRows, schemaDetail, handleUnassign]);

    return (
        <Button
            onClick={onClick}
            variant="contained"
            color="secondary"
            size="xs"
            endIcon={<MinusIcon />}
            disabled={selectedRows.length === 0}
        >
            Unassign Role
        </Button>
    );
};

export default UnassignRoleAction;
