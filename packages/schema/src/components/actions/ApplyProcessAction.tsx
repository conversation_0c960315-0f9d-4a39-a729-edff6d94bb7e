/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
    formatDateTime,
    ISchemaDetail,
    useToggle,
    queryBuilder,
    buildSortParams,
    buildQueryBasedOnFilter,
    SYSTEM_ENTITY_TYPE,
    IAppliedProcess,
    EventType,
} from 'ui-common';
import {
    ArrowLeft,
    Autocomplete,
    Box,
    Button,
    CloseIcon,
    IconButton,
    Loading,
    PlusIcon,
    RichtextCellRenderer,
    RightTray,
    SelectedRows,
    tableIcons,
    tableStyles,
    TextField,
    Typography,
} from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { getSchemaInfo } from '../../utils/helper';
import { AgGridReact } from '@ag-grid-community/react';
import get from 'lodash/get';
import { processUrls, fetch } from '@tripudiotech/admin-api';
import useLocalStore from '../../store/useLocalStore';
import isEmpty from 'lodash/isEmpty';
import { applyProcessToEntityType } from '../../actions';

const ApplyProcessAction = ({
    schemaDetail,
    appliedProcess,
    eventTypes,
}: {
    schemaDetail: ISchemaDetail;
    appliedProcess: IAppliedProcess[];
    eventTypes: Record<string, EventType>;
}) => {
    const [applyProcess, applyProcessToggle] = useToggle(false);
    const gridRef = useRef<AgGridReact>();
    const searchQuery = useRef(null);
    const [selectedRows, setSelectedRows] = useState([]);
    const [setIsLoading, getSchemaAppliedProcess] = useLocalStore((state) => [
        state.setIsLoading,
        state.getSchemaAppliedProcess,
    ]);

    const [formValues, setFormValues] = useState({
        condition: '',
        eventTypes: [],
    });

    const handleSelectionChanged = useCallback(() => setSelectedRows(gridRef.current.api.getSelectedRows()), []);

    const [selected, setSelected] = useState(null);

    const eventTypeOptions = useMemo(
        () =>
            eventTypes
                ? Object.values(eventTypes)
                      .filter((eventType) => eventType.isProcess)
                      .map((eventType) => ({
                          id: eventType.name,
                          label: eventType.description,
                      }))
                : [],
        [eventTypes]
    );

    const gridOptions: any = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            onSelectionChanged: handleSelectionChanged,
            loadingOverlayComponent: Loading,
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    checkboxSelection: true,
                    rowDrag: true,
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    minWidth: 140,
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'updatedAt',
                    headerName: 'Updated At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                },
            ],
            components: {},
            cacheBlockSize: 50,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            rowSelection: 'single',
            maxConcurrentDatasourceRequests: 2,
        }),
        []
    );

    const buildSearchQuery = useCallback(
        (search) => {
            const searchText = search || '';
            return queryBuilder.buildAndOperatorQuery([
                queryBuilder.buildOrOperatorQuery([
                    queryBuilder.buildContainsQuery('name', searchText),
                    queryBuilder.buildContainsQuery('description', searchText),
                ]),
                queryBuilder.buildNotInQuery(
                    'id',
                    appliedProcess.map((process) => process.id)
                ),
            ]);
        },
        [appliedProcess]
    );

    const createServerSideDataSource = useCallback(() => {
        const buildParams = (params) => {
            let queryParams = {
                offset: params.startRow,
                limit: 50,
                ...buildSortParams(params.sortModel),
            };
            const filterModel = params.filterModel;
            if (Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                if (searchQuery.current) {
                    filterConditions.push(buildSearchQuery(searchQuery.current));
                }
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else {
                queryParams['query'] = JSON.stringify(buildSearchQuery(searchQuery.current));
            }
            return queryParams;
        };
        return {
            getRows: (params: any) => {
                fetch({
                    ...processUrls.getProcesses,
                    qs: buildParams(params?.request),
                })
                    .then((response) => {
                        if (response.status !== 200) {
                            params.failCallback();
                            return;
                        }
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);
    const handleSubmit = async () => {
        if (!selected) {
            setSelected(selectedRows[0]);
        } else {
            setIsLoading(true);
            const success = await applyProcessToEntityType(getSchemaInfo(schemaDetail, 'id'), selected, {
                condition: formValues.condition,
                eventTypes: formValues.eventTypes.map((eventType) => eventType.id),
            });
            if (success) {
                await getSchemaAppliedProcess(getSchemaInfo(schemaDetail, 'name'), null, true);
                handleClose();
            }
            setIsLoading(false);
        }
    };
    const addDropZone = useCallback((params) => {
        const container = document.querySelector('#schema-detail');
        const dropZone = {
            getContainer: () => container,
            onDragStop: (params) => {
                handleSubmit();
            },
        };
        params.api.addRowDropZone(dropZone);
    }, []);

    const handleSetDataSource = useCallback((event) => {
        const dataSource = createServerSideDataSource();
        event.api.setServerSideDatasource(dataSource);
        addDropZone(event);
    }, []);

    const handleSearchChanged = (text) => {
        searchQuery.current = text;
        if (gridRef.current) {
            handleSetDataSource(gridRef.current);
        }
    };

    const handleClose = () => {
        setSelectedRows([]);
        setSelected(null);
        searchQuery.current = null;
        applyProcessToggle.close();
    };

    const handleBack = () => {
        setSelected(null);
        setSelectedRows([]);
    };

    const clearSelect = useCallback(() => {
        gridRef.current.api.deselectAll();
    }, [gridRef]);

    const tooltipTitleRenderer = () => {
        return selectedRows && selectedRows.length > 0 ? (
            <ul style={{ maxHeight: '200px', overflowY: 'auto', margin: 0, padding: 0 }}>
                {selectedRows.map((entity: any) => (
                    <li key={entity.id}>
                        <Typography sx={{ fontSize: '12px', fontWeight: 400, m: '8px' }}>{entity.name}</Typography>
                    </li>
                ))}
            </ul>
        ) : (
            ''
        );
    };

    return (
        <>
            <Button
                onClick={applyProcessToggle.open}
                variant="contained"
                color="secondary"
                size="xs"
                endIcon={<PlusIcon />}
            >
                Apply Process
            </Button>
            <RightTray
                title={`Apply Process for ${getSchemaInfo(schemaDetail, 'displayName')}`}
                disabled={
                    selected
                        ? isEmpty(formValues.condition) || isEmpty(formValues.eventTypes)
                        : selectedRows.length === 0
                }
                header={
                    selected ? (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Box
                                sx={{
                                    display: 'flex',
                                    gap: '8px',
                                    color: (theme) => theme.palette.glide.text.normal.tertiary,
                                    alignItems: 'center',
                                }}
                            >
                                <IconButton
                                    className="iconBtn"
                                    onClick={handleBack}
                                    sx={{ color: (theme) => theme.palette.glide.text.normal.tertiary }}
                                >
                                    <ArrowLeft />
                                </IconButton>
                                <Typography
                                    variant="title3"
                                    className="title"
                                    sx={{
                                        color: (theme) => theme.palette.glide.text.normal.tertiary,
                                        textTransform: 'none',
                                    }}
                                >
                                    Select Condition and Event Types you want to trigger the Process
                                </Typography>
                            </Box>
                            <IconButton
                                className="iconBtn"
                                onClick={handleClose}
                                sx={{ color: (theme) => theme.palette.glide.text.normal.tertiary }}
                            >
                                <CloseIcon />
                            </IconButton>
                        </Box>
                    ) : undefined
                }
                enableSearch={!selected}
                componentName={COMPONENT_NAME.APPLY_PROCESS}
                open={applyProcess}
                onClose={handleClose}
                onConfirm={handleSubmit}
                confirmText="Apply"
                disableCloseOnClickOutside
                onSearchChange={handleSearchChanged}
            >
                <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', ...tableStyles }}>
                    {selected ? (
                        <Box sx={{ margin: '16px 24px', display: 'flex', flexDirection: 'column', gap: '8px' }}>
                            <TextField
                                label="Condition"
                                value={formValues.condition}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        condition: e.target.value,
                                    })
                                }
                                required
                                helperText="Condition to trigger the process"
                            />
                            <Autocomplete
                                multiple
                                limitTags={3}
                                value={formValues.eventTypes}
                                options={eventTypeOptions}
                                getOptionLabel={(option: any) => option.label}
                                onChange={(e, newValue) => {
                                    setFormValues({
                                        ...formValues,
                                        eventTypes: newValue,
                                    });
                                }}
                                disableCloseOnSelect={true}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        variant="outlined"
                                        placeholder="Event Types"
                                        label="Event Types"
                                        required
                                        helperText="Event Types to trigger the Process"
                                    />
                                )}
                            />
                        </Box>
                    ) : (
                        <>
                            <SelectedRows
                                selectedRows={selectedRows}
                                clearSelect={clearSelect}
                                tooltipTitleRenderer={tooltipTitleRenderer}
                            />
                            <div style={{ height: '100%', overflow: 'auto' }}>
                                <AgGridReact
                                    className="ag-theme-alpine"
                                    ref={gridRef}
                                    {...gridOptions}
                                    onGridReady={handleSetDataSource}
                                    onRowDragEnter={(e) => e.node.setSelected(true)}
                                    icons={tableIcons}
                                />
                            </div>
                        </>
                    )}
                </Box>
            </RightTray>
        </>
    );
};

export default ApplyProcessAction;
