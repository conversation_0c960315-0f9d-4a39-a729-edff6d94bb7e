/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { ISchemaDetail, RelationTypeEvent, useToggle } from 'ui-common';
import { Box, Button, DocumentIcon, PlusIcon, RightTray, tableStyles, Typography } from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { AgGridReact } from '@ag-grid-community/react';
import get from 'lodash/get';
import { useSchemaTree, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { getSchemaInfo } from '../../utils/helper';
import useLocalStore from '../../store/useLocalStore';
import { createRelation } from '../../actions';
import RelationForm from '../relation/RelationForm';
import { getContextMenuCallback, getIndentClass } from '@tripudiotech/admin-styleguide';
import { buildAndOperatorQuery, buildInQuery } from '@tripudiotech/admin-api';
import { convertFormValuesToQuery } from '../../utils/advancedSearchHelper';

const EntityTypeRenderer = ({ value, data }) => {
    const entityType = get(data, 'name');
    return (
        <div className="item">
            <DocumentIcon />
            <Typography variant="label2-sem">{value}</Typography>
            <Typography variant="label2-reg">({entityType})</Typography>
        </div>
    );
};

const AddRelationshipAction = ({ schemaDetail }: { schemaDetail: ISchemaDetail }) => {
    const [open, openToggle] = useToggle(false);
    const { schemaTreeMap } = useSchemaTree();
    const [isEditing, setIsEditing] = useState(false);
    const [selectedTypes, setSelectedTypes] = useState([]);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const getSchema = useSchemaDetail((state) => state.getSchema);

    const formRef = useRef<any>();
    const gridRef = useRef<AgGridReact>();
    const schemaTree = useMemo(
        () => (schemaTreeMap ? Object.values(schemaTreeMap) : null),
        [schemaTreeMap, schemaDetail]
    );

    const handleSearchChanged = useCallback((text) => {
        if (gridRef.current) {
            gridRef.current.api.setFilterModel({
                displayName: {
                    filterType: 'text',
                    type: 'contains',
                    filter: text,
                },
            });
        }
    }, []);
    const getDataPath = useCallback((data) => {
        return data.path || [];
    }, []);

    const isGroupOpenByDefault = useCallback((params) => {
        return params.level === 0;
    }, []);
    const onRowGroupOpened = useCallback(() => {
        // gridRef.current.columnApi.autoSizeAllColumns();
    }, []);

    const autoGroupColumnDef = useMemo(
        () => ({
            field: 'displayName',
            headerName: 'Name',
            cellClass: getIndentClass,
            minWidth: 308,
            flex: 1,
            filter: 'agTextColumnFilter',
            cellRendererParams: {
                innerRenderer: EntityTypeRenderer,
                suppressCount: true,
                checkbox: true,
            },
            cellStyle: (params) => {
                const { level } = params.node;
                const groupCell = params.value === params.node.key;
                const indent = 24; // change this value to your liking

                return groupCell
                    ? {
                          border: 'none',
                          paddingLeft: '-' + indent * (level + 1) + 'px',
                      }
                    : {
                          border: 'none',
                      };
            },
        }),
        []
    );
    const onFirstDataRendered = useCallback(() => {
        gridRef.current.columnApi.applyColumnState({
            state: [{ colId: 'displayName', sort: 'asc' }],
        });
        onRowGroupOpened();
    }, []);

    const onSelectionChanged = useCallback(() => {
        const selectedNodes = gridRef.current.api.getSelectedNodes();
        let selectedMap = {};
        selectedNodes.forEach((node) => {
            selectedMap[node.key] = true;
        });
        let selectedRootTypes = [];
        selectedNodes.forEach((node) => {
            if (node.level === 0 || !selectedMap[node.parent.key]) {
                selectedRootTypes.push(node.key);
            }
        });
        setSelectedTypes(selectedRootTypes);
    }, []);

    const gridOptions: any = useMemo(
        () => ({
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                filter: true,
                floatingFilter: false,
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'displayName',
                    headerName: 'Display Name',
                    filter: 'agTextColumnFilter',
                    hide: true,
                },
            ],
            components: {},
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            isGroupOpenByDefault,
            onRowGroupOpened,
            onExpandOrCollapseAll: onRowGroupOpened,
            getContextMenuItems: getContextMenuCallback(gridRef),
            getDataPath,
            treeData: true,
            onFirstDataRendered,
            getRowStyle: () => ({
                background: ' #FFFFFF',
                border: 'none',
            }),
            autoGroupColumnDef,
            gridOptions: {
                headerHeight: 0,
                rowHeight: 26,
            },
            enableCellChangeFlash: true,
            onSelectionChanged,
        }),

        [onSelectionChanged]
    );

    const handleClose = useCallback(
        (force = false) => {
            if (isEditing && !force) {
                setIsEditing(false);
                return;
            }
            setIsEditing(false);
            setSelectedTypes([]);
            openToggle.close();
        },
        [isEditing]
    );

    const onConfirm = useCallback(
        async (values) => {
            if (isEditing) {
                formRef.current.submitForm();
            } else {
                setIsEditing(true);
            }
        },
        [isEditing, handleClose, selectedTypes, schemaDetail]
    );

    const handleSubmit = async (values) => {
        setIsLoading(true);
        const fromType = getSchemaInfo(schemaDetail, 'name');
        const requests = selectedTypes.map((type) => ({
            ...values,
            name: values.name,
            displayName: values.displayName,
            fromEntityTypeName: fromType,
            toEntityTypeName: type,
            isRequired: values.isRequired,
            isVisible: values.isVisible,
            isSingleRelation: values.isSingleRelation,
            onFromSideRevise: values[RelationTypeEvent.onFromSideRevise]?.type
                ? values[RelationTypeEvent.onFromSideRevise]
                : null,
            onFromSideClone: values[RelationTypeEvent.onFromSideClone]?.type
                ? values[RelationTypeEvent.onFromSideClone]
                : null,
            onToSideRevise: values[RelationTypeEvent.onToSideRevise]?.type
                ? values[RelationTypeEvent.onToSideRevise]
                : null,
            onToSideClone: values[RelationTypeEvent.onToSideClone]?.type
                ? values[RelationTypeEvent.onToSideClone]
                : null,
            constraint: values.withSearchCriteria
                ? {
                      searchCriteria: JSON.stringify(
                          buildAndOperatorQuery([
                              buildInQuery('schemaType', [selectedTypes?.[0]]),
                              convertFormValuesToQuery(values.condition),
                          ])
                      ),
                  }
                : null,
        }));
        const res = await createRelation(requests);

        if (res) {
            handleClose(true);
        }
        await getSchema(fromType);
        setIsLoading(false);
    };

    return (
        <>
            <Button onClick={openToggle.open} variant="contained" color="secondary" size="xs" endIcon={<PlusIcon />}>
                Add Relationship
            </Button>
            <RightTray
                title={isEditing ? `Add Relationship` : `Select entity types to add relationship`}
                componentName={COMPONENT_NAME.ADD_RELATIONSHIP}
                open={open}
                onClose={handleClose}
                onConfirm={onConfirm}
                confirmText="Add"
                disableCloseOnClickOutside
                enableSearch={!isEditing}
                onSearchChange={handleSearchChanged}
                debounceSearch={40}
                disabled={selectedTypes.length === 0}
            >
                {isEditing ? (
                    <RelationForm formRef={formRef} selectedTypes={selectedTypes} handleSubmit={handleSubmit} />
                ) : (
                    <Box
                        sx={{
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            ...tableStyles,
                            '& .ag-theme-alpine .ag-row': {
                                border: 'none',
                            },
                            '& .ag-theme-alpine .ag-header': {
                                border: 'none !important',
                            },
                            margin: '16px',
                            mt: 0,
                        }}
                    >
                        <div style={{ height: '100%', overflow: 'auto' }} className="ag-theme-alpine">
                            <AgGridReact ref={gridRef} {...gridOptions} rowData={schemaTree} />
                        </div>
                    </Box>
                )}
            </RightTray>
        </>
    );
};

export default AddRelationshipAction;
