/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback } from 'react';
import { IDialog, ISchemaDetail } from 'ui-common';
import { Button, MinusIcon } from 'ui-style';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { getSchemaInfo } from '../../utils/helper';
import { revokeProcess } from '../../actions';
import useLocalStore from '../../store/useLocalStore';

const RevokeProcessAction = ({
    schemaDetail,
    selectedProcess,
}: {
    schemaDetail: ISchemaDetail;
    selectedProcess: any[];
}) => {
    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const [setIsLoading, getSchemaAppliedProcess] = useLocalStore((state) => [
        state.setIsLoading,
        state.getSchemaAppliedProcess,
    ]);

    const handleRevoke = useCallback(async () => {
        setIsLoading(true);
        await revokeProcess(getSchemaInfo(schemaDetail, 'id'), selectedProcess);
        await getSchemaAppliedProcess(getSchemaInfo(schemaDetail, 'name'), null, true);
        setIsLoading(false);
    }, [schemaDetail, selectedProcess]);

    const onRevokeProcess = useCallback(() => {
        onOpenDialog(
            'Revoke applied process',
            `Do you want to revoke selected process from the entity type <b>${getSchemaInfo(
                schemaDetail,
                'displayName'
            )}</b>`,
            handleRevoke,
            'error'
        );
    }, [schemaDetail, handleRevoke]);
    return (
        <Button
            disabled={selectedProcess.length === 0}
            onClick={onRevokeProcess}
            variant="contained"
            color="secondary"
            size="xs"
            endIcon={<MinusIcon />}
        >
            Revoke Process
        </Button>
    );
};

export default RevokeProcessAction;
