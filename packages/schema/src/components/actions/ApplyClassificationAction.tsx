/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef } from 'react';
import { Box, Button, PlusIcon, RightTray, tableStyles, Typography } from 'ui-style';
import { useToggle } from 'ui-common';
import { COMPONENT_NAME } from '../../constants/component-name';
import { AgGridReact } from '@ag-grid-community/react';
import { applyClassification } from '../../actions';
import { useParams } from 'react-router-dom';
import useLocalStore from '../../store/useLocalStore';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';

const ItemRenderer = ({ value }) => {
    return <Typography variant="label2-sem">{value}</Typography>;
};

const ApplyClassification = ({
    appliedClassifications,
    classificationTreeMap,
}: {
    appliedClassifications: any[];
    classificationTreeMap: Record<string, any>;
}) => {
    const [open, openToggle] = useToggle(false);
    const gridRef = useRef<AgGridReact>();
    const { schemaName } = useParams();
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const getSchema = useSchemaDetail((state) => state.getSchema);

    const treeData = useMemo(() => {
        return classificationTreeMap ? Object.values(classificationTreeMap) : null;
    }, [classificationTreeMap]);

    const onFirstDataRendered = useCallback(() => {
        gridRef.current.api.forEachNode((node) => {
            if (appliedClassifications.includes(node.data.name)) {
                node.setSelected(true);
            }
        });
    }, [appliedClassifications]);

    const getContextMenuItems = useCallback(() => {
        return [
            {
                name: 'Sort A → Z',
                action: () => {
                    if (gridRef.current) {
                        gridRef.current.columnApi.applyColumnState({
                            state: [{ colId: 'label', sort: 'asc' }],
                        });
                    }
                },
            },
            {
                name: 'Sort Z → A',
                action: () => {
                    if (gridRef.current) {
                        gridRef.current.columnApi.applyColumnState({
                            state: [{ colId: 'label', sort: 'desc' }],
                        });
                    }
                },
            },
            'separator',
            {
                name: 'Expand selected rows',
                action: () => {
                    if (gridRef.current) {
                        gridRef.current.api.getSelectedNodes().forEach((node) => node.setExpanded(true));
                    }
                },
            },
            'expandAll',
            'contractAll',
            'separator',
            'copy',
            'export',
        ];
    }, []);

    const gridOptions: any = useMemo(
        () => ({
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    hide: true,
                    filter: 'agTextColumnFilter',
                },
            ],
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                editable: false,
                cellStyle: () => ({
                    display: 'inline-block',
                    border: 'none',
                }),
                getRowStyle: () => ({
                    border: 'none',
                }),
            },
            autoGroupColumnDef: {
                field: 'name',
                headerName: 'Name',
                cellClass: 'ag-cell-wrapper',
                cellRendererParams: {
                    suppressCount: true,
                    checkbox: true,
                    innerRenderer: ItemRenderer,
                },
                filter: 'agTextColumnFilter',
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            rowHeight: 32,
            treeData: true,
            animateRows: true,
            getDataPath: (data) => {
                return data.path;
            },
            getRowId: (params) => {
                return params.data.name;
            },
            onFirstDataRendered,
            groupSelectsChildren: true,
            headerHeight: 0,
            getContextMenuItems,
        }),
        [onFirstDataRendered]
    );

    const handleSubmit = useCallback(async () => {
        setIsLoading(true);
        const selectedNodes = gridRef.current.api.getSelectedNodes();
        let selectedMap = {};
        selectedNodes.forEach((node) => {
            selectedMap[node.key] = true;
        });
        let classifications = [];
        selectedNodes.forEach((node) => {
            if (node.level === 0 || !selectedMap[node.parent.key]) {
                classifications.push(node.key);
            }
        });
        const result = await applyClassification(schemaName, classifications);
        await getSchema(schemaName);
        setIsLoading(false);
        if (result) {
            openToggle.close();
        }
    }, []);

    const handleSearchChanged = useCallback((text) => {
        if (gridRef.current) {
            gridRef.current.api.setFilterModel({
                name: {
                    filterType: 'text',
                    type: 'contains',
                    filter: text,
                },
            });
        }
    }, []);

    return (
        <>
            <Button onClick={openToggle.open} variant="contained" color="secondary" size="xs" endIcon={<PlusIcon />}>
                Apply Classification
            </Button>
            <RightTray
                title={`Apply Classification`}
                componentName={COMPONENT_NAME.APPLY_CLASSIFICATION}
                open={open}
                onClose={openToggle.close}
                onConfirm={handleSubmit}
                confirmText="Apply"
                disableCloseOnClickOutside
                enableSearch
                onSearchChange={handleSearchChanged}
                debounceSearch={40}
            >
                <Box
                    sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        ...tableStyles,
                        '& .ag-theme-alpine .ag-row': {
                            border: 'none',
                        },
                    }}
                >
                    <div style={{ height: '100%', overflow: 'auto' }}>
                        <AgGridReact
                            ref={gridRef}
                            className="ag-theme-alpine"
                            {...gridOptions}
                            onRowDragEnter={(e) => e.node.setSelected(true)}
                            rowData={treeData}
                        />
                    </div>
                </Box>
            </RightTray>
        </>
    );
};

export default ApplyClassification;
