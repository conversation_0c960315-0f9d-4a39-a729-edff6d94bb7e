/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef } from 'react';
import { ISchemaDetail, UpdateSchemaRequest, useToggle } from 'ui-common';
import {
    Box,
    EditIcon,
    FormikCheckBox,
    FormikTextField,
    IconButton,
    RichTextField,
    <PERSON>Tray,
    Typography,
} from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import * as yup from 'yup';
import { Formik, Form, Field } from 'formik';
import { getSchemaInfo } from '../../utils/helper';
import { updateEntityType } from '../../actions';
import useLocalStore from '../../store/useLocalStore';
import { useSchemaDetail, useSchemaTree } from '@tripudiotech/admin-caching-store';

export type EditMetadataActionProps = {
    schemaDetail: ISchemaDetail;
};

const validationSchema = yup.object().shape({
    displayName: yup.string().required('Display name is required'),
    description: yup.string().required('Description is required'),
    isExtendable: yup.boolean(),
    isAbstract: yup.boolean(),
    isVisible: yup.boolean(),
});

const EditMetadataAction = ({ schemaDetail }: EditMetadataActionProps) => {
    const [open, openToggle] = useToggle(false);
    const getSchema = useSchemaDetail((state) => state.getSchema);
    const { getSchemaTree } = useSchemaTree();
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const formRef = useRef(null);
    const initialValues = useMemo(
        () => ({
            name: getSchemaInfo(schemaDetail, 'name'),
            displayName: getSchemaInfo(schemaDetail, 'displayName'),
            description: getSchemaInfo(schemaDetail, 'description'),
            isExtendable: getSchemaInfo(schemaDetail, 'extendable'),
            isAbstract: getSchemaInfo(schemaDetail, 'abstract'),
            isVisible: getSchemaInfo(schemaDetail, 'visible'),
        }),
        [schemaDetail]
    );
    const handleSubmit = useCallback(async (values: UpdateSchemaRequest) => {
        if (formRef.current.dirty) {
            setIsLoading(true);
            const res = await updateEntityType(values);
            await getSchema(values.name);
            setIsLoading(false);
            if (res) {
                getSchemaTree(true);
                openToggle.close();
            }
        } else {
            openToggle.close();
        }
    }, []);

    return (
        <>
            <IconButton size="small" onClick={openToggle.open}>
                <EditIcon />
            </IconButton>
            <RightTray
                title={`Edit ${getSchemaInfo(schemaDetail, 'displayName')} Information`}
                componentName={COMPONENT_NAME.EDIT_METADATA}
                open={open}
                onClose={openToggle.close}
                onConfirm={() => formRef.current.submitForm()}
                confirmText="Save changes"
                disableCloseOnClickOutside
            >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', margin: '16px' }}>
                    <Typography
                        variant="label2-med"
                        sx={{
                            color: (theme) => theme.palette.info.main,
                        }}
                    >
                        General Information
                    </Typography>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        innerRef={formRef}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleSubmit(values as any);
                            setSubmitting(false);
                        }}
                    >
                        {({ values, setFieldValue, ...rest }) => {
                            return (
                                <Form
                                    id="metadata-form"
                                    style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
                                >
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Display Name"
                                        name="displayName"
                                        variant="outlined"
                                    />
                                    <Field
                                        fullWidth
                                        multiline
                                        component={FormikTextField}
                                        label="Description"
                                        minRows={2}
                                        name="description"
                                        variant="outlined"
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikCheckBox}
                                        type="checkbox"
                                        name="isExtendable"
                                        Label={{ label: 'Extendable' }}
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikCheckBox}
                                        type="checkbox"
                                        name="isAbstract"
                                        Label={{ label: 'Abstract' }}
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikCheckBox}
                                        type="checkbox"
                                        name="isVisible"
                                        Label={{ label: 'Visible in UI' }}
                                    />
                                </Form>
                            );
                        }}
                    </Formik>
                </Box>
            </RightTray>
        </>
    );
};

export default EditMetadataAction;
