/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef } from 'react';
import { useToggle, ISchemaDetail, AttributeSchema } from 'ui-common';
import {
    AttributeName<PERSON><PERSON><PERSON>,
    <PERSON>,
    Button,
    KeyIcon,
    RichtextCellRenderer,
    RightTray,
    tableIcons,
    tableStyles,
} from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { getSchemaInfo } from '../../utils/helper';
import { AgGridReact } from '@ag-grid-community/react';
import useLocalStore from '../../store/useLocalStore';
import { updateEntityType } from '../../actions';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';

const ChangeUniqueKeyAction = ({
    disabled,
    schemaDetail,
    attributes,
}: {
    disabled: boolean;
    schemaDetail: ISchemaDetail;
    attributes: AttributeSchema[];
}) => {
    const [open, openToggle] = useToggle();
    const gridRef = useRef<AgGridReact>();
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const getSchema = useSchemaDetail((state) => state.getSchema);

    const onFirstDataRendered = useCallback(() => {
        const currentUniqueKeys = attributes
            .filter((attribute) => attribute.uniqueKey)
            .map((attribute) => attribute.name);
        gridRef.current.api.forEachNode((node) => {
            if (currentUniqueKeys.includes(node.data.name)) {
                node.setSelected(true);
            }
        });
    }, [attributes]);

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            headerHeight: 32,
            rowHeight: 32,
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: AttributeNameRenderer,
                    checkboxSelection: true,
                },
                {
                    field: 'displayName',
                    headerName: 'Display Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'type',
                    headerName: 'Type',
                    filter: 'agSetColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
            ],
            icons: tableIcons,
            suppressRowClickSelection: true,
            onFirstDataRendered,
        };
    }, [attributes, onFirstDataRendered]);

    const handleSubmit = useCallback(async () => {
        setIsLoading(true);
        const selectedRows = gridRef.current.api.getSelectedRows().map((row) => row.name);
        const request = {
            name: getSchemaInfo(schemaDetail, 'name'),
            description: getSchemaInfo(schemaDetail, 'description'),
            displayName: getSchemaInfo(schemaDetail, 'displayName'),
            isExtendable: getSchemaInfo(schemaDetail, 'extendable'),
            isRevisable: getSchemaInfo(schemaDetail, 'revisable'),
            isAbstract: getSchemaInfo(schemaDetail, 'abstract'),
            attributeOrder: getSchemaInfo(schemaDetail, 'attributeOrder'),
            uniqueKeys: selectedRows,
        };
        const res = await updateEntityType(request);
        await getSchema(schemaDetail.entityType.name);
        setIsLoading(false);
        if (res) {
            openToggle.close();
        }
    }, [schemaDetail, attributes]);
    return (
        <>
            <Button
                variant="contained"
                color="secondary"
                size="xs"
                endIcon={<KeyIcon />}
                onClick={openToggle.open}
                disabled={disabled}
            >
                Change Unique Keys
            </Button>
            <RightTray
                title={`Change Unique Keys for ${getSchemaInfo(schemaDetail, 'displayName')}`}
                componentName={COMPONENT_NAME.CHANGE_UNIQUE_KEY}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Save changes"
                onConfirm={handleSubmit}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                        ...tableStyles,
                    }}
                >
                    <div style={{ height: '100%', overflow: 'auto' }}>
                        <AgGridReact
                            rowData={attributes}
                            className="ag-theme-alpine"
                            ref={gridRef}
                            {...gridOptions}
                            icons={tableIcons}
                        />
                    </div>
                </Box>
            </RightTray>
        </>
    );
};

export default ChangeUniqueKeyAction;
