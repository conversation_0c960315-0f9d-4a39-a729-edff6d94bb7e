/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
    ISchemaDetail,
    useToggle,
    queryBuilder,
    buildSortParams,
    buildQueryBasedOnFilter,
    AttributeType,
} from 'ui-common';
import {
    ArrowLeft,
    BooleanRenderer,
    Box,
    Button,
    IconButton,
    Loading,
    PlusCircleIcon,
    RichtextCellRenderer,
    RightTray,
    tableIcons,
    tableStyles,
    TextField,
    InputAdornment,
    SearchIcon,
} from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { getSchemaInfo } from '../../utils/helper';
import { AgGridReact } from '@ag-grid-community/react';
import { schemaUrls, fetch } from '@tripudiotech/admin-api';
import useLocalStore from '../../store/useLocalStore';
import { createEntityAttribute } from '../../actions';
import filter from 'lodash//filter';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';
import debounce from 'lodash/debounce';

const AddExistingAttributeAction = ({
    schemaDetail,
    disabled = false,
}: {
    schemaDetail: ISchemaDetail;
    disabled?: boolean;
}) => {
    const [open, openToggle] = useToggle(false);
    const leftGridRef = useRef<AgGridReact>();
    const rightGridRef = useRef<AgGridReact>();
    const [leftSelected, setLeftSelected] = useState([]);
    const [rightSelected, setRightSelected] = useState([]);
    const selectedRef = useRef([]);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const getSchema: (entityName: string, signal?: any) => Promise<ISchemaDetail> = useSchemaDetail(
        (state) => state.getSchema
    );
    const searchQuery = useRef(null);

    // Selected Attributes
    const [selectedAttributes, setSelectedAttributes] = useState([]);

    const handleSubmit = useCallback(async () => {
        const request = selectedAttributes.map((attribute) => {
            const newAttribute = { ...attribute };
            delete newAttribute['nullable'];
            delete newAttribute['system'];
            delete newAttribute['createdAt'];
            delete newAttribute['updatedAt'];
            delete newAttribute['disabled'];
            return newAttribute;
        });
        setIsLoading(true);
        const res = await createEntityAttribute(schemaDetail, request);
        await getSchema(getSchemaInfo(schemaDetail, 'name'));
        setIsLoading(false);
        if (res) {
            handleClose();
        }
    }, [schemaDetail, selectedAttributes]);

    const gridOptions: any = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            onSelectionChanged: () => setLeftSelected(leftGridRef.current.api.getSelectedRows()),
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.name;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    checkboxSelection: true,
                    rowDrag: true,
                    minWidth: 90,
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'displayName',
                    minWidth: 90,
                    headerName: 'Display name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'type',
                    headerName: 'Type',
                    minWidth: 90,
                    filter: 'agSetColumnFilter',
                    filterParams: {
                        values: Object.values(AttributeType),
                    },
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    minWidth: 90,
                    maxWidth: 200,
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
            ],
            cacheBlockSize: 25,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            rowSelection: 'multiple',
            suppressRowClickSelection: true,
        }),
        [leftGridRef]
    );

    const handleNullableChange = useCallback((e, rowData) => {
        const updatedName = rowData.name;
        setSelectedAttributes((attributes) => {
            return [...attributes].map((newAtt) => ({
                ...newAtt,
                isNullable: newAtt.name === updatedName ? e.target.checked : newAtt.isNullable || false,
            }));
        });
    }, []);

    const selectedGridOptions: any = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            onSelectionChanged: () => setRightSelected(rightGridRef.current.api.getSelectedRows()),
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.name;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    checkboxSelection: true,
                    rowDrag: true,
                    headerCheckboxSelection: true,
                    minWidth: 90,
                },
                {
                    field: 'displayName',
                    headerName: 'Display name',
                    minWidth: 90,
                },
                {
                    field: 'type',
                    headerName: 'Type',
                    minWidth: 90,
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    minWidth: 90,
                    maxWidth: 200,
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'isNullable',
                    headerName: 'Can be empty',
                    minWidth: 90,
                    filter: 'agTextColumnFilter',
                    cellRenderer: BooleanRenderer,
                    cellRendererParams: {
                        onChange: handleNullableChange,
                    },
                },
            ],
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            suppressRowClickSelection: true,
        }),
        []
    );

    const buildSearchQuery = useCallback(
        (search) => {
            const searchText = search || '';
            return queryBuilder.buildAndOperatorQuery([
                queryBuilder.buildOrOperatorQuery([
                    queryBuilder.buildContainsQuery('name', searchText),
                    queryBuilder.buildContainsQuery('description', searchText),
                ]),
                queryBuilder.buildNotInQuery(
                    'id',
                    Object.values(schemaDetail.attributes)
                        .map((attr) => attr.id)
                        .concat(selectedRef.current || [])
                ),
            ]);
        },
        [schemaDetail]
    );

    const createServerSideDataSource = useCallback(() => {
        const buildParams = (params) => {
            let queryParams = {
                offset: params.startRow,
                limit: 25,
                ...buildSortParams(params.sortModel),
            };
            const filterModel = params.filterModel;
            if (Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                if (searchQuery.current) {
                    filterConditions.push(buildSearchQuery(searchQuery.current));
                }
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else if (searchQuery.current) {
                queryParams['query'] = JSON.stringify(buildSearchQuery(searchQuery.current));
            }
            return queryParams;
        };
        return {
            getRows: (params: any) => {
                leftGridRef.current.api.showLoadingOverlay();
                fetch({
                    ...schemaUrls.getListAttribute,
                    qs: buildParams(params?.request),
                })
                    .then((response) => {
                        if (response.status !== 200) {
                            params.failCallback();
                            return;
                        }
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                    })
                    .finally(() => {
                        leftGridRef.current.api.hideOverlay();
                        if (leftGridRef.current.api.getDisplayedRowCount() === 0) {
                            leftGridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);

    const handleSearch = useCallback(
        debounce((e) => {
            searchQuery.current = e.target.value;
            if (leftGridRef.current) {
                leftGridRef.current.api.refreshServerSide();
            }
        }, 400),
        []
    );

    const handleSelectAttributes = useCallback(() => {
        const newRows = leftGridRef.current.api.getSelectedRows();
        selectedRef.current = [...selectedRef.current, ...newRows.map((row) => row.id)];
        setSelectedAttributes((oldAttributes) => [...oldAttributes, ...newRows]);
        leftGridRef.current.api.deselectAll();
        leftGridRef.current.api.refreshServerSide();
    }, []);

    const addLeftDropZone = useCallback((params) => {
        const container = document.querySelector('#selected-attributes');
        const dropZone = {
            getContainer: () => container,
            onDragStop: () => {
                handleSelectAttributes();
            },
        };
        params.api.addRowDropZone(dropZone);
    }, []);

    const handleRemoveSelectedAttributes = useCallback(() => {
        const selectedRightRows = rightGridRef.current.api.getSelectedRows();
        setSelectedAttributes((oldAttributes) => {
            const newList = filter(
                oldAttributes,
                (attribute) => !selectedRightRows.some((row) => row.name === attribute.name)
            );
            selectedRef.current = [...newList.map((row) => row.id)];
            return newList;
        });
        rightGridRef.current.api.deselectAll();
        leftGridRef.current.api.refreshServerSide();
    }, []);

    const addRightDropZone = useCallback(() => {
        const container = document.querySelector('#source-attributes');
        const dropZone: any = {
            getContainer: () => container,
            onDragStop: () => {
                handleRemoveSelectedAttributes();
            },
        };
        rightGridRef.current.api.addRowDropZone(dropZone);
    }, [handleRemoveSelectedAttributes]);

    const handleSetDataSource = useCallback((event) => {
        const dataSource = createServerSideDataSource();
        event.api.setServerSideDatasource(dataSource);
        addLeftDropZone(event);
    }, []);

    const handleClose = () => {
        openToggle.close();
        selectedRef.current = [];
        setSelectedAttributes([]);
    };

    return (
        <>
            <Button
                variant="contained"
                color="secondary"
                size="xs"
                endIcon={<PlusCircleIcon />}
                disabled={disabled}
                onClick={openToggle.open}
            >
                Add existing
            </Button>
            <RightTray
                title={`Add existing attribute for ${getSchemaInfo(schemaDetail, 'displayName')}`}
                componentName={COMPONENT_NAME.ADD_EXISTING_ATTRIBUTE}
                disabled={selectedAttributes.length === 0}
                open={open}
                onClose={handleClose}
                onConfirm={handleSubmit}
                confirmText="Add"
                disableCloseOnClickOutside
                fullWidth
            >
                <Box sx={{ height: '100%', display: 'flex', gap: '16px', ...tableStyles }}>
                    <Box
                        sx={{ height: '100%', flex: '10', display: 'flex', flexDirection: 'column' }}
                        id="source-attributes"
                    >
                        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <TextField
                                sx={{
                                    m: '16px',
                                    width: 'auto',
                                    '& input': {
                                        paddingLeft: '4px !important',
                                        fontSize: '14px',
                                        height: '24px',
                                    },
                                }}
                                onChange={handleSearch}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon />
                                        </InputAdornment>
                                    ),
                                }}
                                placeholder="Type  to search"
                                fullWidth
                                variant="outlined"
                                size="small"
                            />
                            <AgGridReact
                                className="ag-theme-alpine"
                                ref={leftGridRef}
                                {...gridOptions}
                                onGridReady={handleSetDataSource}
                                onRowDragEnter={(e) => e.node.setSelected(true)}
                                icons={tableIcons}
                            />
                        </div>
                    </Box>
                    <Box
                        sx={{
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            alignSelf: 'center',
                            flex: '1',
                            gap: '40px',
                        }}
                    >
                        <IconButton size="small" onClick={handleSelectAttributes} disabled={leftSelected.length === 0}>
                            <ArrowLeft sx={{ transform: 'rotate(180deg)' }} />
                        </IconButton>
                        <IconButton
                            size="small"
                            onClick={handleRemoveSelectedAttributes}
                            disabled={rightSelected.length === 0}
                        >
                            <ArrowLeft />
                        </IconButton>
                    </Box>
                    <Box
                        sx={{ height: '100%', flex: '10', display: 'flex', flexDirection: 'column' }}
                        id="selected-attributes"
                    >
                        <div style={{ height: '100%' }}>
                            <Box sx={{ width: '100%', height: '66px' }}></Box>
                            <AgGridReact
                                className="ag-theme-alpine"
                                {...selectedGridOptions}
                                onRowDragEnter={(e) => e.node.setSelected(true)}
                                rowData={selectedAttributes}
                                icons={tableIcons}
                                ref={rightGridRef}
                                onFirstDataRendered={addRightDropZone}
                            />
                        </div>
                    </Box>
                </Box>
            </RightTray>
        </>
    );
};

export default AddExistingAttributeAction;
