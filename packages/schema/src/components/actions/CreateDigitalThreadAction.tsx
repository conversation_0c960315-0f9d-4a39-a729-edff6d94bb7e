/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useState } from 'react';
import { useEdgesState, useNodesState, useReactFlow } from 'reactflow';
import { Box, Button, LoadingOverlay, RightTray, SchemaFieldSection, Stack, TextField } from 'ui-style';
import DigitalThreadDiagram from '../flow/DigitalThreadDiagram';
import useDigitalThreadStore from '../../store/useDigitalThreadStore';
import { useNavigate } from 'react-router-dom';

const CreateDigitalThreadAction = () => {
    const [open, setOpen, createDigitalThread, isLoading] = useDigitalThreadStore((state) => [
        state.isOpenCreate,
        state.setIsOpenCreate,
        state.createDigitalThread,
        state.isLoading,
    ]);
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);
    const navigate = useNavigate();
    const { getEdges } = useReactFlow();
    const onClose = useCallback(() => {
        setOpen(false);
        setNodes([]);
        setEdges([]);
    }, [setOpen, setNodes, setEdges]);

    const handleCreate = useCallback(async () => {
        const result = await createDigitalThread({ name, description }, getEdges());
        if (result) {
            setOpen(false);
            navigate(`/digital-thread/${result.digitalThread.id}`);
        }
    }, [createDigitalThread, name, description]);

    const onCreate = useCallback(() => {
        setOpen(true);
    }, [setOpen]);

    return (
        <>
            {isLoading && open && <LoadingOverlay />}
            <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
                <Button variant="contained" color="secondary" size="small" onClick={onCreate}>
                    Create new
                </Button>
            </Box>
            <RightTray
                title="Create new Digital Thread"
                componentName="create-digital-thread"
                disabled={nodes.length === 0 || !name || !description}
                open={open}
                onClose={onClose}
                fullWidth
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={handleCreate}
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                        position: 'relative',
                        backgroundColor: (theme) => theme.palette.glide.background.normal.inversePrimary,
                    }}
                    id="create-form"
                >
                    <DigitalThreadDiagram
                        id="create-form"
                        nodes={nodes}
                        edges={edges}
                        onNodesChange={onNodesChange}
                        onEdgesChange={onEdgesChange}
                    />
                    <Box
                        sx={{
                            position: 'fixed',
                            top: '96px',
                            right: '16px',
                            borderRadius: '8px',
                            background: (theme) => theme.palette.glide.background.normal.white,
                            padding: '16px',
                            boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px',
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '8px',
                            width: '280px',
                        }}
                    >
                        <SchemaFieldSection>Digital Thread</SchemaFieldSection>
                        <TextField
                            label="Name"
                            fullWidth
                            size="small"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                        />
                        <TextField
                            label="Description"
                            multiline
                            size="small"
                            value={description}
                            minRows={3}
                            onChange={(e: any) => setDescription(e.target.value)}
                        />
                    </Box>
                </Stack>
            </RightTray>
        </>
    );
};

export default CreateDigitalThreadAction;
