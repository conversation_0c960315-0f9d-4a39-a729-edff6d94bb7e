/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback } from 'react';
import { IDialog, ILifeCycleDetail, ISchemaDetail } from 'ui-common';
import { Button, MinusIcon } from 'ui-style';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { getSchemaInfo } from '../../utils/helper';
import { unassignLifeCycles } from '../../actions';
import { useParams } from 'react-router-dom';
import useLocalStore from '../../store/useLocalStore';

const UnAssignLifecycleAction = ({
    selectedRows,
    schemaDetail,
}: {
    selectedRows: ILifeCycleDetail[];
    schemaDetail: ISchemaDetail;
}) => {
    const { schemaName } = useParams();
    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const [setIsLoading, getSchemaLifecycles] = useLocalStore((state) => [
        state.setIsLoading,
        state.getSchemaLifecycles,
    ]);

    const handleUnassign = useCallback(async () => {
        setIsLoading(true);
        const success = await unassignLifeCycles(schemaName, selectedRows);

        if (success) {
            await getSchemaLifecycles(schemaName, null, true);
        }
        setIsLoading(false);
    }, [selectedRows, schemaDetail]);

    const onClick = useCallback(() => {
        onOpenDialog(
            'Unassign lifecycle',
            `Do you want to unassign selected lifecycles from the entity type <b>${getSchemaInfo(
                schemaDetail,
                'displayName'
            )}</b>`,
            handleUnassign,
            'error'
        );
    }, [selectedRows, schemaDetail, handleUnassign]);

    return (
        <Button
            onClick={onClick}
            variant="contained"
            color="secondary"
            size="xs"
            endIcon={<MinusIcon />}
            disabled={selectedRows.length === 0}
        >
            Unassign Lifecycle
        </Button>
    );
};

export default UnAssignLifecycleAction;
