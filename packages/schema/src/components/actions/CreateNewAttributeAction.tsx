/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useRef } from 'react';
import { AttributeForm, Box, Button, PlusIcon, RightTray } from 'ui-style';
import { useToggle, ISchemaDetail } from 'ui-common';
import { getSchemaInfo } from '../../utils/helper';
import { COMPONENT_NAME } from '../../constants/component-name';
import { useSchemaDetail, useSchemaTree, useUnitOfMeasure } from '@tripudiotech/admin-caching-store';
import useLocalStore from '../../store/useLocalStore';
import { createEntityTypeAttribute } from '../../actions';

const CreateNewAttributeAction = ({ disabled, schemaDetail }: { disabled: boolean; schemaDetail: ISchemaDetail }) => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);
    const { schemaTreeMap } = useSchemaTree();
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const getSchema = useSchemaDetail((state) => state.getSchema);

    /**
     * Unit of measure cache
     */
    const [quantityKind, quantityUnit, qdtUnitMapper, qdtKindMapper, getQuantityUnit] = useUnitOfMeasure((state) => [
        state.quantityKind,
        state.quantityUnit,
        state.qdtUnitMapper,
        state.qdtKindMapper,
        state.getQuantityUnit,
    ]);

    const handleSubmit = useCallback(
        async (values) => {
            if (formRef.current.dirty) {
                setIsLoading(true);
                const res = await createEntityTypeAttribute(schemaDetail.entityType.name, values);
                await getSchema(schemaDetail.entityType.name);
                setIsLoading(false);
                if (res) {
                    openToggle.close();
                }
            } else {
                openToggle.close();
            }
        },
        [schemaDetail]
    );

    return (
        <>
            <Button
                variant="contained"
                color="secondary"
                onClick={openToggle.open}
                size="xs"
                endIcon={<PlusIcon />}
                disabled={disabled}
            >
                Create New
            </Button>
            <RightTray
                title={`Create new Attribute for ${getSchemaInfo(schemaDetail, 'displayName')}`}
                componentName={COMPONENT_NAME.CREATE_NEW_ATTRIBUTE}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={() => formRef.current.submitForm()}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <AttributeForm
                        formRef={formRef}
                        schemaDetail={schemaDetail}
                        onSubmit={handleSubmit}
                        quantityKind={quantityKind}
                        quantityUnit={quantityUnit}
                        qdtKindMapper={qdtKindMapper}
                        qdtUnitMapper={qdtUnitMapper}
                        schemaTreeMap={schemaTreeMap}
                        getQuantityUnit={getQuantityUnit}
                    />
                </Box>
            </RightTray>
        </>
    );
};

export default CreateNewAttributeAction;
