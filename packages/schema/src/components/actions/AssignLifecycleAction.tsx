/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
    formatDateTime,
    ISchemaDetail,
    useToggle,
    queryBuilder,
    buildSortParams,
    buildQueryBasedOnFilter,
} from 'ui-common';
import {
    Box,
    Button,
    Loading,
    PlusIcon,
    RichtextCellRenderer,
    RightTray,
    SelectedRows,
    tableIcons,
    tableStyles,
    Typography,
} from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { getSchemaInfo } from '../../utils/helper';
import { AgGridReact } from '@ag-grid-community/react';
import get from 'lodash/get';
import { schemaUrls, fetch } from '@tripudiotech/admin-api';
import useLocalStore from '../../store/useLocalStore';
import { assignLifeCycles } from '../../actions';
import { useParams } from 'react-router-dom';

const AssignLifecycleAction = ({ schemaDetail }: { schemaDetail: ISchemaDetail }) => {
    const [assignLifecycle, assignLifecycleToggle] = useToggle(false);
    const gridRef = useRef<AgGridReact>();
    const searchQuery = useRef(null);
    const [selectedRows, setSelectedRows] = useState([]);
    const [setIsLoading, getSchemaLifecycles] = useLocalStore((state) => [
        state.setIsLoading,
        state.getSchemaLifecycles,
    ]);
    const { schemaName } = useParams();
    const handleSelectionChanged = useCallback(() => setSelectedRows(gridRef.current.api.getSelectedRows()), []);

    const gridOptions: any = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            onSelectionChanged: handleSelectionChanged,
            loadingOverlayComponent: Loading,
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    checkboxSelection: true,
                    rowDrag: true,
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    minWidth: 140,
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'updatedAt',
                    headerName: 'Updated At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                },
            ],
            components: {},
            cacheBlockSize: 50,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            rowSelection: 'multiple',
            maxConcurrentDatasourceRequests: 2,
            suppressRowClickSelection: true,
        }),
        [gridRef]
    );

    const buildSearchQuery = useCallback(
        (search) => {
            const searchText = search || '';
            return queryBuilder.buildAndOperatorQuery([
                queryBuilder.buildOrOperatorQuery([
                    queryBuilder.buildContainsQuery('name', searchText),
                    queryBuilder.buildContainsQuery('description', searchText),
                ]),
                queryBuilder.buildNotInQuery(
                    'id',
                    schemaDetail.lifeCycles.map((lifeCycle) => lifeCycle.id)
                ),
            ]);
        },
        [schemaDetail]
    );

    const createServerSideDataSource = useCallback(() => {
        const buildParams = (params) => {
            let queryParams = {
                offset: params.startRow,
                limit: 50,
                ...buildSortParams(params.sortModel),
            };
            const filterModel = params.filterModel;
            if (Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                if (searchQuery.current) {
                    filterConditions.push(buildSearchQuery(searchQuery.current));
                }
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else {
                queryParams['query'] = JSON.stringify(buildSearchQuery(searchQuery.current));
            }
            return queryParams;
        };
        return {
            getRows: (params: any) => {
                fetch({
                    ...schemaUrls.getListLifeCycle,
                    qs: buildParams(params?.request),
                })
                    .then((response) => {
                        if (response.status !== 200) {
                            params.failCallback();
                            return;
                        }
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);
    const handleSubmit = async () => {
        const rows = gridRef.current.api.getSelectedRows();
        if (rows.length > 0) {
            setIsLoading(true);
            const success = await assignLifeCycles(schemaName, rows);

            if (success) {
                await getSchemaLifecycles(schemaName, null, true);
                handleClose();
            }
            setIsLoading(false);
        }
    };

    const addDropZone = useCallback((params) => {
        const container = document.querySelector('#schema-detail');
        const dropZone = {
            getContainer: () => container,
            onDragStop: (params) => {
                handleSubmit();
            },
        };
        params.api.addRowDropZone(dropZone);
    }, []);

    const handleSetDataSource = useCallback((event) => {
        const dataSource = createServerSideDataSource();
        event.api.setServerSideDatasource(dataSource);
        addDropZone(event);
    }, []);

    const handleSearchChanged = (text) => {
        searchQuery.current = text;
        if (gridRef.current) {
            handleSetDataSource(gridRef.current);
        }
    };

    const handleClose = () => {
        setSelectedRows([]);
        searchQuery.current = null;
        assignLifecycleToggle.close();
    };

    const clearSelect = useCallback(() => {
        gridRef.current.api.deselectAll();
    }, [gridRef]);

    const tooltipTitleRenderer = () => {
        return selectedRows && selectedRows.length > 0 ? (
            <ul style={{ maxHeight: '200px', overflowY: 'auto', margin: 0, padding: 0 }}>
                {selectedRows.map((lifecycle: any) => (
                    <li key={lifecycle.id}>
                        <Typography sx={{ fontSize: '12px', fontWeight: 400, m: '8px' }}>{lifecycle.name}</Typography>
                    </li>
                ))}
            </ul>
        ) : (
            ''
        );
    };

    return (
        <>
            <Button
                onClick={assignLifecycleToggle.open}
                variant="contained"
                color="secondary"
                size="xs"
                endIcon={<PlusIcon />}
            >
                Assign Lifecycle
            </Button>
            <RightTray
                title={`Assign lifecycles for ${getSchemaInfo(schemaDetail, 'displayName')}`}
                componentName={COMPONENT_NAME.ASSIGN_LIFECYCLE}
                open={assignLifecycle}
                onClose={handleClose}
                onConfirm={handleSubmit}
                confirmText="Assign"
                enableSearch
                disableCloseOnClickOutside
                onSearchChange={handleSearchChanged}
            >
                <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', ...tableStyles }}>
                    <SelectedRows
                        selectedRows={selectedRows}
                        clearSelect={clearSelect}
                        tooltipItemGetter={(data) => data.name}
                    />
                    <div style={{ height: '100%', overflow: 'auto' }}>
                        <AgGridReact
                            className="ag-theme-alpine"
                            ref={gridRef}
                            {...gridOptions}
                            onGridReady={handleSetDataSource}
                            onRowDragEnter={(e) => e.node.setSelected(true)}
                            icons={tableIcons}
                        />
                    </div>
                </Box>
            </RightTray>
        </>
    );
};

export default AssignLifecycleAction;
