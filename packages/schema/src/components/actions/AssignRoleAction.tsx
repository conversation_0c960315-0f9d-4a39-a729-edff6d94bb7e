/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ISchemaDetail, useToggle } from 'ui-common';
import { Box, Button, Loading, PlusIcon, RightTray, SelectedRows, tableIcons, tableStyles, Typography } from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { getSchemaInfo } from '../../utils/helper';
import { AgGridReact } from '@ag-grid-community/react';
import useLocalStore from '../../store/useLocalStore';
import { assignRoles } from '../../actions';
import { useParams } from 'react-router-dom';
import { useRoles, useSchemaDetail } from '@tripudiotech/admin-caching-store';

const ItemRenderer = ({ value }) => {
    return <Typography variant="label2-sem">{value}</Typography>;
};
const AssignRoleAction = ({ schemaDetail }: { schemaDetail: ISchemaDetail }) => {
    const [assignRole, assignRoleToggle] = useToggle(false);
    const gridRef = useRef<AgGridReact>();
    const searchQuery = useRef(null);
    const [selectedRows, setSelectedRows] = useState([]);
    const [roles, setRoles] = useState([]);
    const [getAssignedRoles, setIsLoading] = useLocalStore((state) => [state.getAssignedRoles, state.setIsLoading]);
    const handleSelectionChanged = useCallback(() => setSelectedRows(gridRef.current.api.getSelectedRows()), []);
    const { schemaName } = useParams();
    const { getRoles } = useRoles();
    const { getSchema } = useSchemaDetail();

    const getUserRole = async () => {
        const roles = await getRoles();
        setRoles(roles.filter((role) => !schemaDetail.accessibleOrgRoles.includes(role.name)));
        return;
    };
    useEffect(() => {
        getUserRole().catch(console.error);
    }, [schemaDetail]);

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            loadingOverlayComponent: Loading,
            onSelectionChanged: handleSelectionChanged,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'inline-block',
                    border: 'none',
                }),
                getRowStyle: () => ({
                    border: 'none',
                }),
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            headerHeight: 0,
            rowHeight: 32,
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    checkboxSelection: true,
                    cellRenderer: ItemRenderer,
                },
            ],
            icons: tableIcons,
            enableCellChangeFlash: true,
        };
    }, [schemaName]);

    const handleSubmit = async () => {
        const rows = gridRef.current.api.getSelectedRows();
        if (rows.length > 0) {
            setIsLoading(true);
            const success = await assignRoles(
                schemaName,
                rows.map((row) => row.id),
                `${rows.map((row) => row.name).join(', ')} can now create ${getSchemaInfo(schemaDetail, 'displayName')}`
            );

            if (success) {
                await getAssignedRoles(schemaName, null, true);
                await getSchema(schemaName);
                handleClose();
            }
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        setSelectedRows([]);
        searchQuery.current = null;
        assignRoleToggle.close();
    };

    const clearSelect = useCallback(() => {
        gridRef.current.api.deselectAll();
    }, [gridRef]);

    const handleSearchChanged = useCallback((text) => {
        if (gridRef.current) {
            gridRef.current.api.setFilterModel({
                name: {
                    filterType: 'text',
                    type: 'contains',
                    filter: text,
                },
            });
        }
    }, []);

    return (
        <>
            <Button
                onClick={assignRoleToggle.open}
                variant="contained"
                color="secondary"
                size="xs"
                endIcon={<PlusIcon />}
            >
                Assign Role
            </Button>
            <RightTray
                title={`Assign Roles for ${getSchemaInfo(schemaDetail, 'displayName')}`}
                componentName={COMPONENT_NAME.ASSIGN_ROLE}
                open={assignRole}
                onClose={handleClose}
                onConfirm={handleSubmit}
                confirmText="Assign"
                disableCloseOnClickOutside
                enableSearch
                onSearchChange={handleSearchChanged}
            >
                <Box
                    sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        ...tableStyles,
                        '& .ag-theme-alpine .ag-row': {
                            border: 'none',
                        },
                    }}
                >
                    <SelectedRows
                        selectedRows={selectedRows}
                        clearSelect={clearSelect}
                        tooltipItemGetter={(data) => data.name}
                    />
                    <div style={{ height: '100%', overflow: 'auto' }}>
                        <AgGridReact ref={gridRef} className="ag-theme-alpine" {...gridOptions} rowData={roles} />
                    </div>
                </Box>
            </RightTray>
        </>
    );
};

export default AssignRoleAction;
