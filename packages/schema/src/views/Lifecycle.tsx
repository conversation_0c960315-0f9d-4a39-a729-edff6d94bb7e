/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { formatDateTime, IDialog, ILifeCycleDetail, ISchemaDetail } from 'ui-common';
import { BooleanRenderer, Loading, tableIcons, Typography } from 'ui-style';
import { AgGridReact } from '@ag-grid-community/react';
import useLocalStore from '../store/useLocalStore';
import { Link, useParams } from 'react-router-dom';
import get from 'lodash/get';
import { useSchemaDetail, useDialog } from '@tripudiotech/admin-caching-store';
import { updateDefaultLifecycle } from '../actions';
import AssignLifecycleAction from '../components/actions/AssignLifecycleAction';
import UnAssignLifecycleAction from '../components/actions/UnAssignLifecycleAction';
import { SchemaDetailViewContainer } from '@tripudiotech/admin-styleguide';

const LifecycleNameRenderer = ({ value, data }) => {
    return (
        <Typography
            sx={{
                fontSize: '0.875rem',
                color: (theme) => theme.palette.info.main,
                alignItems: 'center',
                display: 'flex',
                height: '100%',
            }}
        >
            <Link to={`/lifecycle/${data.lifeCycle.id}/lifecycle`} style={{ color: 'inherit' }}>
                {value}
            </Link>
        </Typography>
    );
};

const Lifecycle = () => {
    const { schemaName } = useParams();
    const schemaDetail: ISchemaDetail = useSchemaDetail((state) => state.schema[schemaName]);
    const inheritedLifecycleId = schemaDetail.lifeCycles
        .filter((lifecycle) => lifecycle.assignedOn != schemaName)
        .map((lifecycle) => lifecycle.id);

    const [lifecycles, getSchemaLifecycles, setIsLoading] = useLocalStore((state) => [
        state.lifecycles,
        state.getSchemaLifecycles,
        state.setIsLoading,
    ]);
    const gridRef = useRef<AgGridReact>(null);
    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const [selectedRows, setSelectedRows] = useState([]);

    useEffect(() => {
        const controller = new AbortController();
        getSchemaLifecycles(schemaName, controller.signal);
        return () => {
            controller.abort();
        };
    }, [schemaName]);

    const changeDefaultLifecycle = useCallback(
        async (lifeCycle: ILifeCycleDetail) => {
            setIsLoading(true);
            await updateDefaultLifecycle(schemaName, lifeCycle.lifeCycle.id);
            await getSchemaLifecycles(schemaName, null, true);
            setIsLoading(false);
        },
        [schemaName]
    );

    const handleChangingDefaultLifecycle = useCallback(
        (e, lifeCycle: ILifeCycleDetail) => {
            const defaultLifecycle = lifecycles.find((lf) => lf.lifeCycle.default);
            if (e.target.value) {
                onOpenDialog(
                    'Change default lifecycle',
                    `Do you want to change the default Lifecycle ${
                        defaultLifecycle ? `from  <b>${defaultLifecycle.lifeCycle.name}</b> ` : ''
                    }to <b>${lifeCycle.lifeCycle.name}</b>`,
                    () => changeDefaultLifecycle(lifeCycle)
                );
            }
        },
        [schemaName, lifecycles, changeDefaultLifecycle]
    );

    const handleSelectionChanged = useCallback(() => setSelectedRows(gridRef.current.api.getSelectedRows()), []);

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            loadingOverlayComponent: Loading,
            onSelectionChanged: handleSelectionChanged,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'block',
                    alignItems: 'center',
                }),
            },
            isRowSelectable: ({ data }) => {
                if (get(data, 'lifeCycle.default', false)) return false;
                return !inheritedLifecycleId.includes(get(data, 'lifeCycle.id'));
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            headerHeight: 32,
            rowHeight: 32,
            columnDefs: [
                {
                    field: 'lifeCycle.name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: LifecycleNameRenderer,
                    checkboxSelection: true,
                    showDisabledCheckboxes: true,
                },
                {
                    field: 'lifeCycle.description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'lifeCycle.default',
                    headerName: 'Default',
                    filter: 'agSetColumnFilter',
                    cellRenderer: BooleanRenderer,
                    cellRendererParams: {
                        onChange: handleChangingDefaultLifecycle,
                    },
                },
                {
                    field: 'lifeCycle.createdAt',
                    headerName: 'Created At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'lifeCycle.createdAt'));
                    },
                },
                {
                    field: 'lifeCycle.updatedAt',
                    headerName: 'Updated At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'lifeCycle.updatedAt'));
                    },
                },
            ],
            icons: tableIcons,
            enableCellChangeFlash: true,
        };
    }, [schemaName, lifecycles]);

    return (
        <SchemaDetailViewContainer>
            <div className="toolbar">
                <AssignLifecycleAction schemaDetail={schemaDetail} />
                <UnAssignLifecycleAction schemaDetail={schemaDetail} selectedRows={selectedRows} />
            </div>
            <div className="content ag-theme-alpine">
                <AgGridReact ref={gridRef} {...gridOptions} rowData={lifecycles} />
            </div>
        </SchemaDetailViewContainer>
    );
};

export default Lifecycle;
