/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import {
    <PERSON>olean<PERSON><PERSON><PERSON>,
    <PERSON>ading,
    <PERSON><PERSON><PERSON><PERSON><PERSON>lay,
    RichtextCellRenderer,
    TabGroup,
    tableIcons,
    Typography,
} from 'ui-style';
import { ISchemaDetail, SCHEMA_VIEW, schemaHelper, UI_SEARCH_PARAM } from 'ui-common';
import { Link, useParams, useSearchParams } from 'react-router-dom';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { SchemaDetailViewContainer } from '@tripudiotech/admin-styleguide';
import AddRelationshipAction from '../../components/actions/AddRelationshipAction';
import RelationshipDetail from './RelationshipDetail';
import { RelationType } from '@tripudiotech/admin-api';

const DIRECTION_TABS = [
    {
        id: 'outgoing-tab',
        label: 'Outgoing Direction',
        value: 'Outgoing',
        component: Link,
        to: '../relationship/Outgoing',
    },
    {
        id: 'icoming-tab',
        label: 'Incoming Direction',
        value: 'Incoming',
        component: Link,
        to: '../relationship/Incoming',
    },
];

const RelationShip = () => {
    const gridRef = useRef<AgGridReact>(null);
    const { schemaName, direction } = useParams();
    const schemaDetail: ISchemaDetail = useSchemaDetail((state) => state.schema[schemaName]);
    const [searchParams, setSearchParams] = useSearchParams();

    const relations = useMemo(() => {
        return schemaDetail ? schemaDetail.relationTypes : null;
    }, [schemaDetail]);
    const isOutgoing = useMemo(() => direction?.toLocaleLowerCase() === 'outgoing', [direction]);

    const setDirectionFilter = useCallback(() => {
        if (direction && gridRef.current?.api) {
            gridRef.current.api.setFilterModel({
                direction: {
                    filterType: 'set',
                    values: [direction],
                },
            });
        }
    }, [direction]);

    const onRowClicked = useCallback(
        (e) => {
            const relationType: RelationType = e.data;
            searchParams.set(UI_SEARCH_PARAM.RELATION, relationType.name);
            searchParams.set(UI_SEARCH_PARAM.VIEW, SCHEMA_VIEW.DETAIL);
            searchParams.set(UI_SEARCH_PARAM.FROM_ENTITY_TYPE, relationType.fromEntityType);
            searchParams.set(UI_SEARCH_PARAM.TO_ENTITY_TYPE, relationType.toEntityType);
            setSearchParams(searchParams);
        },
        [searchParams]
    );

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            loadingOverlayComponent: Loading,
            noRowsOverlayComponent: NoRowsOverlay,
            noRowsOverlayComponentParams: {
                message: 'No Relationship applied for this Entity Type',
            },
            onRowClicked,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            rowModelType: 'clientSide',
            rowSelection: 'single',
            headerHeight: 32,
            rowHeight: 32,
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'displayName',
                    headerName: 'Display Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: isOutgoing ? 'toEntityType' : 'fromEntityType',
                    headerName: isOutgoing ? 'To Entity' : 'From Entity Type',
                    filter: 'agTextColumnFilter',
                    cellRenderer: (props) => {
                        return (
                            <Link className="link" to={`/schema/${props.value}/metadata`}>
                                {props.value}
                            </Link>
                        );
                    },
                },
                {
                    field: 'visible',
                    headerName: 'Visible',
                    cellRenderer: BooleanRenderer,
                    maxWidth: 106,
                },
                {
                    field: 'required',
                    headerName: 'Required',
                    cellRenderer: BooleanRenderer,
                    maxWidth: 114,
                },
                {
                    field: 'system',
                    headerName: 'System',
                    cellRenderer: BooleanRenderer,
                    maxWidth: 106,
                },
                {
                    field: 'direction',
                    headerName: 'Direction',
                    hide: true,
                },
            ],
            icons: tableIcons,
            onGridReady: setDirectionFilter,
            enableCellChangeFlash: true,
        };
    }, [schemaName, direction, setDirectionFilter, onRowClicked]);

    useEffect(() => {
        setDirectionFilter();
    }, [direction]);

    return (
        <>
            {relations ? (
                <SchemaDetailViewContainer>
                    <div className="toolbar">
                        <TabGroup
                            value={direction}
                            variant="scrollable"
                            scrollButtons="auto"
                            allowScrollButtonsMobile
                            items={DIRECTION_TABS}
                            size="small"
                        />
                        <AddRelationshipAction schemaDetail={schemaDetail} />
                    </div>
                    <div className="content ag-theme-alpine">
                        <AgGridReact ref={gridRef} {...gridOptions} rowData={relations} />
                    </div>
                </SchemaDetailViewContainer>
            ) : (
                <Loading />
            )}
            <RelationshipDetail schemaDetail={schemaDetail} />
        </>
    );
};

export default RelationShip;
