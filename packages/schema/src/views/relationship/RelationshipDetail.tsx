/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';

import {
    Box,
    Button,
    MainTooltip,
    RightTray,
    Typography,
    NoRowsOverlay,
    Loading,
    Grid,
    styled,
    <PERSON><PERSON>a<PERSON>ield,
    BooleanField,
    tableI<PERSON>s,
    AttributeName<PERSON>enderer,
    Bo<PERSON>anRenderer,
    RichtextCellRenderer,
    tableStyles,
} from 'ui-style';
import {
    formatDateTime,
    ISchemaDetail,
    RelationType,
    RelationTypeDetail,
    schemaHelper,
    SCHEMA_VIEW,
    UI_SEARCH_PARAM,
} from 'ui-common';
import { COMPONENT_NAME } from '../../constants/component-name';
import useLocalStore from '../../store/useLocalStore';
import { getSchemaInfo } from '../../utils/helper';
import EditRelationAction from '../../components/actions/EditRelationAction';
import get from 'lodash/get';
import { AgGridReact } from '@ag-grid-community/react';
import { useUnitOfMeasure, useDialog, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import AddExistingAttributeRelationAction from '../../components/actions/AddExistingAttributeRelationAction';
import CreateNewRelationAttributeAction from '../../components/actions/CreateNewRelationAttributeAction';
import RelationAttributeDetail from './RelationAttributeDetail';
import { deleteRelation } from '../../actions';
import { convertQueryObject } from '@tripudiotech/admin-api';

const RelationDetailContainer = styled('div')(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    minWidth: '768px',
    '& .sectionHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        gap: '16px',
        margin: '16px 24px',
    },
    '& .relation-detail-info': {
        borderRight: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
        height: '100%',
    },
    '& .footer-action': {
        height: '72px',
        marginTop: 'auto',
        padding: '16px 24px',
        borderTop: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '16px',
    },
    '& .grid-container': {
        height: '100%',
        wordWrap: 'break-word',
    },
    '& .relation-info': {
        margin: '16px 24px',
    },
    '& .attributes': {
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
    },
    '& .toolbar': {
        margin: '8px 16px',
        display: 'flex',
        gap: '8px',
    },
}));

const RelationInfo = ({
    relationType,
    relationDetail,
    hasError,
    refresh,
    onClose,
}: {
    relationType: RelationType;
    relationDetail: RelationTypeDetail;
    hasError: boolean;
    refresh: () => void;
    onClose: () => void;
}) => {
    const gridRef = useRef<AgGridReact>(null);
    const { schemaName } = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const [getQuantityKind, getQuantityUnit] = useUnitOfMeasure((state) => [
        state.getQuantityKind,
        state.getQuantityUnit,
    ]);
    const attributes = useMemo(() => {
        return relationDetail ? Object.values(relationDetail.attributes) : null;
    }, [relationDetail]);

    const onSelectionChanged = useCallback(() => {
        const selectedRows = gridRef.current.api.getSelectedRows();
        if (selectedRows.length === 1) {
            schemaHelper.openAttributeDetail(searchParams, setSearchParams, selectedRows[0]);
        }
    }, [searchParams, setSearchParams]);

    const redrawRows = useCallback(() => gridRef.current?.api?.redrawRows(), []);

    useEffect(() => {
        if (attributes) {
            attributes.forEach((attribute) => {
                const quantityKind = attribute.unitOfMeasure?.quantityKind;
                if (quantityKind) {
                    getQuantityUnit(quantityKind);
                }
            });
            redrawRows();
        }
    }, [attributes]);

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'clientSide',
            rowSelection: 'single',
            headerHeight: 32,
            rowHeight: 32,
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: AttributeNameRenderer,
                },
                {
                    field: 'displayName',
                    headerName: 'Display Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'type',
                    headerName: 'Type',
                    filter: 'agSetColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'system',
                    headerName: 'System',
                    cellRenderer: BooleanRenderer,
                },
                {
                    field: 'nullable',
                    headerName: 'Can Be Empty',
                    cellRenderer: BooleanRenderer,
                },
            ],
            icons: tableIcons,
            onSelectionChanged: onSelectionChanged,
        };
    }, []);

    const closeAttributeDetail = useCallback(() => {
        searchParams.delete(UI_SEARCH_PARAM.ATTRIBUTE);
        setSearchParams(searchParams);
        gridRef.current.api.deselectAll();
    }, [searchParams, setSearchParams]);

    const isLoaded = hasError || relationDetail;

    const handleDelete = useCallback(async () => {
        setIsLoading(true);
        const res = await deleteRelation(relationType);
        if (res) {
            onClose();
        }
        await refresh();
        setIsLoading(false);
    }, [relationType]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Remove relation',
            `Do you really want to remove the relation <b>${relationType?.displayName}</b>?`,
            handleDelete,
            'error'
        );
    }, [relationType]);

    const isInherited = useMemo(() => {
        if (relationType) {
            return relationType.direction === 'Outgoing'
                ? relationType.fromEntityType !== schemaName
                : relationType.toEntityType !== schemaName;
        }
    }, [relationType, schemaName]);

    const otherSide = useMemo(() => {
        return relationType?.direction === 'Outgoing' ? relationType.toEntityType : relationType.fromEntityType;
    }, [relationType]);

    return (
        <RelationDetailContainer>
            <Grid container spacing={0} className="grid-container">
                <Grid item md={4} lg={3} className="relation-detail-info">
                    <div className="sectionHeader">
                        <Typography variant="ol1" style={{ marginBottom: '8px' }}>
                            GENERAL INFORMATION
                        </Typography>
                        <EditRelationAction
                            relationType={relationDetail?.relationType}
                            isInherited={isInherited}
                            otherSide={otherSide}
                        />
                    </div>
                    <div className="relation-info">
                        <SchemaField label="Name" value={get(relationType, 'name')} />
                        <SchemaField label="Display Name" value={get(relationType, 'displayName')} />
                        <SchemaField label="Description" value={get(relationType, 'description')} />
                        <SchemaField label="From Entity Type" value={get(relationType, 'fromEntityType')} />
                        <SchemaField label="To Entity Type" value={get(relationType, 'toEntityType')} />
                        <BooleanField
                            labelTitle="Indicate if the relation type is a system relation"
                            label="System"
                            value={get(relationType, 'system')}
                        />
                        <BooleanField
                            labelTitle="Indicate if the relation is required when creating or updating an entity"
                            label="Required"
                            value={get(relationType, 'required')}
                        />
                        <BooleanField
                            labelTitle="Indicate if the relation should be visible on the UI"
                            label="Visible in UI"
                            value={get(relationType, 'visible')}
                        />
                        <BooleanField
                            labelTitle="Indicate if multiple relations of the same type can exist between two entities"
                            label="Prevent Duplicate Relation"
                            value={get(relationType, 'singleRelation')}
                        />
                        <SchemaField label="Updated At" value={formatDateTime(get(relationType, 'updatedAt'))} />
                        <SchemaField label="Created At" value={formatDateTime(get(relationType, 'createdAt'))} />
                        <SchemaField
                            label="Search Criteria"
                            value={
                                relationDetail?.relationType?.constraint?.searchCriteria
                                    ? convertQueryObject(
                                          JSON.parse(relationDetail.relationType.constraint.searchCriteria)
                                      )
                                    : ''
                            }
                        />
                    </div>
                </Grid>
                <Grid item md={8} lg={9} className="relation-detail-attributes">
                    {isLoaded ? (
                        hasError ? (
                            <NoRowsOverlay
                                message="An unexpected error occured while getting the relation attributes. Please try
                                again later."
                                error
                            />
                        ) : (
                            <>
                                <div className="attributes">
                                    <div className="toolbar">
                                        <AddExistingAttributeRelationAction
                                            relationType={relationType}
                                            refresh={refresh}
                                            attributes={attributes}
                                        />
                                        <CreateNewRelationAttributeAction
                                            relationType={relationType}
                                            refresh={refresh}
                                        />
                                    </div>
                                    <div className="attributes ag-theme-alpine">
                                        <AgGridReact ref={gridRef} {...gridOptions} rowData={attributes} />
                                    </div>
                                </div>
                                <RelationAttributeDetail
                                    attributes={relationDetail?.attributes}
                                    refresh={refresh}
                                    relationType={relationType}
                                    onClose={closeAttributeDetail}
                                />
                            </>
                        )
                    ) : (
                        <Loading />
                    )}
                </Grid>
            </Grid>
            <div className="footer-action">
                <MainTooltip
                    title={
                        relationType?.system
                            ? 'You are not allowed to delete system relationship'
                            : isInherited
                            ? `This relationship is inherited from the entity type ${otherSide}. Hence you cannot delete it`
                            : ''
                    }
                >
                    <span>
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                            }}
                            variant="outlined"
                            disabled={relationType?.system || relationType?.fromEntityType !== schemaName}
                            onClick={onDelete}
                            color="error"
                        >
                            Delete
                        </Button>
                    </span>
                </MainTooltip>
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={onClose}
                >
                    Cancel
                </Button>
            </div>
        </RelationDetailContainer>
    );
};

const RelationshipDetail = ({ schemaDetail }: { schemaDetail: ISchemaDetail }) => {
    const getSchema = useSchemaDetail((state) => state.getSchema);
    const [searchParams, setSearchParams] = useSearchParams();
    const relationName = searchParams.get(UI_SEARCH_PARAM.RELATION);
    const fromEntityType = searchParams.get(UI_SEARCH_PARAM.FROM_ENTITY_TYPE);
    const toEntityType = searchParams.get(UI_SEARCH_PARAM.TO_ENTITY_TYPE);
    const [hasError, setHasError] = useState(false);
    const open = relationName && searchParams.get(UI_SEARCH_PARAM.VIEW) === SCHEMA_VIEW.DETAIL;
    const [relationDetail, clearRelationDetail, getRelationDetail] = useLocalStore((state) => [
        state.relationDetail,
        state.clearRelationDetail,
        state.getRelationDetail,
    ]);

    const relationType = useMemo(() => {
        return schemaDetail && open
            ? schemaDetail.relationTypes.find(
                  (rel: RelationType) =>
                      rel.name === relationName &&
                      rel.fromEntityType === fromEntityType &&
                      rel.toEntityType === toEntityType
              )
            : null;
    }, [schemaDetail, open, relationName, fromEntityType, toEntityType]);

    const handleClose = useCallback(() => {
        schemaHelper.closeAttributeDetail(searchParams, setSearchParams);
        clearRelationDetail();
    }, [searchParams, setSearchParams]);

    const getRelation = useCallback(async (relationType: RelationType, signal?) => {
        if (relationType) {
            const res = await getRelationDetail(
                relationType.fromEntityType,
                relationType.name,
                relationType.toEntityType,
                relationType.displayName,
                signal
            );
            if (!res) {
                setHasError(true);
            }
        }
    }, []);

    useEffect(() => {
        const controller = new AbortController();
        getRelation(relationType, controller.signal);
        return () => {
            controller.abort();
            setHasError(false);
        };
    }, [relationType, getRelation, setHasError]);

    const refreshSchema = useCallback(() => {
        if (schemaDetail) {
            getSchema(getSchemaInfo(schemaDetail, 'name'));
        }
    }, [schemaDetail]);

    return (
        <RightTray
            title={`Relationship ${relationName}`}
            componentName={COMPONENT_NAME.SCHEMA_ATTRIBUTE_DETAIL}
            open={Boolean(open)}
            onClose={handleClose}
            fullWidth
            hideConfirm
            anchor="bottom"
        >
            {relationType === undefined ? (
                <NoRowsOverlay
                    messageComponent={
                        <Typography key="no-relation-type-found" variant="label1-reg">
                            Relation type <b>{relationName}</b> was not found on the Entity Type{' '}
                            <b>{getSchemaInfo(schemaDetail, 'displayName')}</b>
                        </Typography>
                    }
                />
            ) : relationType === null ? (
                <Loading />
            ) : (
                <Box sx={{ height: '100%', overflow: 'auto' }}>
                    <RelationInfo
                        refresh={refreshSchema}
                        hasError={hasError}
                        relationType={relationType}
                        relationDetail={relationDetail}
                        onClose={handleClose}
                    />
                </Box>
            )}
        </RightTray>
    );
};
export default RelationshipDetail;
