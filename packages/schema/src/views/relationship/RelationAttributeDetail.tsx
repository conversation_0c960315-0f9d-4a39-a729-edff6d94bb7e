/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { AttributeSchema, UI_SEARCH_PARAM, RelationType, useToggle, ISchemaDetail } from 'ui-common';
import { AttributeForm, Box, Button, MainTooltip, RightTray } from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { useDialog, useUnitOfMeasure } from '@tripudiotech/admin-caching-store';
import useLocalStore from '../../store/useLocalStore';
import { deleteRelationAttribute, updateRelationAttribute } from '../../actions';
import AttributeInformation from '../../components/attribute/AttributeInformation';

const RelationAttributeDetail = ({
    attributes,
    relationType,
    refresh,
    onClose,
}: {
    attributes: Record<string, AttributeSchema>;
    relationType: RelationType;
    refresh: () => void;
    onClose: () => void;
}) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const attributeName = searchParams.get(UI_SEARCH_PARAM.ATTRIBUTE);
    const attributeDetail = useMemo(() => {
        if (attributeName && attributes) {
            return attributes[attributeName];
        }
        return null;
    }, [attributes, attributeName]);
    const open = attributeName && attributeDetail;
    const formRef = useRef(null);
    const [isEditing, isEditingToggle] = useToggle(false);

    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);

    const handleDelete = useCallback(async () => {
        setIsLoading(true);
        const res = await deleteRelationAttribute(relationType, attributeDetail);
        await refresh();
        if (res) {
            onClose();
        }
        setIsLoading(false);
    }, [relationType, refresh, attributeDetail]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete attribute',
            `Do you really want to delete the attribute <b>${attributeDetail?.displayName}</b> from the relation type <b>${relationType?.displayName}</b>?`,
            handleDelete,
            'error'
        );
    }, [onOpenDialog, handleDelete]);

    const isSystem = attributeDetail?.system;

    const [quantityKind, quantityUnit, qdtUnitMapper, qdtKindMapper, getQuantityUnit] = useUnitOfMeasure((state) => [
        state.quantityKind,
        state.quantityUnit,
        state.qdtUnitMapper,
        state.qdtKindMapper,
        state.getQuantityUnit,
    ]);

    const handleSubmit = useCallback(
        async (values) => {
            if (formRef.current.dirty) {
                setIsLoading(true);
                const res = await updateRelationAttribute(relationType, attributeDetail.name, values);
                await refresh();
                setIsLoading(false);
                if (res) {
                    isEditingToggle.close();
                }
            } else {
                isEditingToggle.close();
            }
        },
        [attributeDetail]
    );

    return (
        <RightTray
            title={`Attribute ${attributeName}`}
            componentName={COMPONENT_NAME.SCHEMA_ATTRIBUTE_DETAIL}
            open={Boolean(open)}
            onClose={onClose}
            hideConfirm
        >
            {attributeDetail && (
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    {isEditing ? (
                        <AttributeForm
                            formRef={formRef}
                            attributeDetail={attributeDetail}
                            quantityKind={quantityKind}
                            quantityUnit={quantityUnit}
                            qdtKindMapper={qdtKindMapper}
                            qdtUnitMapper={qdtUnitMapper}
                            getQuantityUnit={getQuantityUnit}
                            isUpdating
                            onSubmit={handleSubmit}
                            schemaDetail={undefined}
                            schemaTreeMap={undefined}
                        />
                    ) : (
                        <AttributeInformation attributeDetail={attributeDetail} />
                    )}
                </Box>
            )}
            <Box sx={{ mt: 'auto', display: 'flex', gap: '8px', justifyContent: 'flex-end', margin: '16px 24px' }}>
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={onClose}
                >
                    Cancel
                </Button>
                <MainTooltip title={isSystem ? 'You are not allowed to delete system attribute' : ''}>
                    <span>
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                            }}
                            variant="outlined"
                            disabled={isSystem}
                            onClick={onDelete}
                            color="error"
                        >
                            Delete
                        </Button>
                    </span>
                </MainTooltip>
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="primary"
                    onClick={() => (isEditing ? formRef.current.submitForm() : isEditingToggle.open())}
                >
                    {isEditing ? 'Save' : 'Edit'}
                </Button>
            </Box>
        </RightTray>
    );
};
export default RelationAttributeDetail;
