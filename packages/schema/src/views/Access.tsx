/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { formatDateTime, ISchemaDetail } from 'ui-common';
import { NoRowsOverlay, Loading, tableIcons, Typography } from 'ui-style';
import { AgGridReact } from '@ag-grid-community/react';
import useLocalStore from '../store/useLocalStore';
import { useParams } from 'react-router-dom';
import get from 'lodash/get';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { SchemaDetailViewContainer } from '@tripudiotech/admin-styleguide';
import AssignRoleAction from '../components/actions/AssignRoleAction';
import UnassignRoleAction from '../components/actions/UnassignRoleAction';
import { getSchemaInfo } from '../utils/helper';

const Access = () => {
    const { schemaName } = useParams();
    const schemaDetail: ISchemaDetail = useSchemaDetail((state) => state.schema[schemaName]);

    const [accessedRoles, getAssignedRoles, setIsLoading] = useLocalStore((state) => [
        state.assignedRoles,
        state.getAssignedRoles,
        state.setIsLoading,
    ]);
    const gridRef = useRef<AgGridReact>(null);
    const [selectedRows, setSelectedRows] = useState([]);

    useEffect(() => {
        const controller = new AbortController();
        getAssignedRoles(schemaName, controller.signal);
        return () => {
            controller.abort();
        };
    }, [schemaName]);

    const handleSelectionChanged = useCallback(() => setSelectedRows(gridRef.current.api.getSelectedRows()), []);

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            loadingOverlayComponent: Loading,
            onSelectionChanged: handleSelectionChanged,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'block',
                    alignItems: 'center',
                }),
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            headerHeight: 32,
            rowHeight: 32,
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    checkboxSelection: true,
                    showDisabledCheckboxes: true,
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'createdAt',
                    headerName: 'Created At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'createdAt'));
                    },
                },
                {
                    field: 'updatedAt',
                    headerName: 'Updated At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                },
            ],
            icons: tableIcons,
            enableCellChangeFlash: true,
        };
    }, [schemaName, accessedRoles]);

    const successMessage = useMemo(() => {
        if (selectedRows.length === 0) {
            return null;
        }
        return `${selectedRows.map((row) => row.name).join(', ')} ${
            selectedRows.length == 1 ? 'role' : 'roles'
        } removed from creating ${getSchemaInfo(schemaDetail, 'displayName')}`;
    }, [selectedRows, schemaDetail]);

    return (
        <SchemaDetailViewContainer>
            <div className="toolbar">
                <AssignRoleAction schemaDetail={schemaDetail} />
                <UnassignRoleAction
                    schemaDetail={schemaDetail}
                    selectedRows={selectedRows.map((data) => data.id)}
                    successMessage={successMessage}
                />
            </div>
            <div className="content ag-theme-alpine">
                <AgGridReact
                    ref={gridRef}
                    {...gridOptions}
                    rowData={accessedRoles}
                    noRowsOverlayComponent={NoRowsOverlay}
                    noRowsOverlayComponentParams={{
                        message: `Currently any user can create ${schemaDetail.entityType.displayName}s`,
                    }}
                />
            </div>
        </SchemaDetailViewContainer>
    );
};

export default Access;
