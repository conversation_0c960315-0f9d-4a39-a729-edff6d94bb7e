/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect } from 'react';
import { useEdgesState, useNodesState, useReactFlow } from 'reactflow';
import { Box, Button, CircularProgress, DeleteIcon, EditIcon, SchemaFieldSection, styled, Typography } from 'ui-style';
import 'reactflow/dist/style.css';
import useDigitalThreadStore from '../../store/useDigitalThreadStore';
import { useNavigate, useParams } from 'react-router-dom';
import { IDigitalThreadSchema, RelationType } from 'ui-common';
import { createEdgesAndNodesFromRelationTypes, getLayoutedElements } from '../../utils/graphUtils';
import DigitalThreadDiagram from '../../components/flow/DigitalThreadDiagram';
import { useDialog } from '@tripudiotech/admin-caching-store';

const Container = styled(Box)(({ theme }) => ({
    height: '100%',
    width: '100%',
    position: 'relative',
    backgroundColor: theme.palette.glide.background.normal.inversePrimary,
}));

const DigitalThreadDetail = () => {
    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);
    const { getEdges, getNodes, fitView } = useReactFlow<any, RelationType>();
    const [
        isReadOnly,
        setIsReadOnly,
        isLoading,
        digitalThread,
        getDigitalThreadDetails,
        updateDigitalThread,
        deleteDigitalThread,
    ] = useDigitalThreadStore((state) => [
        state.isReadOnly,
        state.setIsReadOnly,
        state.isLoading,
        state.digitalThread,
        state.getDigitalThreadDetails,
        state.updateDigitalThread,
        state.deleteDigitalThread,
    ]);
    const { id } = useParams();
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const navigate = useNavigate();
    const initializeDiagram = useCallback(
        (dt: IDigitalThreadSchema) => {
            const { nodes, edges } = createEdgesAndNodesFromRelationTypes(dt.relations);
            const layouted = getLayoutedElements(nodes, edges, 'LR', 240, 100);
            setNodes(layouted.nodes);
            setEdges(layouted.edges);
            setTimeout(() => {
                fitView({ duration: 300, padding: 0.2 });
            }, 200);
        },
        [fitView]
    );

    const onEditSave = async () => {
        const result = await updateDigitalThread(id, getEdges());
        if (result) {
            setIsReadOnly(true);
        }
    };
    const onCancelEdit = () => {
        setIsReadOnly(true);
        initializeDiagram(digitalThread);
    };
    const handleDelete = async () => {
        const result = await deleteDigitalThread(id);
        if (result) {
            navigate('/digital-thread');
        }
    };
    const onDelete = () => {
        onOpenDialog(
            'Delete digital thread',
            `<div style="margin-top:16px">Do you really want to delete <b>${digitalThread.digitalThread.name}</b>?</div>`,
            handleDelete,
            'error'
        );
    };

    useEffect(() => {
        if (digitalThread) {
            initializeDiagram(digitalThread);
        }
    }, [digitalThread]);

    useEffect(() => {
        const abortController = new AbortController();
        getDigitalThreadDetails(id, abortController.signal);
        return () => {
            abortController.abort();
            setIsReadOnly(true);
        };
    }, [id]);

    return (
        <Container id="detail-view">
            {isLoading && (
                <Box
                    sx={{
                        position: 'absolute',
                        display: 'flex',
                        height: '100%',
                        width: '100%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 1000,
                    }}
                >
                    <CircularProgress />
                </Box>
            )}
            {digitalThread && (
                <>
                    <Box
                        sx={{
                            position: 'fixed',
                            top: '120px',
                            right: '16px',
                            borderRadius: '8px',
                            background: (theme) => theme.palette.glide.background.normal.white,
                            padding: '16px',
                            boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px',
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '8px',
                            width: '280px',
                            zIndex: 10,
                        }}
                    >
                        <SchemaFieldSection>Digital Thread</SchemaFieldSection>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography
                                sx={{ color: (theme) => theme.palette.glide.text.normal.inversePrimary }}
                                variant="label3-reg"
                            >
                                Name
                            </Typography>
                            <Typography
                                sx={{ color: (theme) => theme.palette.glide.text.normal.inverseTertiary }}
                                variant="label3-reg"
                            >
                                {digitalThread?.digitalThread?.name}
                            </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', gap: '8px', flexDirection: 'column' }}>
                            <Typography
                                sx={{ color: (theme) => theme.palette.glide.text.normal.inversePrimary }}
                                variant="label3-reg"
                            >
                                Description
                            </Typography>
                            <Typography
                                sx={{
                                    color: (theme) => theme.palette.glide.text.normal.inverseTertiary,
                                    whiteSpace: 'pre-wrap',
                                }}
                                variant="label3-reg"
                            >
                                {digitalThread?.digitalThread?.description}
                            </Typography>
                        </Box>
                    </Box>
                    <Box sx={{ display: 'flex', height: '100%', width: '100%' }}>
                        <DigitalThreadDiagram
                            nodes={nodes}
                            edges={edges}
                            onNodesChange={onNodesChange}
                            onEdgesChange={onEdgesChange}
                            isReadOnly={isReadOnly}
                        />
                    </Box>

                    <Box sx={{ position: 'fixed', bottom: '24px', right: '24px', display: 'flex', gap: '8px' }}>
                        {isReadOnly ? (
                            <>
                                <Button
                                    variant="contained"
                                    color="error"
                                    size="small"
                                    onClick={onDelete}
                                    endIcon={<DeleteIcon />}
                                >
                                    Delete
                                </Button>
                                <Button
                                    variant="contained"
                                    color="primary"
                                    size="small"
                                    onClick={() => setIsReadOnly(false)}
                                    endIcon={<EditIcon />}
                                >
                                    Edit
                                </Button>
                            </>
                        ) : (
                            <>
                                <Button size="small" variant="contained" color="secondary" onClick={onCancelEdit}>
                                    Cancel
                                </Button>
                                <Button size="small" variant="contained" color="primary" onClick={onEditSave}>
                                    Save
                                </Button>
                            </>
                        )}
                    </Box>
                </>
            )}
        </Container>
    );
};

export default DigitalThreadDetail;
