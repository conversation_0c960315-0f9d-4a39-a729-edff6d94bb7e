/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
    AnimatedPage,
    Box,
    CloseIcon,
    ContentHeader,
    FixedHeightContainer,
    InputAdornment,
    SearchIcon,
    styled,
    tableStyles,
    TextField,
    Loading,
    DigitalThreadIcon,
    Typography,
} from 'ui-style';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { Link, Outlet } from 'react-router-dom';
import { AgGridReact } from '@ag-grid-community/react';
import { TreeContainer } from '@tripudiotech/admin-styleguide';
import isEmpty from 'lodash/isEmpty';
import useDigitalThreadStore from '../../store/useDigitalThreadStore';
import CreateDigitalThreadAction from '../../components/actions/CreateDigitalThreadAction';
import { ReactFlowProvider } from 'reactflow';

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const RowRenderer = ({ value, data }) => {
    return (
        <Link to={data.id} style={{ textDecoration: 'none', color: 'inherit' }}>
            <Box sx={{ width: '100%', display: 'flex', gap: '8px', cursor: 'pointer', alignItems: 'center' }}>
                <DigitalThreadIcon style={{ height: '16px', width: '16px' }} />
                <Typography variant="bo2">{value}</Typography>
            </Box>
        </Link>
    );
};

const DigitalThread = () => {
    const [searchText, setSearchText] = useState('');
    const digitalThreadPanelRef = useRef<any>();
    const gridRef = useRef<AgGridReact>();
    const onSearchChanged = useCallback((e) => {
        const { value } = e.target;
        setSearchText(value);
    }, []);

    const [digitalThreads, getAll, isReadOnly] = useDigitalThreadStore((state) => [
        state.digitalThreads,
        state.getAll,
        state.isReadOnly,
    ]);

    const gridOptions: any = useMemo(
        () => ({
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                filter: true,
                floatingFilter: false,
                flex: 1,
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RowRenderer,
                },
            ],
            rowModelType: 'clientSide',
            rowSelection: 'single',
            getRowStyle: () => ({
                background: ' #FFFFFF',
                border: 'none',
            }),
            gridOptions: {
                headerHeight: 0,
                rowHeight: 26,
            },
            loadingOverlayComponent: Loading,
        }),
        []
    );

    useEffect(() => {
        getAll();
    }, [getAll]);

    useEffect(() => {
        if (gridRef.current?.api) {
            gridRef.current.api.setFilterModel({
                name: {
                    filterType: 'text',
                    type: 'contains',
                    filter: searchText,
                },
            });
        }
    }, [searchText]);

    useEffect(() => {
        if (isReadOnly) {
            digitalThreadPanelRef.current.expand();
        } else {
            digitalThreadPanelRef.current.collapse();
        }
    }, [isReadOnly]);

    return (
        <AnimatedPage>
            <FixedHeightContainer>
                <ContentHeader
                    title="Digital Thread"
                    actionsRenderer={() => (
                        <ReactFlowProvider>
                            <CreateDigitalThreadAction />
                        </ReactFlowProvider>
                    )}
                />
                <ContentWrapper>
                    <PanelGroup direction="horizontal">
                        <Panel
                            id="digital-thread-list-panel"
                            defaultSize={20}
                            minSize={18}
                            collapsible
                            maxSize={80}
                            style={{ height: '100%', position: 'relative' }}
                            ref={digitalThreadPanelRef}
                        >
                            <TreeContainer>
                                <TextField
                                    className="searchBox"
                                    size="medium"
                                    value={searchText}
                                    onChange={onSearchChanged}
                                    fullWidth
                                    placeholder="Type to search"
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment
                                                position="end"
                                                style={{
                                                    visibility: isEmpty(searchText) ? 'hidden' : 'visible',
                                                    cursor: 'pointer',
                                                }}
                                                onClick={() => setSearchText('')}
                                            >
                                                <CloseIcon sx={{ width: '16px', height: '16px' }} />
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                <div className="agContainer ag-theme-alpine">
                                    <AgGridReact {...gridOptions} ref={gridRef} rowData={digitalThreads} />
                                </div>
                            </TreeContainer>
                            <PanelResizeHandle className="handle"></PanelResizeHandle>
                        </Panel>

                        <Panel id="digital-thread-detail-panel" style={{ height: '100%', position: 'relative' }}>
                            <Outlet />
                        </Panel>
                    </PanelGroup>
                </ContentWrapper>
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default DigitalThread;
