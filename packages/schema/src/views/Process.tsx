/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Loading, NoRowsOverlay, RichtextCellRenderer, tableIcons, Typography } from 'ui-style';
import { AgGridReact } from '@ag-grid-community/react';
import useLocalStore from '../store/useLocalStore';
import { Link, useParams } from 'react-router-dom';
import get from 'lodash/get';
import { useSchemaDetail, useEventTypes } from '@tripudiotech/admin-caching-store';
import { EventType, formatDateTime, ISchemaDetail } from 'ui-common';
import ApplyProcessAction from '../components/actions/ApplyProcessAction';
import RevokeProcessAction from '../components/actions/RevokeProcessAction';
import { SchemaDetailViewContainer } from '@tripudiotech/admin-styleguide';

const ProcessNameRenderer = ({ value, data }) => {
    return (
        <Typography
            sx={{
                fontSize: '0.875rem',
                color: (theme) => theme.palette.info.main,
                alignItems: 'center',
                display: 'flex',
                height: '100%',
            }}
        >
            <Link to={`/process/${data.id}`} style={{ color: 'inherit' }}>
                {value}
            </Link>
        </Typography>
    );
};

const Process = () => {
    const gridRef = useRef<AgGridReact>(null);
    const [selectedRows, setSelectedRows] = useState([]);
    const { schemaName } = useParams();
    const [appliedProcess, getSchemaAppliedProcess] = useLocalStore((state) => [
        state.appliedProcess,
        state.getSchemaAppliedProcess,
    ]);
    const { getEventTypes, eventTypes }: { eventTypes: Record<string, EventType>; getEventTypes: any } =
        useEventTypes();
    const schemaDetail: ISchemaDetail = useSchemaDetail((state) => state.schema[schemaName]);

    useEffect(() => {
        const controller = new AbortController();
        getSchemaAppliedProcess(schemaName, controller.signal);
        getEventTypes();
        return () => {
            controller.abort();
        };
    }, [schemaName]);

    const handleSelectionChanged = useCallback(() => setSelectedRows(gridRef.current.api.getSelectedRows()), []);

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            loadingOverlayComponent: Loading,
            noRowsOverlayComponent: NoRowsOverlay,
            noRowsOverlayComponentParams: {
                message: `No Process applied for this Entity Type`,
            },
            onSelectionChanged: handleSelectionChanged,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'inline-block',
                }),
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            headerHeight: 32,
            rowHeight: 32,
            suppressRowClickSelection: true,
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: ProcessNameRenderer,
                    checkboxSelection: true,
                    showDisabledCheckboxes: true,
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'eventType',
                    headerName: 'Event Type',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                    valueGetter: ({ data }) =>
                        get(data, 'eventTypes', [])
                            .map((eventType) => get(eventTypes, [eventType, 'description'], eventType))
                            .join(', '),
                },
                {
                    field: 'condition',
                    headerName: 'Trigger Condition',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'createdAt',
                    headerName: 'Created At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'createdAt'));
                    },
                },
                {
                    field: 'updatedAt',
                    headerName: 'Updated At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                },
            ],
            icons: tableIcons,
            enableCellChangeFlash: true,
        };
    }, [schemaName, eventTypes]);

    return eventTypes && appliedProcess ? (
        <SchemaDetailViewContainer>
            <div className="toolbar">
                <ApplyProcessAction
                    schemaDetail={schemaDetail}
                    appliedProcess={appliedProcess}
                    eventTypes={eventTypes}
                />
                <RevokeProcessAction schemaDetail={schemaDetail} selectedProcess={selectedRows} />
            </div>
            <div className="content ag-theme-alpine">
                <AgGridReact ref={gridRef} {...gridOptions} rowData={appliedProcess} />
            </div>
        </SchemaDetailViewContainer>
    ) : (
        <Loading />
    );
};

export default Process;
