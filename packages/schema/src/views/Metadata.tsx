/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { AnimatedPage, BooleanField, Box, Grid, SchemaField, styled, Typography } from 'ui-style';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { Link, useParams } from 'react-router-dom';
import { formatDateTime, ISchemaDetail } from 'ui-common';
import { getSchemaInfo } from '../utils/helper';
import EditMetadataAction from '../components/actions/EditMetadataAction';
import { DECOMPOSITION_MODEL, DECOMPOSITION_MODEL_LABEL } from '../constants';

const MetaDataContainer = styled('div')(({ theme }) => ({
    margin: '16px',
    '& .sectionHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        gap: '16px',
    },
}));

const Metadata = () => {
    const { schemaName } = useParams();
    const schemaDetail: ISchemaDetail = useSchemaDetail((state) => state.schema[schemaName]);
    const { decompositionModel } = schemaDetail;
    return (
        <AnimatedPage>
            <MetaDataContainer>
                <div className="sectionHeader">
                    <Typography variant="ol1" style={{ marginBottom: '8px' }}>
                        GENERAL INFORMATION
                    </Typography>
                    <EditMetadataAction schemaDetail={schemaDetail} />
                </div>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <SchemaField label="Name" value={getSchemaInfo(schemaDetail, 'name')} />
                        <SchemaField label="Display Name" value={getSchemaInfo(schemaDetail, 'displayName')} />
                        <SchemaField label="Description" value={getSchemaInfo(schemaDetail, 'description')} />
                        <BooleanField
                            label="System"
                            labelTitle="Indicate if the entity type is system type"
                            value={getSchemaInfo(schemaDetail, 'system')}
                        />
                        <BooleanField
                            label="Extendable"
                            labelTitle="Indicate if the entity type is extendable"
                            value={getSchemaInfo(schemaDetail, 'extendable')}
                        />
                        <BooleanField
                            label="Abstract"
                            labelTitle="Indicate if the entity type is an abstract class"
                            value={getSchemaInfo(schemaDetail, 'abstract')}
                        />
                        <BooleanField
                            label="Visible in UI"
                            labelTitle="Indicate if the Entity Type is visible on the UI"
                            value={getSchemaInfo(schemaDetail, 'visible')}
                        />
                    </Grid>

                    <Grid item xs={12} md={6}>
                        <SchemaField
                            label="Created Date"
                            value={formatDateTime(getSchemaInfo(schemaDetail, 'createdAt'))}
                        />
                        <SchemaField
                            label="Updated Date"
                            value={formatDateTime(getSchemaInfo(schemaDetail, 'updatedAt'))}
                        />
                        <SchemaField label="Created By" value={getSchemaInfo(schemaDetail, 'createdBy')} />
                    </Grid>
                </Grid>
                <Box sx={{ my: '8px' }}>
                    <Typography variant="ol1">DECOMPOSITION MODEL</Typography>
                </Box>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <SchemaField label="Type" value={DECOMPOSITION_MODEL_LABEL[decompositionModel?.type]} />
                    </Grid>
                    {decompositionModel?.type !== DECOMPOSITION_MODEL.NONE && (
                        <Grid item xs={12} md={6}>
                            <SchemaField
                                label="Decomposition Name"
                                value={
                                    <Link to={`/schema/${decompositionModel.name}/metadata`}>
                                        <Typography
                                            variant="label2-reg"
                                            sx={{ color: (theme) => theme.palette.glide.info }}
                                        >
                                            {decompositionModel.name}
                                        </Typography>
                                    </Link>
                                }
                            />
                        </Grid>
                    )}
                </Grid>
            </MetaDataContainer>
        </AnimatedPage>
    );
};

export default Metadata;
