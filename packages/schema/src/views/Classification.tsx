/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo, useRef } from 'react';
import { Loading, NoRowsOverlay, Typography } from 'ui-style';
import { SchemaDetailViewContainer } from '@tripudiotech/admin-styleguide';
import { AgGridReact } from '@ag-grid-community/react';
import ApplyClassification from '../components/actions/ApplyClassificationAction';
import { useClassificationTree, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { useParams } from 'react-router-dom';
import { ISchemaDetail } from 'ui-common';

const Classification = () => {
    const { schemaName } = useParams();
    const gridRef = useRef<AgGridReact>(null);
    const schemaDetail: ISchemaDetail = useSchemaDetail((state) => state.schema[schemaName]);

    const [classificationTreeMap, getClassificationTree] = useClassificationTree((state) => [
        state.classificationTreeMap,
        state.getClassificationTree,
    ]);

    const classifications = useMemo(() => {
        return classificationTreeMap && schemaDetail
            ? Object.values(classificationTreeMap).filter((classification: any) =>
                  schemaDetail.classifications.includes(classification.name)
              )
            : null;
    }, [schemaDetail, classificationTreeMap]);

    useEffect(() => {
        getClassificationTree();
    }, []);

    const gridOptions: any = useMemo(
        () => ({
            columnDefs: [
                {
                    field: 'description',
                    headerName: 'Description',
                },
                {
                    field: 'createdBy',
                    headerName: 'Created By',
                },
            ],
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                editable: false,
                cellStyle: () => ({
                    display: 'inline-block',
                }),
            },
            autoGroupColumnDef: {
                field: 'name',
                headerName: 'Name',
                cellClass: 'ag-cell-wrapper',
                cellRendererParams: {
                    suppressCount: true,
                },
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            headerHeight: 32,
            rowHeight: 32,
            treeData: true,
            animateRows: true,
            groupDefaultExpanded: -1,
            getDataPath: (data) => {
                return data.path;
            },
            enableCellChangeFlash: true,
        }),
        []
    );
    return classifications ? (
        <SchemaDetailViewContainer>
            <div className="toolbar">
                <ApplyClassification
                    classificationTreeMap={classificationTreeMap}
                    appliedClassifications={schemaDetail?.classifications || []}
                />
            </div>
            <div className="content ag-theme-alpine">
                <AgGridReact
                    ref={gridRef}
                    {...gridOptions}
                    rowData={classifications}
                    noRowsOverlayComponent={NoRowsOverlay}
                    noRowsOverlayComponentParams={{
                        message: `Currently, there are no Classifications applied on ${schemaDetail.entityType.displayName}`,
                    }}
                />
            </div>
        </SchemaDetailViewContainer>
    ) : (
        <Loading />
    );
};

export default Classification;
