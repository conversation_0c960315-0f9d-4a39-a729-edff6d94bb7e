import React, { useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { RightTray, Box, LoadingOverlay, Button, AttributeForm } from 'ui-style';
import AttributeInformation from '../../components/attribute/AttributeInformation';
import useAttributeStore from '../../store/useAttributeStore';
import { useToggle } from 'ui-common';
import { useSchemaTree, useUnitOfMeasure } from '@tripudiotech/admin-caching-store';

const AttributeDetailView = () => {
    const { attributeId } = useParams();
    const { attributeDetail, isLoading, getAttributeDetail, updateAttribute } = useAttributeStore();
    const navigate = useNavigate();
    const formRef = useRef(null);
    const [isEditing, isEditingToggle] = useToggle(false);
    const schemaTreeMap = useSchemaTree((state) => state.schemaTreeMap);

    const [quantityKind, quantityUnit, qdtUnitMapper, qdtKindMapper, getQuantityUnit] = useUnitOfMeasure((state) => [
        state.quantityKind,
        state.quantityUnit,
        state.qdtUnitMapper,
        state.qdtKindMapper,
        state.getQuantityUnit,
    ]);

    const handleSubmit = async (values) => {
        if (formRef.current.dirty) {
            const res = await updateAttribute(attributeId, values);
            if (res) {
                isEditingToggle.close();
            }
        } else {
            isEditingToggle.close();
        }
    };

    const handleClose = () => {
        isEditingToggle.close();
    };

    useEffect(() => {
        if (attributeId) {
            getAttributeDetail(attributeId);
        }
    }, [attributeId]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title={`Attribute ${attributeDetail?.name || ''}`}
                open={true}
                onClose={() => {
                    navigate('/attribute');
                }}
                hideConfirm
            >
                {attributeDetail && (
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            height: '100%',
                            p: '16px',
                            gap: '16px',
                            overflow: 'auto',
                        }}
                    >
                        {isEditing ? (
                            <AttributeForm
                                formRef={formRef}
                                attributeDetail={attributeDetail}
                                onSubmit={handleSubmit}
                                quantityKind={quantityKind}
                                quantityUnit={quantityUnit}
                                qdtKindMapper={qdtKindMapper}
                                qdtUnitMapper={qdtUnitMapper}
                                getQuantityUnit={getQuantityUnit}
                                isUpdating
                                schemaTreeMap={schemaTreeMap}
                                schemaDetail={undefined}
                                context="standalone"
                            />
                        ) : (
                            <AttributeInformation
                                attributeDetail={attributeDetail}
                                schemas={attributeDetail.schemas || []}
                                context="standalone"
                            />
                        )}
                    </Box>
                )}
                <Box sx={{ mt: 'auto', display: 'flex', gap: '8px', justifyContent: 'flex-end', margin: '16px 24px' }}>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={handleClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="primary"
                        onClick={() => (isEditing ? formRef.current?.submitForm() : isEditingToggle.open())}
                    >
                        {isEditing ? 'Save' : 'Edit'}
                    </Button>
                </Box>
            </RightTray>
            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default AttributeDetailView;
