/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useToggle, ISchemaDetail, schema<PERSON><PERSON>per, AttributeSchema, UI_SEARCH_PARAM, SCHEMA_VIEW } from 'ui-common';
import { Box, Button, MainTooltip, RightTray, AttributeForm } from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import { useSchemaDetail, useDialog, useUnitOfMeasure, useSchemaTree } from '@tripudiotech/admin-caching-store';
import { getSchemaInfo } from '../../utils/helper';
import useLocalStore from '../../store/useLocalStore';
import { deleteAttribute, updateEntityTypeAttribute } from '../../actions';
import AttributeInformation from '../../components/attribute/AttributeInformation';

const AttributeDetail = ({ schemaDetail }: { schemaDetail: ISchemaDetail }) => {
    const getSchema = useSchemaDetail((state) => state.getSchema);
    const formRef = useRef(null);
    const [searchParams, setSearchParams] = useSearchParams();
    const attributeName = searchParams.get(UI_SEARCH_PARAM.ATTRIBUTE);
    const open = attributeName && searchParams.get(UI_SEARCH_PARAM.VIEW) === SCHEMA_VIEW.DETAIL;
    const { schemaTreeMap } = useSchemaTree();

    const [isEditing, isEditingToggle] = useToggle(false);

    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);

    /**
     * Unit of measure cache
     */
    const [quantityKind, quantityUnit, qdtUnitMapper, qdtKindMapper, getQuantityUnit] = useUnitOfMeasure((state) => [
        state.quantityKind,
        state.quantityUnit,
        state.qdtUnitMapper,
        state.qdtKindMapper,
        state.getQuantityUnit,
    ]);

    /**
     * Attribute Detail from schema
     */
    const attributeDetail: AttributeSchema = useMemo(() => {
        if (attributeName && schemaDetail) {
            return schemaDetail.attributes[attributeName];
        }
    }, [attributeName, schemaDetail]);

    const handleClose = useCallback(() => {
        // clear all search params
        schemaHelper.closeAttributeDetail(searchParams, setSearchParams);
        isEditingToggle.close();
    }, [searchParams, setSearchParams]);

    const handleDelete = useCallback(async () => {
        setIsLoading(true);
        const res = await deleteAttribute(schemaDetail, attributeDetail);
        if (res) {
            handleClose();
        }
        await getSchema(schemaDetail.entityType.name);
        setIsLoading(false);
    }, [schemaDetail, attributeDetail]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete attribute',
            `Do you really want to delete the attribute <b>${
                attributeDetail?.displayName
            }</b> from the entity type <b>${getSchemaInfo(schemaDetail, 'displayName')}</b>?`,
            handleDelete,
            'error'
        );
    }, [onOpenDialog, handleDelete]);

    const handleSubmit = useCallback(
        async (values) => {
            if (formRef.current.dirty) {
                setIsLoading(true);
                const res = await updateEntityTypeAttribute(schemaDetail.entityType.name, attributeDetail.name, values);
                await getSchema(schemaDetail.entityType.name);
                setIsLoading(false);
                if (res) {
                    isEditingToggle.close();
                }
            } else {
                isEditingToggle.close();
            }
        },
        [schemaDetail, attributeDetail]
    );

    const isSystem = attributeDetail?.system;
    const attributeOf = attributeDetail?.attributeOf;
    const isInherited = attributeOf !== getSchemaInfo(schemaDetail, 'name');

    return (
        <RightTray
            title={`Attribute ${attributeName}`}
            componentName={COMPONENT_NAME.SCHEMA_ATTRIBUTE_DETAIL}
            open={Boolean(open)}
            onClose={handleClose}
            disableCloseOnClickOutside={isEditing}
            hideConfirm
        >
            {attributeDetail && (
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    {isEditing ? (
                        <AttributeForm
                            formRef={formRef}
                            attributeDetail={attributeDetail}
                            schemaDetail={schemaDetail}
                            onSubmit={handleSubmit}
                            quantityKind={quantityKind}
                            quantityUnit={quantityUnit}
                            qdtKindMapper={qdtKindMapper}
                            qdtUnitMapper={qdtUnitMapper}
                            schemaTreeMap={schemaTreeMap}
                            getQuantityUnit={getQuantityUnit}
                            isUpdating
                        />
                    ) : (
                        <AttributeInformation schemaDetail={schemaDetail} attributeDetail={attributeDetail} />
                    )}
                </Box>
            )}
            <Box sx={{ mt: 'auto', display: 'flex', gap: '8px', justifyContent: 'flex-end', margin: '16px 24px' }}>
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={() => (isEditing ? isEditingToggle.close() : handleClose())}
                >
                    Cancel
                </Button>
                {!isEditing && (
                    <MainTooltip
                        title={
                            isInherited ? (
                                <span>
                                    This attribute is inherited from <b>{attributeOf}</b>. Hence you cannot delete it
                                    from the current entity type.
                                </span>
                            ) : isSystem ? (
                                'You are not allowed to modify system attribute'
                            ) : (
                                ''
                            )
                        }
                    >
                        <span>
                            <Button
                                sx={{
                                    width: '136px',
                                    justifyContent: 'flex-start',
                                }}
                                variant="outlined"
                                disabled={isSystem || isInherited}
                                onClick={onDelete}
                                color="error"
                            >
                                Delete
                            </Button>
                        </span>
                    </MainTooltip>
                )}
                {/* TODO: Implement attribute modification restrictions for system entity types once they are consolidated under an abstract parent type. */}
                <Button
                    sx={{
                        width: '136px',
                        justifyContent: 'flex-start',
                        maxWidth: '260px',
                    }}
                    variant="contained"
                    onClick={() => (isEditing ? formRef.current.submitForm() : isEditingToggle.open())}
                    color="primary"
                >
                    {isEditing ? 'Save' : 'Edit'}
                </Button>
            </Box>
        </RightTray>
    );
};
export default AttributeDetail;
