/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ISchemaDetail, UI_SEARCH_PARAM, SCHEMA_VIEW, schemaHelper, IDialog, ACTION, SYSTEM_RELATION } from 'ui-common';
import {
    RightTray,
    Box,
    Button,
    FormikTextField,
    TextField,
    Autocomplete,
    Chip,
    DISABLED_IDENTIFIER_ATTRIBUTES,
    Typography,
    Grid,
    PlusIcon,
    Menu,
    MenuItem,
    DeleteIcon,
    IconButton,
} from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import get from 'lodash/get';
import * as yup from 'yup';
import { Formik, Form, Field } from 'formik';
import { useDialog, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import useLocalStore from '../../store/useLocalStore';
import { createIdentifier, deleteIdentifier, updateIdentifier } from '../../actions';
import flatten from 'lodash/flatten';
import omit from 'lodash/omit';

const validationSchema = yup.object().shape({
    nextAssignmentElements: yup
        .array()
        .of(
            yup.object().shape({
                type: yup.string().required('Type is required'),
                value: yup.string().required('Value is required'),
            })
        )
        .min(1, 'At least one assignment element is required'),

    prefix: yup.string().nullable(),
    suffix: yup.string().nullable(),
    skipCharacters: yup.string().nullable(),
});

enum AssignmentType {
    relationAttribute = 'relationAttribute',
    attribute = 'attribute',
    increment = 'increment',
}

const AssignmentTypeLabel = {
    relationAttribute: 'Relation Attribute',
    attribute: 'Attribute',
    increment: 'Increment',
};

type AssignmentPattern = {
    type: AssignmentType;
    value: string;
};

const parseNextAssignmentValue = (value: string): AssignmentPattern[] => {
    if (!value) return [];

    const regex = /\{[^{}]+\}|[^{}]+/g;
    const matches = value.match(regex) || [];

    return matches
        .map((match) => match.trim())
        .filter(Boolean)
        .map((match): AssignmentPattern => {
            // Handle curly brace wrapped values
            if (match.startsWith('{') && match.endsWith('}')) {
                const innerValue = match.slice(1, -1); // Remove { and }

                // Check if it contains a dot (relation attribute)
                if (innerValue.includes('.')) {
                    return {
                        type: AssignmentType.relationAttribute,
                        value: innerValue,
                    };
                } else {
                    return {
                        type: AssignmentType.attribute,
                        value: innerValue,
                    };
                }
            } else {
                // Plain text without curly braces
                return {
                    type: AssignmentType.increment,
                    value: match,
                };
            }
        });
};

const formatNextAssignmentElements = (elements: AssignmentPattern[]): string => {
    if (!elements || elements.length === 0) return '';

    return elements
        .map((element) => (element.type === AssignmentType.increment ? element.value : `{${element.value}}`))
        .join('');
};

const Identifier = ({ schemaDetail }: { schemaDetail: ISchemaDetail }) => {
    const getSchema = useSchemaDetail((state) => state.getSchema);
    const [attributeOptions, setAttributeOptions] = useState([]);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const openMenu = Boolean(anchorEl);
    const [relationOptions, setRelationOptions] = useState([]);
    const formRef = useRef(null);
    const [searchParams, setSearchParams] = useSearchParams();
    const attributeName = searchParams.get(UI_SEARCH_PARAM.ATTRIBUTE);
    const open = attributeName && searchParams.get(UI_SEARCH_PARAM.VIEW) === SCHEMA_VIEW.IDENTIFIER;
    const action = searchParams.get(UI_SEARCH_PARAM.ACTION);
    const isUpdating = action === ACTION.UPDATE;

    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);

    const selectedAttribute = useMemo(() => {
        return get(schemaDetail, ['attributes', attributeName]);
    }, [schemaDetail, attributeName]);

    const disabled = DISABLED_IDENTIFIER_ATTRIBUTES.includes(selectedAttribute?.name);

    const initialValues = useMemo(() => {
        return isUpdating && selectedAttribute?.identifier
            ? {
                  ...selectedAttribute.identifier,
                  nextAssignmentElements: parseNextAssignmentValue(selectedAttribute?.identifier?.nextAssignment || ''),
              }
            : {
                  nextAssignment: '',
                  prefix: '',
                  suffix: '',
                  skipCharacters: '',
                  nextAssignmentElements: [],
              };
    }, [selectedAttribute]);

    const handleClose = useCallback(() => {
        schemaHelper.closeIdentifier(searchParams, setSearchParams);
    }, [searchParams, setSearchParams]);

    const handleCreate = useCallback(
        async (values) => {
            setIsLoading(true);
            await createIdentifier(schemaDetail.entityType.name, selectedAttribute.name, values);
            await getSchema(schemaDetail.entityType.name);
            setIsLoading(false);
            handleClose();
        },
        [schemaDetail, selectedAttribute]
    );

    const handleUpdate = useCallback(
        async (values) => {
            setIsLoading(true);
            await updateIdentifier(schemaDetail.entityType.name, selectedAttribute.name, values);
            await getSchema(schemaDetail.entityType.name);
            setIsLoading(false);
            handleClose();
        },
        [schemaDetail, selectedAttribute]
    );

    const handleSubmit = useCallback(
        (values) => {
            if (formRef.current.dirty) {
                // Format nextAssignmentElements into nextAssignment string
                const formattedValues = {
                    ...omit(values, 'nextAssignmentElements'),
                    nextAssignment: formatNextAssignmentElements(values.nextAssignmentElements),
                };

                if (isUpdating) {
                    handleUpdate(formattedValues);
                } else {
                    handleCreate(formattedValues);
                }
            } else {
                handleClose();
            }
        },
        [selectedAttribute, handleClose, isUpdating, handleUpdate, handleCreate]
    );

    const handleDeleteIdentifier = useCallback(async () => {
        setIsLoading(true);
        const res = await deleteIdentifier(schemaDetail.entityType.name, selectedAttribute.name);
        if (res) {
            await getSchema(schemaDetail.entityType.name);
            setIsLoading(false);
            handleClose();
        }
        setIsLoading(false);
    }, [schemaDetail, selectedAttribute, handleClose]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete auto generated value',
            `Do you really want to delete the <b>auto generated value</b> on attribute <b>${selectedAttribute.displayName}</b>?`,
            handleDeleteIdentifier,
            'error'
        );
    }, [schemaDetail, selectedAttribute, handleDeleteIdentifier]);

    const fetchSchema = useCallback(async (schemaDetail: ISchemaDetail) => {
        if (schemaDetail) {
            try {
                setAttributeOptions(
                    Object.values(schemaDetail.attributes).map((attribute) => ({
                        label: attribute.displayName,
                        value: `{${attribute.name}}`,
                        group: 'Attribute',
                    }))
                );
                const requiredEntityTypes = schemaDetail.relationTypes.filter(
                    (relation) => relation.required && relation.direction === 'Outgoing'
                );
                if (requiredEntityTypes.length > 0) {
                    const relEntityTypes = await Promise.all(
                        requiredEntityTypes.map(async (relation) => {
                            const schema = await getSchema(relation.toEntityType);
                            return Object.values(schema.attributes).map((attribute) => ({
                                label: attribute.displayName,
                                value: `${schema.entityType.name}.${attribute.name}`,
                                group: `${relation.displayName} - ${schema.entityType.displayName}`,
                            }));
                        })
                    );
                    setRelationOptions(flatten(relEntityTypes));
                }
            } catch (error) {
                console.error(error);
            }
        }
    }, []);

    const handleMenuClose = () => {
        setAnchorEl(null);
    };

    useEffect(() => {
        fetchSchema(schemaDetail);
    }, [schemaDetail]);

    return (
        <RightTray
            title={`Auto Generated Value of ${attributeName}`}
            componentName={COMPONENT_NAME.EDIT_METADATA}
            open={Boolean(open)}
            onClose={handleClose}
            confirmText={isUpdating ? 'Save changes' : 'Create'}
            disableCloseOnClickOutside
            hideConfirm
        >
            <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', p: '0px 24px', pb: '16px' }}>
                <Box sx={{ overflow: 'auto', pt: '16px' }}>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleSubmit(values);
                            setSubmitting(false);
                        }}
                    >
                        <Formik
                            enableReinitialize
                            initialValues={initialValues}
                            innerRef={formRef}
                            validationSchema={validationSchema}
                            onSubmit={(values, { setSubmitting }) => {
                                handleSubmit(values as any);
                                setSubmitting(false);
                            }}
                        >
                            {({ values, errors, touched, setFieldValue, ...rest }) => {
                                return (
                                    <Form
                                        id="identifier-form"
                                        style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
                                    >
                                        <Typography
                                            sx={{ color: (theme) => theme.palette.glide.info }}
                                            variant="label2-med"
                                            className="sectionLabel"
                                        >
                                            Pattern Builder
                                        </Typography>

                                        <Box
                                            sx={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                gap: '12px',
                                                borderRadius: '4px',
                                                border: (theme) =>
                                                    `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                                padding: '16px',
                                            }}
                                        >
                                            <Grid container spacing={1}>
                                                <Grid item xs={3} sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <Typography variant="label2-med">Prefix:</Typography>
                                                </Grid>
                                                <Grid item xs={9}>
                                                    <Field
                                                        fullWidth
                                                        component={FormikTextField}
                                                        name="prefix"
                                                        variant="outlined"
                                                    />
                                                </Grid>
                                                <Grid item xs={12}>
                                                    <Grid container spacing={1}>
                                                        {values.nextAssignmentElements?.map((element, idx) => (
                                                            <Grid item xs={12} key={`element-${idx}`}>
                                                                <Grid container spacing={1}>
                                                                    <Grid
                                                                        item
                                                                        xs={3}
                                                                        sx={{ display: 'flex', alignItems: 'center' }}
                                                                    >
                                                                        <Typography variant="label2-med">
                                                                            {AssignmentTypeLabel[element.type]}:
                                                                        </Typography>
                                                                    </Grid>
                                                                    <Grid item xs={8}>
                                                                        {element.type === 'attribute' ? (
                                                                            <Autocomplete
                                                                                options={attributeOptions}
                                                                                value={
                                                                                    attributeOptions.find(
                                                                                        (option) =>
                                                                                            option.value ===
                                                                                            values
                                                                                                ?.nextAssignmentElements?.[
                                                                                                idx
                                                                                            ]?.value
                                                                                    ) ||
                                                                                    values?.nextAssignmentElements?.[
                                                                                        idx
                                                                                    ]?.value
                                                                                }
                                                                                onChange={(_, newValue) =>
                                                                                    setFieldValue(
                                                                                        `nextAssignmentElements.${idx}.value`,
                                                                                        newValue?.value
                                                                                    )
                                                                                }
                                                                                renderInput={(params) => (
                                                                                    <TextField
                                                                                        {...params}
                                                                                        required
                                                                                        helperText={
                                                                                            Boolean(
                                                                                                get(errors, [
                                                                                                    'nextAssignmentElements',
                                                                                                    idx,
                                                                                                    'value',
                                                                                                ]) &&
                                                                                                    get(touched, [
                                                                                                        'nextAssignmentElements',
                                                                                                        idx,
                                                                                                        'value',
                                                                                                    ])
                                                                                            )
                                                                                                ? get(errors, [
                                                                                                      'nextAssignmentElements',
                                                                                                      idx,
                                                                                                      'value',
                                                                                                  ])
                                                                                                : ''
                                                                                        }
                                                                                        error={Boolean(
                                                                                            get(errors, [
                                                                                                'nextAssignmentElements',
                                                                                                idx,
                                                                                                'value',
                                                                                            ]) &&
                                                                                                get(touched, [
                                                                                                    'nextAssignmentElements',
                                                                                                    idx,
                                                                                                    'value',
                                                                                                ])
                                                                                        )}
                                                                                    />
                                                                                )}
                                                                            />
                                                                        ) : element.type === 'relationAttribute' ? (
                                                                            <Autocomplete
                                                                                options={relationOptions}
                                                                                value={
                                                                                    relationOptions.find(
                                                                                        (option) =>
                                                                                            option.value ===
                                                                                            values
                                                                                                ?.nextAssignmentElements?.[
                                                                                                idx
                                                                                            ]?.value
                                                                                    ) ||
                                                                                    values?.nextAssignmentElements?.[
                                                                                        idx
                                                                                    ]?.value
                                                                                }
                                                                                onChange={(_, newValue) =>
                                                                                    setFieldValue(
                                                                                        `nextAssignmentElements.${idx}.value`,
                                                                                        newValue?.value
                                                                                    )
                                                                                }
                                                                                groupBy={(option) => option.group}
                                                                                renderInput={(params) => (
                                                                                    <TextField
                                                                                        {...params}
                                                                                        required
                                                                                        helperText={
                                                                                            Boolean(
                                                                                                get(errors, [
                                                                                                    'nextAssignmentElements',
                                                                                                    idx,
                                                                                                    'value',
                                                                                                ]) &&
                                                                                                    get(touched, [
                                                                                                        'nextAssignmentElements',
                                                                                                        idx,
                                                                                                        'value',
                                                                                                    ])
                                                                                            )
                                                                                                ? get(errors, [
                                                                                                      'nextAssignmentElements',
                                                                                                      idx,
                                                                                                      'value',
                                                                                                  ])
                                                                                                : ''
                                                                                        }
                                                                                        error={Boolean(
                                                                                            get(errors, [
                                                                                                'nextAssignmentElements',
                                                                                                idx,
                                                                                                'value',
                                                                                            ]) &&
                                                                                                get(touched, [
                                                                                                    'nextAssignmentElements',
                                                                                                    idx,
                                                                                                    'value',
                                                                                                ])
                                                                                        )}
                                                                                    />
                                                                                )}
                                                                            />
                                                                        ) : (
                                                                            <Field
                                                                                fullWidth
                                                                                component={FormikTextField}
                                                                                name={`nextAssignmentElements.${idx}.value`}
                                                                                variant="outlined"
                                                                            />
                                                                        )}
                                                                    </Grid>
                                                                    <Grid
                                                                        item
                                                                        xs={1}
                                                                        sx={{ display: 'flex', alignItems: 'center' }}
                                                                    >
                                                                        <IconButton
                                                                            size="small"
                                                                            onClick={() => {
                                                                                const newElements = [
                                                                                    ...values.nextAssignmentElements,
                                                                                ];
                                                                                newElements.splice(idx, 1);
                                                                                setFieldValue(
                                                                                    'nextAssignmentElements',
                                                                                    newElements
                                                                                );
                                                                            }}
                                                                            aria-label="delete element"
                                                                        >
                                                                            <DeleteIcon />
                                                                        </IconButton>
                                                                    </Grid>
                                                                </Grid>
                                                            </Grid>
                                                        ))}

                                                        <Grid
                                                            item
                                                            xs={12}
                                                            sx={{ display: 'flex', justifyContent: 'flex-end' }}
                                                        >
                                                            <Box
                                                                sx={{
                                                                    display: 'flex',
                                                                    gap: '16px',
                                                                    alignItems: 'center',
                                                                }}
                                                            >
                                                                {values.nextAssignmentElements?.length === 0 &&
                                                                    errors.nextAssignmentElements &&
                                                                    touched.nextAssignmentElements && (
                                                                        <Typography
                                                                            sx={{
                                                                                color: (theme) =>
                                                                                    theme.palette.error.main,
                                                                                marginLeft: '8px',
                                                                            }}
                                                                            variant="label2-med"
                                                                        >
                                                                            {errors.nextAssignmentElements}
                                                                        </Typography>
                                                                    )}
                                                                <Button
                                                                    endIcon={<PlusIcon />}
                                                                    variant="contained-blue"
                                                                    size="small"
                                                                    onClick={(e) => setAnchorEl(e.currentTarget)}
                                                                >
                                                                    Add Element
                                                                </Button>
                                                            </Box>
                                                            <Menu
                                                                anchorEl={anchorEl}
                                                                open={openMenu}
                                                                onClose={handleMenuClose}
                                                            >
                                                                {Object.values(AssignmentType).map((value) => (
                                                                    <MenuItem
                                                                        key={value}
                                                                        value={value}
                                                                        onClick={() => {
                                                                            setFieldValue('nextAssignmentElements', [
                                                                                ...values.nextAssignmentElements,
                                                                                { type: value, value: '' },
                                                                            ]);
                                                                            handleMenuClose();
                                                                        }}
                                                                    >
                                                                        {AssignmentTypeLabel[value]}
                                                                    </MenuItem>
                                                                ))}
                                                            </Menu>
                                                        </Grid>
                                                    </Grid>
                                                </Grid>
                                                <Grid item xs={2} sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <Typography variant="label2-med">Suffix:</Typography>
                                                </Grid>

                                                <Grid item xs={10}>
                                                    <Field
                                                        fullWidth
                                                        component={FormikTextField}
                                                        name="suffix"
                                                        variant="outlined"
                                                    />
                                                </Grid>
                                                <Grid item xs={2} sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <Typography variant="label2-med">Skip Characters:</Typography>
                                                </Grid>

                                                <Grid item xs={10}>
                                                    <Field
                                                        fullWidth
                                                        component={FormikTextField}
                                                        name="skipCharacters"
                                                        variant="outlined"
                                                    />
                                                </Grid>
                                            </Grid>
                                        </Box>
                                        <Typography
                                            sx={{ color: (theme) => theme.palette.glide.info }}
                                            variant="label2-med"
                                            className="sectionLabel"
                                        >
                                            Preview:
                                        </Typography>
                                        <Box
                                            sx={{
                                                bgcolor: '#f5f5f5',
                                                padding: '8px 16px',
                                                borderRadius: '4px',
                                                maxHeight: '200px',
                                                overflowY: 'auto',
                                                fontFamily: 'monospace',
                                                border: (theme) =>
                                                    `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                            }}
                                        >
                                            <Typography variant="label2-med">
                                                {values.prefix}
                                                {formatNextAssignmentElements(values.nextAssignmentElements)}
                                                {values.suffix}
                                            </Typography>
                                        </Box>
                                    </Form>
                                );
                            }}
                        </Formik>
                    </Formik>
                </Box>
                <Box sx={{ mt: 'auto', display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={handleClose}
                    >
                        Cancel
                    </Button>
                    {isUpdating && (
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                            }}
                            variant="outlined"
                            onClick={onDelete}
                            color="error"
                            disabled={disabled}
                        >
                            Delete
                        </Button>
                    )}
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        onClick={() => formRef.current.submitForm()}
                        color="primary"
                        disabled={disabled}
                    >
                        Save
                    </Button>
                </Box>
            </Box>
        </RightTray>
    );
};

export default Identifier;
