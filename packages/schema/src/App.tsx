/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import { LicenseManager } from '@ag-grid-enterprise/core';
import '@ag-grid-community/core/dist/styles/ag-grid.css';
import '@ag-grid-community/core/dist/styles/ag-theme-alpine.css';
import { GlideThemeProvider } from 'ui-style';
import { ModuleRegistry } from '@ag-grid-community/core';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ClipboardModule } from '@ag-grid-enterprise/clipboard';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import SchemaDetail from './layouts/SchemaDetail';
import Metadata from './views/Metadata';
import Attribute from './views/attribute/Attribute';
import Lifecycle from './views/Lifecycle';
import RelationShip from './views/relationship/Relationship';
import Classification from './views/Classification';
import Process from './views/Process';
import Access from './views/Access';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import EmptyView from './views/EmptyView';
import DigitalThread from './views/digital-thread/DigitalThread';
import DigitalThreadDetail from './views/digital-thread/DigitalThreadDetail';
import { useEffect } from 'react';
import { useSchemaTree } from '@tripudiotech/admin-caching-store';
import { ReactFlowProvider } from 'reactflow';
import EmptyDigitalThread from './views/digital-thread/EmptyDigitalThread';
import AttributeLayout from './layouts/AttributeLayout';
import AttributeDetailView from './views/attribute/AttributeDetailView';
import AttributeListView from './views/attribute/AttributeListView';

LicenseManager.setLicenseKey(process.env.AG_GRID_LICENSE);

ModuleRegistry.registerModules([
    RowGroupingModule,
    ExcelExportModule,
    SetFilterModule,
    ColumnsToolPanelModule,
    FiltersToolPanelModule,
    MenuModule,
    ClipboardModule,
    ClientSideRowModelModule,
    ServerSideRowModelModule,
]);

export default function Root(props) {
    const getSchemaTree = useSchemaTree((state) => state.getSchemaTree);
    useEffect(() => {
        getSchemaTree();
    }, []);
    return (
        <GlideThemeProvider>
            <BrowserRouter basename="admin">
                <Routes>
                    <Route path="/schema" element={<MainLayout />}>
                        <Route path="" element={<EmptyView />} />
                        <Route path=":schemaName" element={<SchemaDetail />}>
                            <Route path="" element={<Navigate to="metadata" />} />
                            <Route path="metadata" element={<Metadata />} />
                            <Route path="attribute" element={<Attribute />} />
                            <Route path="lifecycle" element={<Lifecycle />} />
                            <Route path="relationship" element={<Navigate to="Outgoing" />} />
                            <Route path="relationship/:direction" element={<RelationShip />} />
                            <Route path="classification" element={<Classification />} />
                            <Route path="process" element={<Process />} />
                            <Route path="access" element={<Access />} />
                        </Route>
                    </Route>
                    <Route path="/digital-thread" element={<DigitalThread />}>
                        <Route
                            path=":id"
                            element={
                                <ReactFlowProvider>
                                    <DigitalThreadDetail />
                                </ReactFlowProvider>
                            }
                        />
                        <Route index element={<EmptyDigitalThread />} />
                    </Route>
                    <Route path="/attribute" element={<AttributeLayout />}>
                        <Route path="" element={<AttributeListView />} >
                            <Route path=":attributeId" element={<AttributeDetailView />} />
                        </Route>
                    </Route>
                </Routes>
            </BrowserRouter>
        </GlideThemeProvider>
    );
}
