
import { create } from 'zustand';
import { AttributeDetail, fetch, schemaUrls } from "@tripudiotech/admin-api";

type AttributeStore = {
    isLoading: boolean;
    attributeDetail: AttributeDetail;
    getAttributeDetail: (attributeId: string) => Promise<void>;
    updateAttribute: (attributeId: string, data: Partial<AttributeDetail>) => Promise<boolean>;
}

const initialStates = {
    isLoading: false,
    attributeDetail: null,
}

const useAttributeStore = create<AttributeStore>((set, get) => ({
    ...initialStates,
    getAttributeDetail: async (attributeId: string) => {
        try {
            set({ isLoading: true });
            const response = await fetch({
                ...schemaUrls.getAttributeDetail,
                params: { attributeId },
            });
            set({ attributeDetail: response.data });
        } catch (error) {
            console.error('Error fetching attribute detail:', error);
        } finally {
            set({ isLoading: false });
        }
    },
    updateAttribute: async (attributeId: string, data: Partial<AttributeDetail>) => {
        try {
            set({ isLoading: true });
            await fetch({
                ...schemaUrls.updateAttributeById,
                params: { attributeId },
                data,
            });
            return true;
        } catch (error) {
            console.error('Error updating attribute:', error);
            return false;
        } finally {
            set({ isLoading: false });
        }
    },
}))

export default useAttributeStore