/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Edge, Node } from 'reactflow';
import { create } from 'zustand';
import { fetch, schemaUrls, DEFAULT_CLIENT_SIDE_LIMIT } from '@tripudiotech/admin-api';
import { IDigitalThread, IDigitalThreadSchema, RelationType } from 'ui-common';

type DigitalThreadStore = {
    digitalThreads: IDigitalThread[];
    digitalThread: IDigitalThreadSchema;
    nodes: Node[];
    edges: Edge[];
    isLoading?: boolean;
    isReadOnly?: boolean;
    isOpenCreate?: boolean;
    setIsLoading: (value: boolean) => void;
    createDigitalThread: (metadata: any, edges: Edge<RelationType>[]) => Promise<IDigitalThreadSchema>;
    getAll: () => Promise<IDigitalThread[]>;
    setIsReadOnly: (value: boolean) => void;
    getDigitalThreadDetails: (id: string, signal: any) => Promise<any>;
    updateDigitalThread: (id: string, edges: Edge<RelationType>[]) => Promise<any>;
    deleteDigitalThread: (id: string) => Promise<any>;
    setIsOpenCreate: (value: boolean) => void;
};

const initialStates = {
    digitalThreads: null,
    digitalThread: null,
    isLoading: false,
    isReadOnly: true,
    isOpenCreate: false,
    nodes: [],
    edges: [],
};

const useDigitalThreadStore = create<DigitalThreadStore>((set, get) => ({
    ...initialStates,
    setIsLoading: (isLoading: boolean) => set({ isLoading }),
    createDigitalThread: async (metadata, edges: Edge<RelationType>[]) => {
        set({ isLoading: true });
        const { name, description } = metadata;
        const request = {
            name,
            description,
            relations: edges.map((edge) => ({
                fromEntityType: edge.data.fromEntityType,
                toEntityType: edge.data.toEntityType,
                name: edge.data.name,
            })),
        };

        try {
            const { data } = await fetch({
                ...schemaUrls.createDigitalThread,
                data: request,
                successMessage: 'Successfully created new digital thread',
            });
            await get().getAll();
            return data;
        } catch (e) {
        } finally {
            set({ isLoading: false });
        }
    },
    getAll: async () => {
        set({ isLoading: true });
        try {
            const { data } = await fetch({
                ...schemaUrls.getAllDigitalThreads,
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });
            set({ digitalThreads: data.data });
            return data.data as IDigitalThread[];
        } catch (e) {
        } finally {
            set({ isLoading: false });
        }
    },
    setIsReadOnly: (isReadOnly: boolean) => {
        console.log('check', isReadOnly);
        set({ isReadOnly });
    },
    getDigitalThreadDetails: async (id, signal) => {
        set({ isLoading: true });
        try {
            const { data } = await fetch({
                ...schemaUrls.getDigitalThreadById,
                params: {
                    id,
                },
                signal,
            });
            set({ digitalThread: data });
            return data;
        } catch (e) {
        } finally {
            set({ isLoading: false });
        }
    },
    updateDigitalThread: async (id: string, edges: Edge<RelationType>[]) => {
        set({ isLoading: true });
        try {
            const req = {
                name: get().digitalThread.digitalThread.name,
                description: get().digitalThread.digitalThread.description,
                relations: edges.map((edge) => ({
                    fromEntityType: edge.data.fromEntityType,
                    toEntityType: edge.data.toEntityType,
                    name: edge.data.name,
                })),
            };
            const { data } = await fetch({
                ...schemaUrls.updateDigitalThread,
                params: {
                    id,
                },
                data: req,
            });
            set({ digitalThread: data });
            return data;
        } catch (err) {
        } finally {
            set({ isLoading: false });
        }
    },
    deleteDigitalThread: async (id: string) => {
        try {
            set({ isLoading: true });
            const data = await fetch({
                ...schemaUrls.deleteDigitalThread,
                params: { id },
            });
            await get().getAll();
            return data;
        } catch (err) {
        } finally {
            set({ isLoading: false });
        }
    },
    setIsOpenCreate: (isOpenCreate: boolean) => set({ isOpenCreate }),
}));

export default useDigitalThreadStore;
