/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { ILifeCycleDetail, IAppliedProcess, RelationTypeDetail } from 'ui-common';
import { create } from 'zustand';
import { fetch, schemaUrls } from '@tripudiotech/admin-api';

type SchemaLocalStore = {
    isLoading: boolean;
    isLoadingLifecycles: boolean;
    isLoadingProcess: boolean;
    isLoadingRoles: boolean;
    lifecycles: ILifeCycleDetail[];
    appliedProcess: IAppliedProcess[];
    relationDetail: RelationTypeDetail;
    setIsLoading: (value: boolean) => void;
    getSchemaLifecycles: (entityType: string, signal?: any, force?: boolean) => void;
    getSchemaAppliedProcess: (entityType: string, signal?: any, force?: boolean) => void;
    getRelationDetail: (
        fromEntityType: string,
        relationType: string,
        toEntityType: string,
        displayName: string,
        signal?
    ) => Promise<boolean>;
    clearRelationDetail: () => void;
    assignedRoles: any[];
    getAssignedRoles: (entityType: string, signal?: any, force?: boolean) => void;
    clearStore: () => void;
};

const initialStates = {
    isLoading: false,
    isLoadingLifecycles: false,
    isLoadingProcess: false,
    isLoadingRoles: false,
    lifecycles: null,
    appliedProcess: null,
    relationDetail: null,
    assignedRoles: null,
};

const useLocalStore = create<SchemaLocalStore>((set, get) => ({
    ...initialStates,
    setIsLoading: (isLoading: boolean) => {
        set({ isLoading });
    },
    getSchemaLifecycles: async (entityTypeName: string, signal?: any, force = false) => {
        if (get().lifecycles && !force) return;

        set({ isLoadingLifecycles: true });

        try {
            const { data } = await fetch({
                ...schemaUrls.fetchEntityLifecycle,
                params: { entityTypeName },
            });
            set({ lifecycles: data.data });
        } catch (error) {
            console.error(error);
        } finally {
            set({ isLoadingLifecycles: false });
        }
    },
    getSchemaAppliedProcess: async (entityType: string, signal?: any, force = false) => {
        if (get().appliedProcess && !force) return;
        set({ isLoadingProcess: true });

        try {
            const { data } = await fetch({
                ...schemaUrls.getAppliedProcess,
                params: { entityType },
                signal,
            });
            set({ appliedProcess: data });
        } catch (error) {
            console.error(error);
        } finally {
            set({ isLoadingProcess: false });
        }
    },
    getRelationDetail: async (
        fromEntityType: string,
        relationType: string,
        toEntityType: string,
        displayName: string,
        signal?: any
    ): Promise<boolean> => {
        try {
            const { data } = await fetch({
                ...schemaUrls.getRelation,
                params: { fromEntityType, relationType, toEntityType },
                signal,
                skipToast: true,
            });
            set({ relationDetail: data });
            return true;
        } catch (err: any) {
            /**
             * https://glidesyslm.atlassian.net/browse/GY-382
             * Handle creating relation in case missing in data migration
             */
            if (err?.response?.status === 404) {
                await fetch({
                    ...schemaUrls.createRelation,
                    data: {
                        name: relationType,
                        displayName,
                        fromEntityTypeName: fromEntityType,
                        toEntityTypeName: toEntityType,
                    },
                });

                const { data } = await fetch({
                    ...schemaUrls.getRelation,
                    params: { fromEntityType, relationType, toEntityType },
                    signal,
                });
                set({ relationDetail: data });
                return true;
            }
            return false;
        } finally {
        }
    },
    clearRelationDetail: () => set({ relationDetail: null }),
    getAssignedRoles: async (entityType: string, signal?: any, force = false) => {
        if (get().assignedRoles && !force) return;
        set({ isLoadingRoles: true });

        try {
            const { data } = await fetch({
                ...schemaUrls.getAccessedRoles,
                params: {
                    entityType,
                },
                signal,
            });
            set({ assignedRoles: data });
        } catch (error) {
            console.error(error);
        } finally {
            set({ isLoadingRoles: false });
        }
    },
    clearStore: () => {
        set({ ...initialStates });
    },
}));

export default useLocalStore;
