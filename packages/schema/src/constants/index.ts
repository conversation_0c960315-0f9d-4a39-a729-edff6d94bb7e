import { AttributeType, QUERY } from '@tripudiotech/admin-api';
import { Condition } from '../components/advanced-search/FilterComponents';

/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const MASTER_MODEL_OPTIONS = [
    { id: 'withoutMaster', value: 'Without Master' },
    { id: 'withMaster', value: 'With Master' },
];

export const DECOMPOSITION_MODEL_OPTIONS = [
    {
        id: 'NONE',
        value: 'None',
    },
    {
        id: 'REV_TO_MASTER',
        value: 'Revision to Master',
    },
    {
        id: 'REV_TO_REV',
        value: 'Revision to Revision',
    },
];

export const DECOMPOSITION_MODEL = {
    REV_TO_MASTER: 'REV_TO_MASTER',
    REV_TO_REV: 'REV_TO_REV',
    NONE: 'NONE',
};

export const DECOMPOSITION_MODEL_LABEL = {
    REV_TO_MASTER: 'Revision to Master',
    REV_TO_REV: 'Revision to Revision',
    NONE: 'None',
};

export const EXPAND_ENTITY_TYPE = 'EXPAND_ENTITY_TYPE';

export const ZOOM_OPTIONS = {
    duration: 500,
};
export const EMPTY_FILTER_VALUE = { field: '', value: null };
export const DEFAULT_FILTER_VALUE: Condition = { operator: '', values: { field: '', value: null }, filterType: '' };
export const DEFAULT_GROUP_FILTER_VALUE = { operator: QUERY.AND, values: [], filterType: 'group' };
export const INITIAL_CONDITION: Condition = {
    operator: QUERY.AND,
    values: [DEFAULT_FILTER_VALUE],
    filterType: 'group',
};

export const FILTER_OPTION_LABEL = {
    attribute: 'Attribute',
    relation: 'Relation',
    lifecycleState: 'Lifecycle State',
    classification: 'Classification',
    relationExists: 'Relation Exists',
    relationAttribute: 'Relation Attribute',
} as const;
export const RELATION_FILTER_OPTIONS = [
    {
        label: FILTER_OPTION_LABEL.attribute,
        value: 'attribute',
    },
    {
        label: FILTER_OPTION_LABEL.relationExists,
        value: 'relationExists',
    },
    {
        label: FILTER_OPTION_LABEL.relationAttribute,
        value: 'relationAttribute',
    },
];
export const FILTER_OPTIONS: any = [
    {
        label: FILTER_OPTION_LABEL.attribute,
        value: 'attribute',
    },
    {
        label: FILTER_OPTION_LABEL.relation,
        value: 'relation',
    },
    {
        label: FILTER_OPTION_LABEL.lifecycleState,
        value: 'lifecycleState',
    },
    {
        label: FILTER_OPTION_LABEL.classification,
        value: 'classification',
    },
] as const;
export const LIFECYCLE_STATE_OPTION_LABEL = {
    ['state.name']: 'Name',
    ['state.isOfficial']: 'Is Official',
    ['state.isObsoleted']: 'Is Obsoleted',
    ['state.isSuperseded']: 'Is Superseded',
} as const;
export const LIFECYCLE_STATE_FILTER_OPTIONS = [
    {
        label: 'Name',
        value: 'state.name',
    },
    {
        label: 'Is Official',
        value: 'state.isOfficial',
    },
    {
        label: 'Is Obsoleted',
        value: 'state.isObsoleted',
    },
    {
        label: 'Is Superseded',
        value: 'state.isSuperseded',
    },
] as const;

export const GROUP_OPERATORS = [
    {
        label: 'Or',
        value: QUERY.OR,
    },
    {
        label: 'And',
        value: QUERY.AND,
    },
] as const;

export const NUMBER_OPERATORS = [
    {
        label: 'Is Null',
        value: QUERY.IS_NULL,
    },
    {
        label: 'Is not Null',
        value: QUERY.IS_NON_NULL,
    },
    {
        label: 'Equal',
        value: QUERY.EQUAL,
    },
    {
        label: 'Not Equal',
        value: QUERY.NOT_EQUAL,
    },
    {
        label: 'Less Than',
        value: QUERY.LT,
    },
    {
        label: 'Less Than or Equal',
        value: QUERY.LTE,
    },
    {
        label: 'Greater Than',
        value: QUERY.GT,
    },
    {
        label: 'Greater Than or Equal',
        value: QUERY.GTE,
    },
] as const;

export const BOOLEAN_OPERATORS = [
    {
        label: 'Exact',
        value: QUERY.EXACT,
    },
] as const;
export const DATA_TYPE_OPERATORS = [
    {
        label: 'Exact',
        value: QUERY.EXACT,
    },
] as const;

export const STRING_OPERATORS = [
    {
        label: 'Contains',
        value: QUERY.CONTAINS,
    },
    {
        label: 'Not Contains',
        value: QUERY.NOT_CONTAINS,
    },
    {
        label: 'Exact',
        value: QUERY.EXACT,
    },
    {
        label: 'Equal',
        value: QUERY.EQUAL,
    },
    {
        label: 'Not Equal',
        value: QUERY.NOT_EQUAL,
    },
    {
        label: 'In',
        value: QUERY.IN,
    },
    {
        label: 'Not In',
        value: QUERY.NOT_IN,
    },
    {
        label: 'Is Null',
        value: QUERY.IS_NULL,
    },
    {
        label: 'Is not Null',
        value: QUERY.IS_NON_NULL,
    },
    {
        label: 'Regular Expression',
        value: QUERY.REGEX,
    },
] as const;
export const DEFAULT_FILTER_OPERATORS = [
    {
        label: 'Contains',
        value: QUERY.CONTAINS,
    },
    {
        label: 'Not Contains',
        value: QUERY.NOT_CONTAINS,
    },
    {
        label: 'Not Equal',
        value: QUERY.NOT_EQUAL,
    },
    {
        label: 'Exact',
        value: QUERY.EXACT,
    },
    {
        label: 'In',
        value: QUERY.IN,
    },
    {
        label: 'Not In',
        value: QUERY.NOT_IN,
    },
    {
        label: 'Is Null',
        value: QUERY.IS_NULL,
    },
    {
        label: 'Is not Null',
        value: QUERY.IS_NON_NULL,
    },
] as const;

export const ARRAY_TYPE_OPERATORS = [
    { label: 'Contains', value: QUERY.CONTAINS },
    {
        label: 'Not Contains',
        value: QUERY.NOT_CONTAINS,
    },
    { label: 'In', value: QUERY.IN },
] as const;

export const OPERATORS_BY_TYPE = {
    [AttributeType.TEXT]: STRING_OPERATORS,
    [AttributeType.STRING]: STRING_OPERATORS,
    [AttributeType.BOOLEAN]: BOOLEAN_OPERATORS,
    [AttributeType.DATE]: NUMBER_OPERATORS,
    [AttributeType.DATE_TIME]: NUMBER_OPERATORS,
    [AttributeType.INTEGER]: NUMBER_OPERATORS,
    [AttributeType.FLOAT]: NUMBER_OPERATORS,
    [AttributeType.LONG]: NUMBER_OPERATORS,
    [AttributeType.STRING_ARRAY]: ARRAY_TYPE_OPERATORS,
    [AttributeType.DATE_ARRAY]: ARRAY_TYPE_OPERATORS,
    [AttributeType.INTEGER_ARRAY]: ARRAY_TYPE_OPERATORS,
    [AttributeType.FLOAT_ARRAY]: ARRAY_TYPE_OPERATORS,
    [AttributeType.DATE_TIME_ARRAY]: ARRAY_TYPE_OPERATORS,
} as const;

export const SEMANTIC_VERSION_VALUE = '{semantic-version}';
