/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const COMPONENT_NAME = {
    EDIT_METADATA: 'edit-metadata',
    ATTRIBUTE_IDENTIFIER: 'attr-identifier',
    CREATE_SUB_TYPE: 'create-subtype',
    ASSIGN_LIFECYCLE: 'assign-lifecycle',
    ASSIGN_ROLE: 'assign-role',
    APPLY_PROCESS: 'apply-process',
    ADD_EXISTING_ATTRIBUTE: 'add-existing-attribute',
    SCHEMA_ATTRIBUTE_DETAIL: 'schema-attribute-detail',
    CREATE_NEW_ATTRIBUTE: 'create-new-attribute',
    CHANGE_UNIQUE_KEY: 'change-unique-key',
    APPLY_CLASSIFICATION: 'apply-classification',
    ADD_RELATIONSHIP: 'add-relationship',
};
