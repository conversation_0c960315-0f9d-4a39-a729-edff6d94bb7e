/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    AttributeSchema,
    ILifeCycle,
    ILifeCycleDetail,
    ISchemaDetail,
    RelationType,
    SYSTEM_RELATION,
    UpdateSchemaRequest,
} from 'ui-common';
import { fetch, schemaUrls, processUrls } from '@tripudiotech/admin-api';
import { notifySuccess, notifyError } from '@tripudiotech/admin-styleguide';
import { getSchemaInfo } from '../utils/helper';

export const updateAttributeOrder = async (detailSchema: ISchemaDetail, newOrder: string[]) => {
    try {
        const { entityType } = detailSchema;
        const request = {
            attributeOrder: newOrder,
            name: entityType.name,
            description: entityType.description,
            displayName: entityType.displayName,
            isExtendable: entityType.extendable,
            isAbstract: entityType.abstract,
            uniqueKeys: entityType.uniqueKeys,
        };

        const res = await updateEntityType(request);
        return res;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const updateEntityType = async (request: UpdateSchemaRequest) => {
    try {
        await fetch({
            ...schemaUrls.updateEntityType,
            params: { entityTypeName: request.name },
            data: request,
            successMessage: `Successfully updated schema information`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    }
};

export const applyClassification = async (entityType: string, data) => {
    try {
        await fetch({
            ...schemaUrls.applyClassification,
            data,
            params: {
                entityType,
            },
            successMessage: `Successfully applied classifications for <b>${entityType}</b>`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    }
};

export const deleteIdentifier = async (entityType, attributeName) => {
    try {
        await fetch({
            ...schemaUrls.deleteAttributeIdentifier,
            params: { entityType, attributeName },
            successMessage: `Auto generated value for attribute <b>${attributeName}</b> has been deleted successfully.`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const deleteAttribute = async (schema: ISchemaDetail, attribute: AttributeSchema) => {
    try {
        await fetch({
            ...schemaUrls.deleteEntityAttribute,
            params: { entityTypeName: getSchemaInfo(schema, 'name'), attributeName: attribute.name },
            successMessage: `Successfully delete attribute <b>${
                attribute?.displayName
            }</b> on the entity type <b>${getSchemaInfo(schema, 'displayName')}</b>`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const createIdentifier = async (entityType, attributeName, values) => {
    try {
        await fetch({
            ...schemaUrls.createAttributeIdentifier,
            params: { entityType, attributeName },
            data: values,
            successMessage: `Auto generated value for attribute <b>${attributeName}</b> has been created successfully.`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const createRelationAttribute = async (relationType: RelationType, attributes: AttributeSchema[]) => {
    try {
        await fetch({
            ...schemaUrls.createRelationAttribute,
            params: {
                fromEntityTypeName: relationType.fromEntityType,
                relationName: relationType.name,
                toEntityTypeName: relationType.toEntityType,
            },
            data: attributes,
            successMessage: `Successfully added attributes for relation ${relationType.displayName}`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const updateRelationAttribute = async (relationType: RelationType, attributeName: string, attribute: AttributeSchema) => {
    try {
        await fetch({
            ...schemaUrls.updateRelationAttribute,
            params: {
                fromEntityTypeName: relationType.fromEntityType,
                relationName: relationType.name,
                toEntityTypeName: relationType.toEntityType,
                attributeName,
            },
            data: attribute,
            successMessage: `Successfully updated attribute <b>${attribute.displayName}</b> on the relation ${relationType.displayName}`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
}

export const deleteRelation = async (relationType: RelationType) => {
    try {
        await fetch({
            ...schemaUrls.deleteRelation,
            params: {
                fromEntityTypeName: relationType.fromEntityType,
                relationName: relationType.name,
                toEntityTypeName: relationType.toEntityType,
            },
            successMessage: `Successfully removed relation <b>${relationType.displayName}</b>`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const createRelation = async (relations: any) => {
    try {
        await Promise.all(
            relations.map((relation) =>
                fetch({
                    ...schemaUrls.createRelation,
                    data: relation,
                })
            )
        );
        notifySuccess(`Success created new relationships`);
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const updateRelation = async (fromEntityTypeName, relationName, toEntityTypeName, relation: any) => {
    try {
        await fetch({
            ...schemaUrls.updateRelation,
            data: relation,
            params: {
                fromEntityTypeName,
                relationName,
                toEntityTypeName,
            },
            successMessage: `Successfully updated relation <b>${relation.displayName}</b>`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const deleteRelationAttribute = async (relationType: RelationType, attribute: AttributeSchema) => {
    try {
        await fetch({
            ...schemaUrls.unassignRelationAttribute,
            params: {
                fromEntityTypeName: relationType.fromEntityType,
                relationName: relationType.name,
                toEntityTypeName: relationType.toEntityType,
                attributeName: attribute.name,
            },
            data: attribute.name,
            successMessage: `Successfully removed attribute <b>${attribute.displayName}</b> on the relation ${relationType.displayName}`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const updateIdentifier = async (entityType, attributeName, values) => {
    try {
        await fetch({
            ...schemaUrls.updateAttributeIdentifier,
            params: { entityType, attributeName },
            data: values,
            successMessage: `Auto generated value for attribute <b>${attributeName}</b> has been updated successfully.`,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const createEntitySubType = async (
    parentEntityType: string,
    request,
    notify = true
): Promise<{ error: boolean; data: any | null }> => {
    try {
        const { data } = await fetch({
            ...schemaUrls.createEntitySubType,
            params: { entityTypeName: parentEntityType },
            data: request,
            successMessage: notify ? `Successfully created new Entity Type as sub type of ${parentEntityType}.` : null,
        });
        return {
            error: false,
            data,
        };
    } catch {
        return {
            error: true,
            data: null,
        };
    } finally {
    }
};

export const updateEntityTypeAttribute = async (schemaName, attributeName, attributes) => {
    try {
        await fetch({
            ...schemaUrls.updateAttribute,
            params: { schemaName, attributeName },
            data: attributes,
            successMessage: `Successfully updated attribute ${attributeName}`,
        });
        return true;
    } catch (e) {
        console.error(e);
        return false;
    }
};

export const createEntityTypeAttribute = async (entityTypeName, attribute) => {
    try {
        await fetch({
            ...schemaUrls.createEntityAttribute,
            params: { entityTypeName },
            data: [attribute],
            successMessage: `Successfully updated attribute ${attribute.displayName}`,
        });
        return true;
    } catch (e) {
        console.error(e);
        return false;
    }
};

export const createEntityAttribute = async (schemaDetail: ISchemaDetail, attributes) => {
    try {
        await fetch({
            ...schemaUrls.createEntityAttribute,
            params: {
                entityTypeName: schemaDetail.entityType.name,
            },
            data: attributes,
            successMessage: `Successfully added attributes to the entity Type <b>${schemaDetail.entityType.displayName}</b>`,
        });
        return true;
    } catch (err) {
        console.error(err);
        return false;
    } finally {
    }
};

const createEntityTypesWithTransaction = async (entityTypes: { parentType: string; data: ISchemaDetail }[]) => {
    const responses = await Promise.all(
        entityTypes.map(({ parentType, data }) => createEntitySubType(parentType, data, false))
    );

    const hasError = responses.some(({ error }) => error);
    if (hasError) {
        let rollbackRequests = [];
        responses.forEach(({ error, data }) => {
            if (!error) {
                rollbackRequests.push(
                    fetch({
                        ...schemaUrls.deleteEntityType,
                        params: { entityTypeName: data.name },
                    })
                );
            }
        });

        await Promise.all(rollbackRequests);

        return {
            error: true,
            data: null,
        };
    }
    return {
        error: false,
        data: responses.map((response) => response.data),
    };
};

export const updateDefaultLifecycle = async (entityTypeName: string, lifecycleId: string) => {
    try {
        await fetch({
            ...schemaUrls.updateDefaultLifecycle,
            params: { entityTypeName, lifecycleId },
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const applyProcessToEntityType = async (objectId: string, process: any, data) => {
    try {
        await fetch({
            ...processUrls.applyProcess,
            params: {
                processId: process.id,
                objectId: objectId,
            },
            data,
        });
        return true;
    } catch (error) {
        console.error(error);
        return false;
    } finally {
    }
};

export const revokeProcess = async (schemaId: string, processList: any[]) => {
    try {
        await Promise.all(
            processList.map((process) =>
                fetch({
                    ...processUrls.revokeProcess,
                    params: {
                        objectId: schemaId,
                        processId: process.id,
                    },
                    skipToast: true,
                })
            )
        );
        notifySuccess('Successfully revoked selected process');
        return true;
    } catch (err) {
        console.error(err);
        notifyError('An error occurred while revoking selected process');
        return false;
    }
};

export const assignLifeCycles = async (entityTypeName: string, lifecycles: ILifeCycle[]) => {
    try {
        await fetch({
            ...schemaUrls.assignLifeCycles,
            params: { entityTypeName },
            data: {
                lifeCycles: lifecycles.map((lf) => lf.id),
            },
            successMessage: `Successfully assigned selected lifecycles`,
        });
        return true;
    } catch (err) {
        console.error(err);
        return false;
    }
};

export const unassignLifeCycles = async (entityTypeName: string, lifecycles: ILifeCycleDetail[]) => {
    try {
        await fetch({
            ...schemaUrls.unassignLifeCycles,
            params: { entityTypeName },
            data: {
                lifeCycles: lifecycles.map((lf) => lf.lifeCycle.id),
            },
            successMessage: `Successfully unassigned selected lifecycles`,
        });
        return true;
    } catch (err) {
        return false;
    }
};

export const assignRoles = async (
    entityTypeName: string,
    roleIds: string[],
    successMessage?: string,
    errorMessage?: string
) => {
    try {
        await Promise.all(
            roleIds.map((roleId) =>
                fetch({
                    ...schemaUrls.assignRole,
                    params: { entityType: entityTypeName, roleId },
                })
            )
        );

        notifySuccess(successMessage || 'Successfully assign selected roles');
        return true;
    } catch (err) {
        console.error(err);
        notifyError(errorMessage || 'An error occurred while assigning selected roles');
        return false;
    }
};

export const unassignRoles = async (
    entityTypeName: string,
    roleIds: string[],
    successMessage?: string,
    errorMessage?: string
) => {
    try {
        await Promise.all(
            roleIds.map((roleId) =>
                fetch({
                    ...schemaUrls.unassignRole,
                    params: { entityType: entityTypeName, roleId },
                })
            )
        );

        notifySuccess(successMessage || 'Successfully unassign selected roles');
        return true;
    } catch (err) {
        console.error(err);
        notifyError(errorMessage || 'An error occurred while assigning selected roles');
        return false;
    }
};
export const createEntityTypeRelation = async (relation) => {
    try {
        const { data } = await fetch({
            ...schemaUrls.createRelation,
            data: relation,
        });
        return {
            data,
            error: false,
        };
    } catch (err) {
        console.error(err);
        return {
            error: true,
        };
    }
};

export const deleteEntityType = async (entityTypeName: string) => {
    try {
        await fetch({
            ...schemaUrls.deleteEntityType,
            params: { entityTypeName },
            successMessage: `Entity <b>${entityTypeName}</b> has been deleted successfully`,
        });
        return {
            error: false,
        };
    } catch (err) {
        console.error(err);
        return {
            error: true,
        };
    }
};

export const createEntityTypeWithMaster = async (
    parentEntityName: string,
    entityType: any,
    parentMasterEntityName: string,
    entityTypeMaster: any
) => {
    try {
        const { error, data } = await createEntityTypesWithTransaction([
            {
                parentType: parentEntityName,
                data: entityType,
            },
            {
                parentType: parentMasterEntityName,
                data: entityTypeMaster,
            },
        ]);

        if (!error) {
            const relation = {
                fromEntityTypeName: entityType.name,
                toEntityTypeName: entityTypeMaster.name,
                name: SYSTEM_RELATION.HAS_MASTER,
                isRequired: true,
                isSystem: true,
            };
            const { error } = await createEntityTypeRelation(relation);
            if (error) {
                await fetch({
                    ...schemaUrls.deleteEntityType,
                    params: { entityTypeName: entityType.name },
                });
                await fetch({
                    ...schemaUrls.deleteEntityType,
                    params: { entityTypeName: entityTypeMaster.name },
                });
            }
        }
        return {
            error: false,
            data,
        };
    } catch {
        return {
            error: true,
            data: null,
        };
    } finally {
    }
};
