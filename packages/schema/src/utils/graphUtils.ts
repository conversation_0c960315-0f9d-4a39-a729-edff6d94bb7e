/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import dagre from 'dagre';
import { RelationType } from 'ui-common';

export const getLayoutedElements = (nodes, edges, direction = 'LR', nodeWidth = 220, nodeHeight = 100) => {
    const dagreGraph = new dagre.graphlib.Graph();
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    const isHorizontal = direction === 'LR';
    dagreGraph.setGraph({
        rankdir: direction,
        nodesep: 100,
        edgesep: 50,
        ranksep: 50,
    });

    nodes.forEach((node) => {
        dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight });
    });

    edges.forEach((edge) => {
        dagreGraph.setEdge(edge.source, edge.target);
    });

    dagre.layout(dagreGraph);

    nodes.forEach((node) => {
        const nodeWithPosition = dagreGraph.node(node.id);
        node.targetPosition = isHorizontal ? 'left' : 'top';
        node.sourcePosition = isHorizontal ? 'right' : 'bottom';

        // We are shifting the dagre node position (anchor=center center) to the top left
        // so it matches the React Flow node anchor point (top left).
        node.position = {
            x: nodeWithPosition.x - nodeWidth / 2,
            y: nodeWithPosition.y - nodeHeight / 2,
        };

        return node;
    });

    return { nodes, edges };
};

const isOutgoingRelation = (relationType: RelationType): boolean => relationType.direction === 'Outgoing';

export const createEdgesAndNodesFromRelationTypes = (relationType: RelationType[]) => {
    let entityTypes = new Set<String>([]);
    let nodes = [];
    let edges = [];
    let reverse = {};
    relationType.forEach((relationType) => {
        const isOutgoing = isOutgoingRelation(relationType);
        entityTypes.add(relationType.fromEntityType);
        entityTypes.add(relationType.toEntityType);
        reverse[`${relationType.toEntityType}-${relationType.fromEntityType}`] = true;
        edges.push({
            id: `${relationType.fromEntityType}-${relationType.name}-${relationType.toEntityType}`,
            source: relationType.fromEntityType,
            target: relationType.toEntityType,
            data: {
                ...relationType,
                reversed: reverse[`${relationType.fromEntityType}-${relationType.toEntityType}`],
            },
            label: relationType.displayName,
            markerEnd: { type: 'arrow' },
            type: 'orthogonalEdge',
            zIndex: 1,
        });
    });
    entityTypes.forEach((entityType) => {
        nodes.push({
            id: entityType,
            data: {
                name: entityType,
                displayName: entityType,
                label: entityType,
            },
            position: { x: 0, y: 0 },
            width: 200,
            height: 100,
            type: 'baseNode',
        });
    });
    return { nodes, edges };
};
export const calculatePathfindingPath = (sourceX, sourceY, targetX, targetY, nodes, direction = 'LR') => {
    const gridSize = 10; // 10px per grid cell

    // Create a grid that maps the canvas (initially an empty grid)
    const grid = createGrid(nodes, gridSize);

    // Convert source and target positions into grid coordinates
    const start = {
        x: Math.floor(sourceX / gridSize),
        y: Math.floor(sourceY / gridSize),
    };
    const end = {
        x: Math.floor(targetX / gridSize),
        y: Math.floor(targetY / gridSize),
    };

    // Temporarily return a simple straight line path (no A* yet)
    let path = `M${sourceX},${sourceY} L${targetX},${targetY}`;

    // Run A* algorithm to find the shortest path (commenting out for now)
    const aStarPath = aStar(grid, start, end);

    if (aStarPath.length > 0) {
        // Convert the A* path into canvas coordinates
        const canvasPath = aStarPath.map((point) => ({
            x: point.x * gridSize,
            y: point.y * gridSize,
        }));

        // Create an SVG path string (use 'L' for straight lines)
        path = canvasPath.reduce(
            (acc, point, index) => acc + (index === 0 ? `M${point.x},${point.y}` : ` L${point.x},${point.y}`),
            ''
        );
    }

    return path;
};

// Helper function to create a grid
const createGrid = (nodes, gridSize) => {
    const gridWidth = 100; // Assuming 1000px wide canvas (adjust as needed)
    const gridHeight = 100; // Assuming 1000px tall canvas (adjust as needed)
    const grid = Array(gridWidth)
        .fill(null)
        .map(() => Array(gridHeight).fill(0)); // Empty grid

    // Mark nodes as blocked on the grid
    nodes.forEach((node) => {
        const {
            position: { x, y },
            width,
            height,
        } = node;
        const nodeX1 = Math.floor(x / gridSize);
        const nodeY1 = Math.floor(y / gridSize);
        const nodeX2 = Math.floor((x + width) / gridSize);
        const nodeY2 = Math.floor((y + height) / gridSize);

        for (let i = nodeX1; i <= nodeX2; i++) {
            for (let j = nodeY1; j <= nodeY2; j++) {
                grid[i][j] = 1; // Mark node's grid cells as blocked
            }
        }
    });

    return grid;
};

// A* Pathfinding Algorithm (same as earlier but simplified)
const aStar = (grid, start, end) => {
    const openList = [start];
    const closedList = [];
    const cost = { [key(start)]: 0 };
    const prev = {};

    const heuristic = (pos0, pos1) => Math.abs(pos1.x - pos0.x) + Math.abs(pos1.y - pos0.y);

    while (openList.length > 0) {
        let current = openList[0];
        openList.forEach((node) => {
            if (cost[key(node)] + heuristic(node, end) < cost[key(current)] + heuristic(current, end)) {
                current = node;
            }
        });

        if (current.x === end.x && current.y === end.y) {
            return reconstructPath(prev, current);
        }

        openList.splice(openList.indexOf(current), 1);
        closedList.push(current);

        getNeighbors(current, grid).forEach((neighbor) => {
            if (closedList.some((closed) => closed.x === neighbor.x && closed.y === neighbor.y)) {
                return;
            }

            const tentativeCost = cost[key(current)] + 1;
            const existingCost = cost[key(neighbor)] || Infinity;

            if (tentativeCost < existingCost) {
                cost[key(neighbor)] = tentativeCost;
                prev[key(neighbor)] = current;

                if (!openList.some((open) => open.x === neighbor.x && open.y === neighbor.y)) {
                    openList.push(neighbor);
                }
            }
        });
    }

    return [];
};

const key = (pos) => `${pos.x},${pos.y}`;

const reconstructPath = (prev, node) => {
    const path = [node];
    while (prev[key(node)]) {
        node = prev[key(node)];
        path.push(node);
    }
    return path.reverse(); // Reverse path from start to end
};

const getNeighbors = (node, grid) => {
    const neighbors = [];
    const directions = [
        { x: -1, y: 0 },
        { x: 1, y: 0 }, // Left, Right
        { x: 0, y: -1 },
        { x: 0, y: 1 }, // Up, Down
    ];

    directions.forEach((dir) => {
        const neighborX = node.x + dir.x;
        const neighborY = node.y + dir.y;

        if (grid[neighborX] && grid[neighborX][neighborY] === 0) {
            neighbors.push({ x: neighborX, y: neighborY });
        }
    });

    return neighbors;
};

// Helper function to calculate orthogonal waypoints
export const calculateOrthogonalPath = (sourceX, sourceY, targetX, targetY, nodes) => {
    const offsetX = 50; // Horizontal offset for boxy paths
    const offsetY = 40; // Vertical offset to avoid overlap

    let midX1, midX2, midY;

    // Determine direction based on node positions
    const isLeftToRight = sourceX < targetX;

    let labelX, labelY;

    // For Left-to-Right paths (using a step path)
    if (isLeftToRight) {
        // Midpoints for orthogonal path (can be adjusted to avoid nodes)
        let midX = (sourceX + targetX) / 2;
        let midY = (sourceY + targetY) / 2;

        // Check for node collisions and adjust path
        nodes.forEach((node) => {
            const {
                position: { x, y },
                width,
                height,
            } = node;
            const nodeBox = { x1: x, x2: x + width, y1: y, y2: y + height };

            // If the midpoint falls within the node's bounding box, adjust it
            if (midX > nodeBox.x1 && midX < nodeBox.x2 && midY > nodeBox.y1 && midY < nodeBox.y2) {
                midX = nodeBox.x2 + 20; // Shift horizontally
                midY = nodeBox.y2 + 20; // Shift vertically
            }
        });

        // Set the label coordinates at the midpoint of the path
        labelX = midX;
        labelY = (sourceY + targetY) / 2;

        // Create an orthogonal path with two segments
        const path = `M${sourceX},${sourceY} L${midX},${sourceY} L${midX},${targetY} L${targetX},${targetY}`;
        return { path, labelX, labelY };
    }

    // For Right-to-Left paths, create a boxy path
    else {
        // First horizontal segment to the right
        midX1 = sourceX + offsetX;

        // Vertical segment to avoid collision
        midY = sourceY < targetY ? targetY - offsetY : targetY + offsetY;

        // Second horizontal segment to the left of the target node
        midX2 = targetX - offsetX;

        // Check for collisions and adjust midpoints dynamically
        nodes.forEach((node) => {
            const { x, y, width, height } = node.position;
            const nodeBox = { x1: x, x2: x + width, y1: y, y2: y + height };

            // Adjust Y if the path overlaps a node
            if (midX1 > nodeBox.x1 && midX1 < nodeBox.x2 && sourceY > nodeBox.y1 && sourceY < nodeBox.y2) {
                midY = sourceY < nodeBox.y1 ? nodeBox.y1 - offsetY : nodeBox.y2 + offsetY;
            }
        });

        // Set the label coordinates at the midpoint of the path
        labelX = (midX1 + midX2) / 2;
        labelY = midY;

        // Generate the boxy path with horizontal and vertical segments, connecting horizontally to the handle
        const path = `M${sourceX},${sourceY} L${midX1},${sourceY} L${midX1},${midY} L${midX2},${midY} L${midX2},${targetY} L${targetX},${targetY}`;
        return { path, labelX, labelY };
    }
};
