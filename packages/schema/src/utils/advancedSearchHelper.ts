/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { FormikProps } from 'formik';
import get from 'lodash/get';
import { AttributeType, convertQuery, QUERY, SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';
import keys from 'lodash/keys';
import {
    AdvancedFilterFormProps,
    Condition,
    FilterType,
    RelationRowData,
} from '../components/advanced-search/FilterComponents';
import { v4 as uuidv4 } from 'uuid';
import { SchemaWithLifeCycleDetail, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { INITIAL_CONDITION } from '../constants';
import { AttributeSchema, RelationType } from 'ui-common';

export const isLifecycleStateFilterWithInputValue = (filterType: any, filterField: any, filterOperator: any) => {
    return (
        filterType === 'lifecycleState' && filterField && ![QUERY.IS_NON_NULL, QUERY.IS_NULL].includes(filterOperator)
    );
};

export const isLifecycleStateAttributeSelected = (filterType: any, filterField: any) => {
    return filterType === 'lifecycleState' && filterField;
};

export const isLifecycleStateFilter = (filterType: FilterType) => {
    return filterType === 'lifecycleState';
};

export const isRelationFilter = (filterType: FilterType) => {
    return filterType === 'relation';
};

export const isClassificationFilter = (filterType: FilterType) => {
    return filterType === 'classification';
};

export const isFilterWithInputValue = (
    filterType: FilterType,
    filterField: any,
    attributes: Record<string, AttributeSchema>,
    filterOperator: any
) => {
    return (
        ['attribute', 'classification', 'relationAttribute'].includes(filterType) &&
        filterField &&
        attributes?.[filterField] &&
        ![QUERY.IS_NON_NULL, QUERY.IS_NULL].includes(filterOperator)
    );
};

export const isAttributeSelected = (
    filterType: FilterType,
    filterField: any,
    attributes: Record<string, AttributeSchema>
) => {
    return (
        ['attribute', 'classification', 'relationAttribute'].includes(filterType) &&
        filterField &&
        attributes?.[filterField]
    );
};

export const isAttributeFilter = (filterType: FilterType) => {
    return filterType === 'attribute';
};
export const isRelationAttributeFilter = (filterType: FilterType) => filterType === 'relationAttribute';

export const isError = (formProps: FormikProps<AdvancedFilterFormProps>, name: string) =>
    get(formProps.touched, name) &&
    Boolean(get(formProps.errors, name)) &&
    typeof get(formProps.errors, name) === 'string';

export const getDataType = (attributeSchema: AttributeSchema): string => {
    return get(attributeSchema, ['constraint', 'dataType']);
};

export const getEnumRange = (attributeSchema: AttributeSchema): string[] => {
    return get(attributeSchema, ['constraint', 'enumRange']);
};
export const convertFormValuesToQuery = (condition) => {
    if ([QUERY.AND, QUERY.OR].includes(condition.operator)) {
        return {
            [condition.operator]: condition.values.map((value) => convertFormValuesToQuery(value)),
        };
    }
    if ([QUERY.IN, QUERY.NOT_IN].includes(condition.operator)) {
        return {
            [condition.operator]: {
                [condition.values?.field]: Array.isArray(condition.values?.value)
                    ? condition.values?.value
                    : condition.values?.value?.split(',') || [],
            },
        };
    }
    if ([QUERY.IS_NON_NULL, QUERY.IS_NULL].includes(condition.operator)) {
        return {
            [condition.operator]: condition.values?.field,
        };
    }
    return {
        [condition.operator]: {
            [condition.values?.field]: condition?.values?.value,
        },
    };
};

const isRelationCriteria = (values) => {
    if (values?.length > 0) {
        const firstItem = values[0];
        const firstItemKey: any = keys(firstItem)?.[0];
        if ([QUERY.AND, QUERY.OR].includes(firstItemKey)) {
            return isRelationCriteria(firstItem[firstItemKey]);
        }
        if ([QUERY.IS_NULL, QUERY.IS_NON_NULL].includes(firstItemKey)) {
            return firstItem[firstItemKey]?.startsWith('relation');
        }
        const field = keys(firstItem[firstItemKey])?.[0];
        return field && field?.startsWith('relation.');
    }
    return false;
};
export const getRelationQuery = (values) => {
    if (values?.length > 0) {
        const firstItem = values[0];
        if ([QUERY.AND, QUERY.OR].includes(firstItem?.operator)) {
            return getRelationQuery(firstItem?.values);
        }
        const field = firstItem?.values?.field;
        return field && field?.startsWith('relation.') ? field.replace('relationAttr:', '') : '';
    }
    if ([QUERY.AND, QUERY.OR].includes(values?.operator)) {
        return getRelationQuery(values?.values);
    }
    return '';
};

export const extractRelationsFromRelationFilter = (relationQuery: string): { name: string; entityType: string }[] => {
    if (relationQuery) {
        const queryParts: string[] = relationQuery.split('.');
        let relations = [];
        queryParts.forEach((queryPart: string, index) => {
            if (index === queryParts.length - 1 || index === 0 || queryPart === 'relation') return;

            if (queryPart.includes(':')) {
                const [name, entityType] = queryPart.split(':');
                relations.push({
                    name,
                    entityType,
                });
            } else {
                relations.push({
                    name: queryPart,
                    entityType: SYSTEM_ENTITY_TYPE.SYS_ROOT,
                });
            }
        });
        return relations;
    }
    return [];
};

export const getRelationFilterAsText = (values) => {
    if (!values) return 'Create relation filter';
    const relationQuery = getRelationQuery(values);
    const relationTypes = extractRelationsFromRelationFilter(relationQuery);
    if (!relationTypes?.length) return 'Create relation filter';

    const relWithoutAttribute = relationQuery.replace(/\.[^.]+$/, '');

    const query = JSON.stringify(convertFormValuesToQuery(values)).replace(
        new RegExp(`${relWithoutAttribute}.`, 'g'),
        ''
    );

    if (relationQuery.endsWith('.id')) {
        return `Relation: ${relationTypes.map((relation) => relation.name).join(' > ')} ${
            values?.values?.[0]?.operator === QUERY.IS_NON_NULL ? 'EXISTS' : 'NOT EXISTS'
        }`;
    }

    const representation = convertQuery(query);

    return `Relation: ${relationTypes.map((relation) => relation.name).join(' > ')} AND${representation}`.replace(
        'relationAttr:',
        'Relation Attribute: '
    );
};

export const buildRelationTreeData = async (
    schemaDetail: SchemaWithLifeCycleDetail,
    relations
): Promise<RelationRowData[]> => {
    let parent: RelationRowData = {
        id: schemaDetail.entityType.name,
        name: schemaDetail.entityType.name,
        displayName: schemaDetail.entityType.displayName,
        entityType: schemaDetail.entityType.name,
        path: [schemaDetail.entityType.name],
        isLoaded: true,
        relationQuery: '',
    };
    let data: RelationRowData[] = [{ ...parent }];
    const startIndex = { value: 0 }; // Use an object to keep track of the index

    await recursivelyExpandRelationTypes(data, schemaDetail?.relationTypes || [], startIndex, relations, parent);
    return data;
};

const recursivelyExpandRelationTypes = async (
    rowData: RelationRowData[],
    relationTypes: RelationType[],
    startIndex: { value: number },
    relations,
    parent
) => {
    for (const relationType of relationTypes) {
        const rowId = uuidv4();
        const relEntityType =
            relationType.direction === 'Incoming' ? relationType.fromEntityType : relationType.toEntityType;

        if (
            startIndex.value >= relations.length ||
            relationType.name !== relations[startIndex.value].name ||
            relations[startIndex.value].entityType !== relEntityType
        ) {
            rowData.push({
                id: rowId,
                name: relationType.name,
                displayName: relationType.displayName,
                entityType: relEntityType,
                path: [...parent.path, rowId],
                isLoaded: false,
                relationQuery: `${parent.relationQuery}${parent.relationQuery ? '.' : ''}relation.${
                    relationType.name
                }:${relEntityType}`,
                fromEntityType: relationType.fromEntityType,
                toEntityType: relationType.toEntityType,
            });
        } else {
            const subRelations = await useSchemaDetail.getState().getSchema(relEntityType, true);
            const newParent = {
                id: rowId,
                name: relationType.name,
                displayName: relationType.displayName,
                entityType: relEntityType,
                path: [...parent.path, rowId],
                isLoaded: true,
                relationQuery: `${parent.relationQuery}${parent.relationQuery ? '.' : ''}relation.${
                    relationType.name
                }:${relEntityType}`,
                fromEntityType: relationType.fromEntityType,
                toEntityType: relationType.toEntityType,
            };
            rowData.push({ ...newParent });
            startIndex.value++;

            await recursivelyExpandRelationTypes(
                rowData,
                subRelations?.relationTypes || [],
                startIndex,
                relations,
                newParent
            );
        }
    }
};
export const extractClassificaitonField = (field: string | null): { classificationId: string; attribute: string } => {
    const parts = field?.split('.');
    return {
        classificationId: parts?.[1] || '',
        attribute: parts?.[2] || '',
    };
};

export const getFilterTypeFromSearchCriteria = (searchCriteria): FilterType => {
    const key: any = keys(searchCriteria)?.[0];
    const value = searchCriteria?.[key];

    if ([QUERY.AND, QUERY.OR].includes(key)) {
        return isRelationCriteria(value) ? 'relation' : 'group';
    }

    const field = [QUERY.IS_NON_NULL, QUERY.IS_NULL].includes(key) ? searchCriteria[key] : keys(value)?.[0];
    if (field.startsWith('state')) {
        return 'lifecycleState';
    }
    if (field.startsWith('classification')) {
        return 'classification';
    }
    if (field.startsWith('relation') && field.endsWith('.id')) {
        return 'relationExists';
    }
    if (field.includes('.relationAttr')) {
        return 'relationAttribute';
    }
    return 'attribute';
};
export const convertSearchCriteriaToFormValues = (searchCriteria): Condition => {
    try {
        const key: any = keys(searchCriteria)?.[0];
        const filterType = getFilterTypeFromSearchCriteria(searchCriteria);
        if (key) {
            const value = searchCriteria[key];
            // Group Filter can be Relation Filter as Design
            if ([QUERY.AND, QUERY.OR].includes(key)) {
                return {
                    operator: key,
                    values: value?.map((singleValue) => convertSearchCriteriaToFormValues(singleValue)),
                    filterType,
                };
            }
            if ([QUERY.IN, QUERY.NOT_IN].includes(key)) {
                const field = keys(value)?.[0];
                const fieldValue = value[field];
                return {
                    operator: key,
                    filterType,
                    values: {
                        field: field,
                        value: fieldValue,
                        classification:
                            filterType === 'classification' ? extractClassificaitonField(field)?.classificationId : '',
                    },
                };
            }
            if ([QUERY.IS_NON_NULL, QUERY.IS_NULL].includes(key)) {
                const field = searchCriteria[key];
                return {
                    operator: key,
                    filterType,
                    values: {
                        field,
                        value: null,
                        classification:
                            filterType === 'classification' ? extractClassificaitonField(field)?.classificationId : '',
                    },
                };
            }
            const field = keys(value)?.[0];
            const fieldValue = value[field];
            return {
                operator: key,
                filterType,
                values: {
                    field: field,
                    value: fieldValue,
                    classification:
                        filterType === 'classification' ? extractClassificaitonField(field)?.classificationId : '',
                },
            };
        }
    } catch (err) {
        console.error(err);
        // Return the initial condition if the conversion fai
        return INITIAL_CONDITION;
    }
};

export const isGroupFilter = (valueObject) => {
    return [QUERY.AND, QUERY.OR].includes(valueObject?.operator) && valueObject?.filterType !== 'relation';
};

export const isArrayAttribute = (type: AttributeType) =>
    [
        AttributeType.STRING_ARRAY,
        AttributeType.DATE_ARRAY,
        AttributeType.DATE_TIME_ARRAY,
        AttributeType.INTEGER_ARRAY,
        AttributeType.FLOAT_ARRAY,
        AttributeType.BOOLEAN_ARRAY,
    ].includes(type);

export const extractSearchCriteriaWithoutType = (searchCriteria: any): any => {
    const criteria = typeof searchCriteria === 'string' ? JSON.parse(searchCriteria) : searchCriteria;
    const query = criteria?.[QUERY.AND]?.[1] || criteria;

    if (query?.[QUERY.AND]) {
        return query;
    }
    return {
        [QUERY.AND]: [query],
    };
};
