/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo } from 'react';
import { AnimatedPage, Loading, MoreIcon, styled, Typography, TabGroup, MainMenuList, DeleteIcon } from 'ui-style';
import { IDialog, ISchemaDetail, schema<PERSON><PERSON>per, SY<PERSON>EM_ENTITY_TYPE, SYSTEM_RELATION } from 'ui-common';
import { useSchemaDetail, useSchemaTree, useDialog } from '@tripudiotech/admin-caching-store';
import { Outlet, useMatch, useNavigate, useParams } from 'react-router-dom';
import get from 'lodash/get';
import { SCHEMA_DETAIL_TABS } from '../constants/tabs';
import CreateSubTypeAction from '../components/actions/CreateSubTypeAction';
import { expandEntityType, getSchemaInfo } from '../utils/helper';
import useLocalStore from '../store/useLocalStore';
import { deleteEntityType } from '../actions';

const SchemaDetailContainer = styled('div')(({ theme }) => {
    const glideTheme = theme.palette.glide;
    return {
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        '& .headerContainer': {
            '& .primaryContent': {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
            },
            padding: '16px',
            borderBottom: `1px solid ${glideTheme.stroke.normal.primary}`,
            '& .subActions': {
                display: 'flex',
                gap: '8px',
                '& .moreBtn': {
                    backgroundColor: glideTheme.background.normal.blue2,
                },
            },
        },
        '& .mainContent': {
            height: '100%',
        },
    };
});

const SchemaDetail = () => {
    const { schemaName } = useParams();
    const [detailSchema, getSchema]: [
        ISchemaDetail,
        (entityName: string, withLifecycle?: boolean, signal?: any) => Promise<ISchemaDetail>
    ] = useSchemaDetail((state) => [state.schema[schemaName], state.getSchema]);
    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const [setIsLoading, clearLocalStore] = useLocalStore((state) => [state.setIsLoading, state.clearStore]);

    const [isLoadedSchemaTree, getSchemaTree, schemaTreeMap] = useSchemaTree((state) => [
        state.isLoaded,
        state.getSchemaTree,
        state.schemaTreeMap,
    ]);

    const hasSubTypes = useMemo(() => {
        return Object.values(schemaTreeMap).some(
            (schema: any) => schema.path.includes(schemaName) && schemaName !== schema.name
        );
    }, [schemaTreeMap, schemaName]);

    const matched = useMatch('schema/:schemaName/:view/*');
    const navigate = useNavigate();
    const view = get(matched, ['params', 'view']);
    const isSystem = detailSchema?.entityType?.system;

    useEffect(() => {
        if (schemaName) {
            getSchema(schemaName);
        }
        return () => {
            clearLocalStore();
        };
    }, [schemaName]);

    const handleDelete = useCallback(async () => {
        setIsLoading(true);
        const { error } = await deleteEntityType(schemaName);
        if (!error) {
            navigate('../');
            await getSchemaTree(true);
        }
        setIsLoading(false);
    }, [schemaName]);

    const onDelete = useCallback(() => {
        const isMaster = get(detailSchema, 'schemaName', []).includes(SYSTEM_ENTITY_TYPE.ENTITY_MASTER);
        const isRevision = Boolean(schemaHelper.getOutgoingRelation(detailSchema, SYSTEM_RELATION.HAS_MASTER));

        const warning =
            isMaster || isRevision
                ? `Delete a <b>${isMaster ? 'Master Entity Type' : 'Revision Entity Type'}</b> will delete the <b>${
                      !isMaster ? 'Master Entity Type' : 'Revision Entity Type'
                  }</b> associated with it as well. `
                : '';
        onOpenDialog(
            'Delete entity type',
            `${warning}
            <div style="margin-top:16px">Do you really want to delete <b>${getSchemaInfo(
                detailSchema,
                'displayName'
            )} (${getSchemaInfo(detailSchema, 'name')})</b>?</div>`,
            handleDelete,
            'error'
        );
    }, [detailSchema]);

    useEffect(() => {
        const schemaName = detailSchema?.entityType?.name;
        if (schemaName) {
            expandEntityType(schemaName);
        }
    }, [detailSchema]);

    return detailSchema ? (
        <AnimatedPage style={{ height: '100%' }}>
            <SchemaDetailContainer id="schema-detail">
                <div className="headerContainer">
                    <div className="primaryContent">
                        <div className="info">
                            <Typography variant="label1-sem">
                                {get(detailSchema, ['entityType', 'displayName'])}
                            </Typography>
                        </div>
                        <div className="subActions">
                            <CreateSubTypeAction detailSchema={detailSchema} />

                            <MainMenuList
                                btnIcon={<MoreIcon sx={{ transform: 'rotate(90deg)' }} />}
                                items={[
                                    {
                                        id: 'delete-menu-item',
                                        label: 'Delete',
                                        icon: (
                                            <DeleteIcon
                                                id="delete-icon"
                                                key="delete-icon"
                                                sx={{ ml: '16px' }}
                                                color="error"
                                            />
                                        ),
                                        onClick: onDelete,
                                        disabled: isSystem || hasSubTypes,
                                        title: isSystem
                                            ? `You are not allowed to delete system Entity Type`
                                            : hasSubTypes
                                            ? `You are not allowed to delete Entity Type which has sub types`
                                            : ``,
                                    },
                                ]}
                            />
                        </div>
                    </div>
                    <TabGroup
                        value={view}
                        variant="scrollable"
                        scrollButtons="auto"
                        allowScrollButtonsMobile
                        aria-label="Schema navigation tabs"
                        items={SCHEMA_DETAIL_TABS}
                        size="small"
                        style={{
                            marginTop: '8px',
                        }}
                    />
                </div>
                <div className="mainContent">
                    <Outlet />
                </div>
            </SchemaDetailContainer>
        </AnimatedPage>
    ) : (
        isLoadedSchemaTree && <Loading />
    );
};

export default SchemaDetail;
