/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useMemo, useState } from 'react';
import {
    Box,
    Button,
    ContentHeader,
    FixedHeightContainer,
    styled,
    AnimatedPage,
    LoadingOverlay,
    tableStyles,
    SvgIcon,
    SvgIconProps,
} from 'ui-style';
import { useSchemaTree } from '@tripudiotech/admin-caching-store';
import SchemaTree from '../components/SchemaTree';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { Outlet } from 'react-router-dom';
import useLocalStore from '../store/useLocalStore';
import { fetch as apiFetch, schemaUrls } from '@tripudiotech/admin-api';
import { notifyError, notifySuccess } from '@tripudiotech/admin-styleguide';

const RefreshIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M9 14C10.1867 14 11.3467 13.6481 12.3334 12.9888C13.3201 12.3295 14.0892 11.3925 14.5433 10.2961C14.9974 9.19975 15.1162 7.99335 14.8847 6.82946C14.6532 5.66558 14.0818 4.59648 13.2426 3.75736C12.4035 2.91825 11.3344 2.3468 10.1705 2.11529C9.00666 1.88378 7.80026 2.0026 6.7039 2.45673C5.60754 2.91085 4.67047 3.67989 4.01118 4.66658C3.35189 5.65328 3 6.81331 3 8V11.1L1.2 9.3L0.5 10L3.5 13L6.5 10L5.8 9.3L4 11.1V8C4 7.0111 4.29324 6.0444 4.84265 5.22215C5.39206 4.39991 6.17295 3.75904 7.08658 3.38061C8.00021 3.00217 9.00555 2.90315 9.97545 3.09608C10.9454 3.289 11.8363 3.76521 12.5355 4.46447C13.2348 5.16373 13.711 6.05465 13.9039 7.02455C14.0969 7.99446 13.9978 8.99979 13.6194 9.91342C13.241 10.8271 12.6001 11.6079 11.7779 12.1574C10.9556 12.7068 9.98891 13 9 13V14Z"
            fill="inherit"
        />
    </SvgIcon>
);
const SchemaActions = () => {
    const [isLoading, setIsLoading] = useState(false);
    const onRefreshCache = async () => {
        try {
            setIsLoading(true);
            await apiFetch({
                ...schemaUrls.reloadCache,
                skipToast: true,
            });
            notifySuccess('Successfully refreshed cache');
        } catch (e) {
            notifyError('An error occurred while refreshing cache');
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            {isLoading && <LoadingOverlay />}
            <Button
                variant="contained"
                color="secondary"
                size="small"
                endIcon={<RefreshIcon />}
                onClick={onRefreshCache}
            >
                Refresh Cache
            </Button>
        </Box>
    );
};

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const MainLayout = () => {
    const { isLoaded, schemaTreeMap } = useSchemaTree();
    const [isLoading] = useLocalStore((state) => [state.isLoading]);

    const schemaTree = useMemo(() => (schemaTreeMap ? Object.values(schemaTreeMap) : null), [schemaTreeMap]);

    return isLoaded ? (
        <AnimatedPage>
            {isLoading && <LoadingOverlay />}
            <FixedHeightContainer>
                <ContentHeader title="Type and Class" actionsRenderer={SchemaActions} />
                <ContentWrapper>
                    <PanelGroup direction="horizontal" autoSaveId="schema-panels">
                        <Panel
                            id="schema-tree-panel"
                            defaultSize={33}
                            minSize={5}
                            maxSize={80}
                            style={{ height: '100%', position: 'relative' }}
                        >
                            <SchemaTree schemaTree={schemaTree} />
                            <PanelResizeHandle className="handle"></PanelResizeHandle>
                        </Panel>

                        <Panel id="schema-detail-panel" style={{ height: '100%', position: 'relative' }}>
                            <Outlet />
                        </Panel>
                    </PanelGroup>
                </ContentWrapper>
            </FixedHeightContainer>
        </AnimatedPage>
    ) : (
        <LoadingOverlay />
    );
};

export default MainLayout;
