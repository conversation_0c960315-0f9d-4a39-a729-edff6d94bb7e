
import { AnimatedPage, ContentHeader, FixedHeightContainer } from 'ui-style';
import { Outlet } from 'react-router-dom';

const AttributeLayout = () => {
    return (
        <AnimatedPage>
            <FixedHeightContainer>
                <ContentHeader title="Attribute" />
                <Outlet />
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default AttributeLayout;