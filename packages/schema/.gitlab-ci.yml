.schema-variables:
  variables:
    PACKAGE_DIR: packages/schema

aws-stag-schema-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .schema-variables

aws-stag-schema-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .schema-variables
  needs:
    - aws-stag-schema-package

gcp-stag-schema-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .schema-variables

gcp-stag-schema-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .schema-variables
  needs:
    - gcp-stag-schema-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-schema-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .schema-variables

gcp-uat-schema-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .schema-variables
  needs:
    - gcp-uat-schema-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json