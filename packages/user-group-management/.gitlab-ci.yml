.user-group-management-variables:
  variables:
    PACKAGE_DIR: packages/user-group-management

aws-stag-user-group-management-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .user-group-management-variables

aws-stag-user-group-management-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .user-group-management-variables
  needs:
    - aws-stag-user-group-management-package

gcp-stag-user-group-management-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .user-group-management-variables

gcp-stag-user-group-management-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .user-group-management-variables
  needs:
    - gcp-stag-user-group-management-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-user-group-management-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .user-group-management-variables

gcp-uat-user-group-management-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .user-group-management-variables
  needs:
    - gcp-uat-user-group-management-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json