/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { userUrls, fetch } from '@tripudiotech/admin-api';
import lodashGet from 'lodash/get';

type UserGroupAttrs = {
    name: string;
    description?: string;
    isDirty?: boolean;
};

type UserGroupSchema = {
    isLoading: boolean;
    viewedGroupInfor: Record<string, any>;
    viewedGroupUsers: Record<string, any>;
    updateLoading: (status: boolean) => void;
    updateViewedGroupInfor: (groupId: string, user: any) => void;
    updateViewedGroupUsers: (groupId: string, user: any) => void;
    createNewUserGroup: (
        attributes: UserGroupAttrs,
        assignUsers: any[],
        onSuccess?: (res: any) => void,
        onFailed?: (err: any) => void
    ) => void;
    updateUserGroup: (
        id: string,
        data: { properties: UserGroupAttrs; newUsers: any[]; removeUsers: any[] },
        onSuccess?: (res: any) => void,
        onFailed?: (err: any) => void
    ) => void;
};

const useUserGroup = create<UserGroupSchema>((set, get) => ({
    isLoading: false,
    viewedGroupInfor: {},
    viewedGroupUsers: {},
    updateViewedGroupInfor: (groupId, infor) => {
        set({
            viewedGroupInfor: {
                ...get().viewedGroupInfor,
                [groupId]: infor,
            },
        });
    },
    updateViewedGroupUsers: (groupId, users) => {
        set({
            viewedGroupUsers: {
                ...get().viewedGroupUsers,
                [groupId]: users,
            },
        });
    },
    updateLoading: (status: boolean) => {
        set({
            isLoading: status,
        });
    },
    createNewUserGroup: async (
        properties: any,
        assignUsers: any[],
        onSuccess = (res: any) => {},
        onFailed = (e: any) => {}
    ) => {
        try {
            set({ isLoading: true });
            const res = await fetch({
                ...userUrls.createUserGroups,
                data: { properties },
                skipToast: true,
            });

            if (assignUsers.length > 0) {
                const reqs = assignUsers.map((user) =>
                    fetch({
                        ...userUrls.assignUserToGroups,
                        params: {
                            groupId: res.data.id,
                            userEmail: user.email,
                        },
                        skipToast: true,
                    })
                );

                const assignUserRes = await Promise.all(reqs);
                const isSomeUserAssignedSuccess = assignUserRes.some((res) => res.status === 200);

                if (isSomeUserAssignedSuccess) {
                    onSuccess({ ...res, name: properties.name, isSomeUserAssignedSuccess });
                    return;
                }

                onSuccess({ ...res, name: properties.name, isSomeUserAssignedSuccess });
                return;
            }

            onSuccess({ ...res, name: properties.name });
        } catch (e) {
            onFailed({ ...e, name: properties.name });
        } finally {
            set({ isLoading: false });
        }
    },
    updateUserGroup: async (
        groupId: string,
        data: { properties: UserGroupAttrs; newUsers: any[]; removeUsers: any[] },
        onSuccess = (res: any) => {},
        onFailed = (e: any) => {}
    ) => {
        try {
            const {
                newUsers,
                removeUsers,
                properties: { isDirty, ...properties },
            } = data;
            set({ isLoading: true });
            let res = {};

            if (isDirty) {
                res = await fetch({
                    ...userUrls.updateUserGroups,
                    data: {
                        properties,
                    },
                    params: {
                        groupId,
                    },
                    skipToast: true,
                });

                if (newUsers.length === 0 && removeUsers.length === 0) {
                    onSuccess({ ...res, data: properties, isDirty });
                    return;
                }
            }

            if (newUsers.length === 0 && removeUsers.length === 0) {
                onSuccess({ data: properties, isDirty: false });
                return;
            }

            const addUserReqs = newUsers.map((user) =>
                fetch({
                    ...userUrls.assignUserToGroups,
                    params: {
                        groupId,
                        userEmail: user.email,
                    },
                    skipToast: true,
                })
            );
            const removeUserReqs = removeUsers.map((user) =>
                fetch({
                    ...userUrls.removeUserOutGroups,
                    params: {
                        groupId,
                        userEmail: user.email || lodashGet(user, 'properties.email', ''),
                    },
                    skipToast: true,
                })
            );

            const addRemoveUserRes = await Promise.all([...addUserReqs, ...removeUserReqs]);

            if (addRemoveUserRes.every((r) => r.status === 200)) {
                onSuccess({ ...res, name: data.properties.name, isDirty: true });
                return;
            }

            onFailed({
                customeMessage: 'An error occurred while adding/removing a user. Please try again later.',
            });
        } catch (e) {
            onFailed(e);
        } finally {
            set({ isLoading: false });
        }
    },
}));

export default useUserGroup;
