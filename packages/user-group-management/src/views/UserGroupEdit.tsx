/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import {
    Box,
    Button,
    RightTray,
    Stack,
    SchemaFieldSection,
    LoadingOverlay,
    Loading,
    tableIcons,
    IconButton,
    ArrowLeft,
    tableStyles,
} from 'ui-style';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { userService } from '@tripudiotech/admin-api';
import { notifySuccess, notifyError } from '@tripudiotech/admin-styleguide';
import get from 'lodash/get';
import clone from 'lodash/clone';
import find from 'lodash/find';
import isEmpty from 'lodash/isEmpty';
import { AgGridReact } from '@ag-grid-community/react';
import UserGroupForm from '../components/UserGroupForm';
import useUserGroup from '../stores/useUserGroup';

const UserGroupEdit = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const formRef = useRef(null);
    // const [groupData, setGroupData] = useState(null);
    const { isLoading, updateLoading, viewedGroupUsers, updateUserGroup, viewedGroupInfor } = useUserGroup(
        (state) => state
    );

    const [leftSelectedUsers, setLeftSelectedUsers] = useState([]);
    const [rightSelectedUsers, setRightSelectedUsers] = useState([]);
    const [assignedUsers, setAssignedUsers] = useState([]);

    const selectedUsersRef = useRef([]);
    const leftTableRef = useRef(null);
    const rightTableRef = useRef(null);
    const gridOptions = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            columnDefs: [
                {
                    field: 'properties.name',
                    headerName: 'Name',
                    minWidth: 80,
                    filter: false,
                    checkboxSelection: true,
                    rowDrag: true,
                    cellRenderer: ({ data, value }) => {
                        const { firstName, lastName } = data;
                        if (value) return value;

                        return `${firstName} ${lastName}`;
                    },
                },
                {
                    field: 'properties.email',
                    headerName: 'Email',
                    minWidth: 120,
                    filterParams: {
                        filterOptions: ['contains'],
                        suppressAndOrCondition: true,
                    },
                    cellRenderer: ({ data, value }) => {
                        const { email } = data;
                        if (value) return value;

                        return email;
                    },
                },
            ],
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            loadingOverlayComponent: Loading,
            serverSideInfiniteScroll: true,
            showDisabledCheckboxes: true,
            cacheBlockSize: 10,
            suppressRowClickSelection: true,
            rowDragMultiRow: true,
        }),
        []
    );

    const emitRefresfEventNRedirect = useCallback(() => {
        // Dispatch a custom event to refresh the data of user group table
        const event = new CustomEvent('refresh-user-group-table', { detail: true });
        window.dispatchEvent(event);
    }, [navigate]);

    const refreshAssignedUserTable = useCallback(
        (data: any) => {
            const event = new CustomEvent(`refresh-assigned-user-group-${params.id}`, { detail: true });
            window.dispatchEvent(event);
        },
        [params.id]
    );

    const onUpdateSuccess = (res) => {
        if (res.isDirty) {
            notifySuccess(<span>Update User Group successfully</span>);
            formRef.current.resetForm();
            emitRefresfEventNRedirect();
            refreshAssignedUserTable(res.data);
        }

        navigate(`/user-group-management/${params.id}`, {
            state: {
                data: { properties: res.data },
            },
        });
    };

    const onUpdateFailed = useCallback(
        (e) => {
            formRef.current?.setSubmitting(false);
            const { customeMessage } = e;

            emitRefresfEventNRedirect();
            refreshAssignedUserTable(null);
            if (customeMessage) {
                notifyError(<span>{customeMessage}</span>);
                return;
            }

            notifyError(
                <span>
                    An error occurred while updating User Group <b>{viewedGroupInfor[params.id]?.properties?.name}</b>.
                    Please try again later.
                </span>
            );
        },
        [formRef, viewedGroupInfor, params.id]
    );

    useEffect(() => {
        if (params.id) {
            setAssignedUsers(viewedGroupUsers[params.id] || []);
            selectedUsersRef.current = viewedGroupUsers[params.id] || [];
        }
    }, [viewedGroupUsers, params.id]);

    const handleSave = async () => {
        formRef.current.submitForm();

        if (!formRef.current.isValid) return;

        const selectedUsers = selectedUsersRef.current;
        let usersForAdd = [];
        let usersForRemove = [];

        if (assignedUsers.length && selectedUsers.length === 0) {
            usersForRemove = assignedUsers;
        } else if (assignedUsers.length === 0 && selectedUsers.length) {
            usersForAdd = selectedUsers;
        } else if (assignedUsers.length && selectedUsers.length) {
            selectedUsers.forEach((selectedUser) => {
                const email = selectedUser.email || get(selectedUser, 'properties.email', '');
                if (email) {
                    if (!find(assignedUsers, ['email', email]) && !find(assignedUsers, ['properties.email', email])) {
                        usersForAdd.push(selectedUser);
                    }
                }
            });
            assignedUsers.forEach((assignedUser) => {
                const email = get(assignedUser, 'properties.email', '');
                if (email) {
                    if (!find(selectedUsers, ['email', email]) && !find(selectedUsers, ['properties.email', email])) {
                        usersForRemove.push(assignedUser);
                    }
                }
            });
        }

        updateUserGroup(
            params.id,
            {
                properties: { ...formRef.current.values, isDirty: formRef.current.dirty },
                newUsers: usersForAdd,
                removeUsers: usersForRemove,
            },
            onUpdateSuccess,
            onUpdateFailed
        );
    };

    const createServerSideDatasource = () => {
        return {
            getRows: async (params) => {
                const { firstName, lastName, email } = params.request.filterModel;
                let query = '';

                if (firstName?.filter) {
                    query = query ? `${query}&firstName=${firstName.filter}` : `firstName=${firstName.filter}`;
                }

                if (lastName?.filter) {
                    query = query ? `${query}&lastName=${lastName.filter}` : `lastName=${lastName.filter}`;
                }

                if (email?.filter) {
                    query = query ? `${query}&email=${email.filter}` : `email=${email.filter}`;
                }

                leftTableRef.current.api.showLoadingOverlay();
                userService
                    .getUsers(params.request.startRow, 10, query)
                    .then(({ data }) => {
                        const totalCount = data.pageInfo.total;
                        const rowsThisPage = data.data[0];

                        params.success({
                            rowData: rowsThisPage,
                            rowCount: totalCount,
                        });
                    })
                    .catch(() => {
                        params.fail();
                    })
                    .finally(() => {
                        leftTableRef.current?.api?.hideOverlay();

                        if (leftTableRef.current?.api.getDisplayedRowCount() === 0) {
                            leftTableRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    };

    const onGridReady = useCallback((params) => {
        const datasource = createServerSideDatasource();
        params.api.setServerSideDatasource(datasource);
        addLeftDropZone(params);
    }, []);

    const moveToRight = useCallback(() => {
        const newRows = leftTableRef.current.api.getSelectedRows();
        let users = clone(selectedUsersRef.current);

        if (selectedUsersRef.current.length === 0) {
            users = newRows;
        } else {
            newRows.forEach((row: any) => {
                if (!find(users, ['email', row.email]) && !find(users, ['properties.email', row.email])) {
                    users.push(row);
                }
            });
        }

        selectedUsersRef.current = users;
        setLeftSelectedUsers([]);
        leftTableRef.current.api.deselectAll();
    }, [selectedUsersRef, leftTableRef, setLeftSelectedUsers]);

    const moveToLeft = useCallback(() => {
        const removingRows = rightTableRef.current.api.getSelectedRows();
        const users = selectedUsersRef.current.filter((user) => {
            if (user.email) {
                return (
                    !find(removingRows, ['email', user.email]) && !find(removingRows, ['properties.email', user.email])
                );
            }

            if (get(user, 'properties.email', '')) {
                const email = get(user, 'properties.email', '');
                return !find(removingRows, ['email', email]) && !find(removingRows, ['properties.email', email]);
            }

            return true;
        });

        selectedUsersRef.current = users;
        setRightSelectedUsers([]);
        rightTableRef.current.api.deselectAll();
    }, [rightTableRef, selectedUsersRef, setRightSelectedUsers]);

    const addLeftDropZone = useCallback(
        (params) => {
            const container = document.querySelector('#right-table');
            const dropZone = {
                getContainer: () => container,
                onDragStop: () => {
                    moveToRight();
                },
            };
            params.api.addRowDropZone(dropZone);
        },
        [moveToRight]
    );

    const addRightDropZone = useCallback(() => {
        const container = document.querySelector('#left-table');
        const dropZone: any = {
            getContainer: () => container,
            onDragStop: () => {
                moveToLeft();
            },
        };
        rightTableRef.current.api.addRowDropZone(dropZone);
    }, [moveToLeft]);

    useEffect(() => {
        if (!isEmpty(get(location.state, 'assignedUsers', []))) {
            selectedUsersRef.current = get(location.state, 'assignedUsers', []);
            setAssignedUsers(get(location.state, 'assignedUsers', []));
        }
    }, [location.state]);

    const onRightTableReady = useCallback(() => {
        rightTableRef.current?.api?.showLoadingOverlay();

        if (viewedGroupUsers[params.id]) {
            rightTableRef.current?.api?.hideOverlay();
            if (get(viewedGroupUsers, params.id, []).length === 0) {
                rightTableRef.current?.api?.showNoRowsOverlay();
            }
        }
    }, [viewedGroupUsers, params.id]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title="User Group Edit"
                componentName="user-group-edit"
                open
                onClose={() => {
                    navigate(`/user-group-management/${params.id}`, { state: { data: viewedGroupInfor[params.id] } });
                }}
                // PaperProps={{
                //     sx: {
                //         width: 950,
                //     }
                // }}
                fullWidth
                disableCloseOnClickOutside
                hideConfirm
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                    }}
                >
                    <Box
                        className="general-info--wrapper"
                        sx={{
                            width: '100%',
                        }}
                    >
                        <Box
                            className="general-info"
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '12px',
                                padding: '12px',
                                height: '100%',
                            }}
                        >
                            <SchemaFieldSection>General Information</SchemaFieldSection>
                            <UserGroupForm formRef={formRef} userGroupData={viewedGroupInfor[params.id]} />

                            <SchemaFieldSection>Select users to assign to this group</SchemaFieldSection>

                            <Box sx={{ ...tableStyles, height: '100%', display: 'flex', gap: '16px' }}>
                                {/** Left Table */}
                                <Box sx={{ height: '100%', flex: 10 }} id="left-table">
                                    <AgGridReact
                                        className="ag-theme-alpine"
                                        onGridReady={onGridReady}
                                        ref={leftTableRef}
                                        {...gridOptions}
                                        rowModelType={'serverSide'}
                                        rowSelection={'multiple'}
                                        icons={tableIcons}
                                        onRowDragEnter={(e) => e.node.setSelected(true)}
                                        onSelectionChanged={(e) => {
                                            setLeftSelectedUsers(e.api.getSelectedRows());
                                        }}
                                    ></AgGridReact>
                                </Box>

                                {/** Action Button */}
                                <Box
                                    sx={{
                                        height: '100%',
                                        flex: 1,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        paddingTop: '25px',
                                        gap: '40px',
                                    }}
                                >
                                    <IconButton
                                        size="small"
                                        onClick={moveToRight}
                                        disabled={leftSelectedUsers.length === 0}
                                    >
                                        <ArrowLeft sx={{ transform: 'rotate(180deg)' }} />
                                    </IconButton>
                                    <IconButton
                                        size="small"
                                        onClick={moveToLeft}
                                        disabled={rightSelectedUsers.length === 0}
                                    >
                                        <ArrowLeft />
                                    </IconButton>
                                </Box>

                                {/** right Table */}
                                <Box sx={{ height: '100%', flex: 10 }} id="right-table">
                                    <AgGridReact
                                        ref={rightTableRef}
                                        className="ag-theme-alpine"
                                        {...gridOptions}
                                        rowData={selectedUsersRef.current}
                                        rowSelection={'multiple'}
                                        rowModelType={'clientSide'}
                                        icons={tableIcons}
                                        onSelectionChanged={(e) => {
                                            setRightSelectedUsers(e.api.getSelectedRows());
                                        }}
                                        onRowDragEnter={(e) => e.node.setSelected(true)}
                                        onFirstDataRendered={addRightDropZone}
                                        onGridReady={onRightTableReady}
                                    ></AgGridReact>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                </Stack>

                <Box
                    sx={{
                        mt: 'auto',
                        display: 'flex',
                        gap: '8px',
                        justifyContent: 'flex-end',
                        margin: '16px 24px',
                    }}
                >
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={() => {
                            formRef.current?.resetForm();
                            navigate(`/user-group-management/${params.id}`, {
                                state: { data: viewedGroupInfor[params.id] },
                            });
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        onClick={() => {
                            handleSave();
                        }}
                        color="primary"
                    >
                        Save Changes
                    </Button>
                </Box>
            </RightTray>

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default UserGroupEdit;
