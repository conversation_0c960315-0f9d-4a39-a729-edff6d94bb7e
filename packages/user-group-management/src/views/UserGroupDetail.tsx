/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import {
    Box,
    Button,
    RightTray,
    Stack,
    SchemaFieldSection,
    LoadingOverlay,
    Grid,
    SchemaField,
    Loading,
    tableStyles,
} from 'ui-style';
import { useNavigate, useParams, useLocation, Outlet } from 'react-router-dom';
import { fetch, entityUrls, userUrls } from '@tripudiotech/admin-api';
import { useDialog } from '@tripudiotech/admin-caching-store';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { AgGridReact } from '@ag-grid-community/react';
import useUserGroup from '../stores/useUserGroup';

const BusinessRuleDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const gridRef = useRef(null);
    const { isLoading, updateLoading, viewedGroupUsers, updateViewedGroupInfor, updateViewedGroupUsers } = useUserGroup(
        (state) => state
    );

    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const [groupData, setGroupData] = useState(null);
    const gridOptions = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            animateRows: true,
            columnDefs: [
                {
                    field: 'properties.name',
                    headerName: 'Name',
                    minWidth: 80,
                },
                {
                    field: 'properties.email',
                    headerName: 'Email',
                    minWidth: 120,
                    filterParams: {
                        filterOptions: ['contains'],
                        suppressAndOrCondition: true,
                    },
                },
            ],
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            loadingOverlayComponent: Loading,
        }),
        []
    );

    const emitRefresfEventNRedirect = useCallback(() => {
        // Dispatch a custom event to refresh the data of user group table
        const refreshProcessListEvent = new CustomEvent('refresh-user-group-table', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
        navigate('/user-group-management');
    }, [navigate]);

    const handleDelete = () => {
        const groupName = get(groupData, 'properties.name', '');
        onOpenDialog(
            'Delete User Group',
            `Do you want to delete Group <b>${groupName}</b>`,
            async () => {
                try {
                    updateLoading(true);
                    await fetch({
                        ...userUrls.deleteUserGroups,
                        params: {
                            groupId: groupData.id,
                        },
                        successMessage: (
                            <span>
                                <b>{groupName}</b> has been deleted successfully
                            </span>
                        ),
                    });
                    updateLoading(false);
                    emitRefresfEventNRedirect();
                } catch (e) {
                    updateLoading(false);
                }
            },
            'error'
        );
    };

    const getUserUnderGroup = useCallback(
        async (groupId, force = false) => {
            try {
                if (viewedGroupUsers[groupId] && !force) return;

                gridRef?.current?.api?.showLoadingOverlay();

                const {
                    data: { data },
                } = await fetch({
                    ...userUrls.getUserUnderGroup,
                    params: {
                        groupId,
                    },
                    qs: {
                        limit: 1000,
                    },
                    skipToast: true,
                });

                updateViewedGroupUsers(groupId, data);
            } catch (e) {
                console.info(e);
            } finally {
                gridRef.current?.api.hideOverlay();
                if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                    gridRef.current?.api.showNoRowsOverlay();
                }
            }
        },
        [gridRef]
    );

    const getGroupDetail = useCallback(async () => {
        try {
            updateLoading(true);
            const { data } = await fetch({
                ...entityUrls.getEntityById,
                params: {
                    entityType: 'UserGroup',
                    entityId: params.id,
                },
                skipToast: true,
            });

            setGroupData(data);
            updateViewedGroupInfor(params.id, data);
        } catch (e) {
            console.log(e);
        } finally {
            updateLoading(false);
        }
    }, [params.id]);

    useEffect(() => {
        if (!isEmpty(get(location.state, 'data', null))) {
            setGroupData(get(location.state, 'data', null));
            updateViewedGroupInfor(params.id, get(location.state, 'data', null));
            return;
        }

        getGroupDetail();
    }, [location.state, params.id]);

    useEffect(() => {
        if (params.id) {
            window.addEventListener(`refresh-assigned-user-group-${params.id}`, (e) => {
                getUserUnderGroup(params.id, true);
            });
        }
    }, [params.id]);

    const onGridReady = useCallback(() => {
        if (params.id) getUserUnderGroup(params.id);
    }, [params.id]);

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <RightTray
                title="User Group Detail"
                componentName="user-group-detail"
                open
                onClose={() => {
                    navigate('/user-group-management');
                }}
                PaperProps={{
                    sx: {
                        width: 475,
                    },
                }}
                disableCloseOnClickOutside
                hideConfirm
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                            '&.panel-close': {
                                overflowY: 'hidden',
                            },
                        },
                    }}
                >
                    <Box
                        className="general-info--wrapper"
                        sx={{
                            width: '100%',
                        }}
                    >
                        <Box
                            className="general-info"
                            sx={{
                                ...tableStyles,
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '12px',
                                padding: '12px',
                                height: '100%',
                            }}
                        >
                            <SchemaFieldSection>General Information</SchemaFieldSection>

                            <Grid container spacing={2}>
                                <Grid
                                    item
                                    xs={12}
                                    sx={{
                                        '> .MuiGrid-root': {
                                            '&:nth-of-type(3)': {
                                                'div:nth-of-type(2)': {
                                                    paddingTop: 0,
                                                    span: {
                                                        lineHeight: '18px',
                                                    },
                                                },
                                            },
                                        },
                                    }}
                                >
                                    <SchemaField label="Name" value={get(groupData, 'properties.name', '')} />
                                    <SchemaField label="Auth ID" value={get(groupData, 'properties.authId', '')} />
                                    <SchemaField
                                        label="Description"
                                        value={get(groupData, 'properties.description', '')}
                                        breakline
                                    />
                                </Grid>
                            </Grid>

                            <SchemaFieldSection>Users in this group</SchemaFieldSection>

                            <Box className="ag-theme-alpine" sx={{ height: '100%' }}>
                                <AgGridReact
                                    ref={gridRef}
                                    {...gridOptions}
                                    onGridReady={onGridReady}
                                    rowModelType={'clientSide'}
                                    rowData={viewedGroupUsers[params?.id] || []}
                                ></AgGridReact>
                            </Box>
                        </Box>
                    </Box>
                </Stack>

                <Box
                    sx={{
                        mt: 'auto',
                        display: 'flex',
                        gap: '8px',
                        justifyContent: 'flex-end',
                        margin: '16px 24px',
                    }}
                >
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={() => {
                            navigate('/user-group-management');
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                        }}
                        variant="outlined"
                        onClick={handleDelete}
                        color="error"
                    >
                        Delete Group
                    </Button>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        onClick={() => {
                            navigate(`${location.pathname}/edit`, {
                                state: {
                                    data: location.state?.data || groupData,
                                    assignedUsers: viewedGroupUsers[params?.id] || [],
                                },
                            });
                        }}
                        color="primary"
                    >
                        Edit
                    </Button>
                </Box>
            </RightTray>

            {isLoading && <LoadingOverlay />}

            <Outlet />
        </Box>
    );
};

export default BusinessRuleDetail;
