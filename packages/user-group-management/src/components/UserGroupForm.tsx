/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useEffect, useMemo } from 'react';
import { Formik, Form, Field } from 'formik';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';
import { AttributeSchema, schemaHelper } from 'ui-common';
import { Box, FormikTextField, Grid, RichTextField } from 'ui-style';
import * as yup from 'yup';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';
import isNil from 'lodash/isNil';
import { AttributeType } from '@tripudiotech/admin-styleguide';

const defaultInitValues = {
    name: '',
    description: '',
    title: '',
};

const UserGroupForm = ({ formRef, userGroupData }: any) => {
    const [initialValues, setInitialValues] = useState<any>(defaultInitValues);

    const [userGroupSchema, getSchema] = useSchemaDetail((state) => [
        state.schema[SYSTEM_ENTITY_TYPE.USER_GROUP],
        state.getSchema,
    ]);

    const attributes: AttributeSchema[] = useMemo(() => {
        if (userGroupSchema) {
            return schemaHelper
                .getSchemaOrderedAttributes(userGroupSchema)
                .filter((attribute: AttributeSchema) => attribute.visible && isEmpty(attribute.identifier));
        }
        return [];
    }, [userGroupSchema]);

    const validationSchema = useMemo(() => {
        if (!userGroupSchema) return null;
        const validateRule = {
            name: yup.string().required('Name is required'),
            description: yup.string(),
            title: yup.string(),
        };
        attributes.forEach((attribute: any) => {
            if (!attribute.visible || attribute.identifier || attribute.nullable) return;
            if (!validateRule[attribute.name]) {
                validateRule[attribute.name] = yup.string().required(`${attribute.displayName} is required`);
            }
        });
        return yup.object().shape(validateRule);
    }, [userGroupSchema]);

    useEffect(() => {
        getSchema(SYSTEM_ENTITY_TYPE.USER_GROUP);
    }, []);

    useEffect(() => {
        setInitialValues({
            name: get(userGroupData, 'properties.name', ''),
            title: get(userGroupData, 'properties.title', ''),
            description: get(userGroupData, 'properties.description', ''),
        });
    }, [userGroupData]);

    return (
        <Formik
            enableReinitialize
            isInitialValid={!!get(userGroupData, 'properties.name', '')}
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={() => {}}
            innerRef={formRef}
        >
            <Form>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <Grid container spacing={2}>
                        {attributes.map((attribute: any) => {
                            if (!attribute.visible || attribute.identifier) return;
                            const { id, displayName, name, description, nullable } = attribute;
                            const isRequired = isNil(nullable) ? false : !Boolean(nullable);
                            if (attribute.richText) {
                                return (
                                    <Grid item xs={12} key={id}>
                                        <Field
                                            fullWidth
                                            variant="outlined"
                                            id={id}
                                            label={displayName}
                                            name={name}
                                            helperText={description}
                                            required={isRequired}
                                            InputLabelProps={{ shrink: true }}
                                            component={RichTextField}
                                        />
                                    </Grid>
                                );
                            }
                            switch (attribute.type) {
                                case AttributeType.STRING:
                                    return (
                                        <Grid item xs={12} key={id}>
                                            <Field
                                                fullWidth
                                                variant="outlined"
                                                component={FormikTextField}
                                                required={isRequired}
                                                label={displayName}
                                                name={name}
                                                helperText={description}
                                                InputLabelProps={{ shrink: true }}
                                            />
                                        </Grid>
                                    );
                                case AttributeType.DATE:
                            }
                        })}
                    </Grid>
                </Box>
            </Form>
        </Formik>
    );
};

export default UserGroupForm;
