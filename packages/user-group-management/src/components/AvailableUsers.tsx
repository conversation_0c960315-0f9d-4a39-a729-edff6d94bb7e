/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useMemo, useCallback, useRef } from 'react';
import { userService } from '@tripudiotech/admin-api';
import { AgGridReact } from '@ag-grid-community/react';
import { Box, Grid, Chip, Loading } from 'ui-style';

const AvailableUsers = ({
    onSelectionChanged,
    assignedUsers,
}: {
    onSelectionChanged: (user: any[]) => void;
    assignedUsers?: any[];
}) => {
    const gridRef = useRef<AgGridReact>(null);
    const gridOptions: any = useMemo(
        () => ({
            defaultColDef: {
                flex: 1,
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Full Name',
                    filter: false,
                    checkboxSelection: true,
                    cellRenderer: ({ data, value }) => {
                        const { firstName, lastName } = data;
                        if (value) return value;

                        return `${firstName} ${lastName}`;
                    },
                },
                {
                    field: 'email',
                    headerName: 'Email',
                    filterParams: {
                        filterOptions: ['contains'],
                        suppressAndOrCondition: true,
                    },
                    cellRenderer: ({ data, value }) => {
                        const { email } = data;
                        if (value) return value;

                        return email;
                    },
                },
                {
                    field: 'enabled',
                    headerName: 'Status',
                    cellRenderer: function (params) {
                        return (
                            <Chip
                                size="small"
                                label={params.value ? 'ACTIVE' : 'INACTIVE'}
                                variant={params.value ? 'status-success' : 'status-error'}
                            />
                        );
                    },
                },
            ],
            loadingOverlayComponent: Loading,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            showDisabledCheckboxes: true,
            cacheBlockSize: 10,
            rowSelection: 'multiple',
        }),
        []
    );

    const createServerSideDatasource = () => {
        return {
            getRows: async (params) => {
                const { firstName, lastName, email } = params.request.filterModel;
                let query = '';

                if (firstName?.filter) {
                    query = query ? `${query}&firstName=${firstName.filter}` : `firstName=${firstName.filter}`;
                }

                if (lastName?.filter) {
                    query = query ? `${query}&lastName=${lastName.filter}` : `lastName=${lastName.filter}`;
                }

                if (email?.filter) {
                    query = query ? `${query}&email=${email.filter}` : `email=${email.filter}`;
                }
                gridRef.current.api.showLoadingOverlay();
                userService
                    .getUsers(params.request.startRow, 10, query)
                    .then(({ data }) => {
                        const totalCount = data.pageInfo.total;
                        const rowsThisPage = data.data[0];

                        params.success({
                            rowData: rowsThisPage,
                            rowCount: totalCount,
                        });
                    })
                    .catch(() => {
                        params.fail();
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();

                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    };

    const onGridReady = useCallback(
        (params) => {
            const datasource = createServerSideDatasource();
            params.api.setServerSideDatasource(datasource);
        },
        [assignedUsers]
    );

    return (
        <Grid container spacing={2} sx={{ height: '100%' }}>
            <Grid item sm={12}>
                <Box className="ag-theme-alpine" sx={{ height: '100%' }}>
                    <AgGridReact
                        {...gridOptions}
                        ref={gridRef}
                        onGridReady={onGridReady}
                        onSelectionChanged={(e) => {
                            onSelectionChanged(e.api.getSelectedRows());
                        }}
                    ></AgGridReact>
                </Box>
            </Grid>
        </Grid>
    );
};

export default AvailableUsers;
