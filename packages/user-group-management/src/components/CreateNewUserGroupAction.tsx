/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useState } from 'react';
import { Box, Button, RightTray, Stack, SchemaFieldSection, LoadingOverlay, Typography } from 'ui-style';
import { useToggle } from 'ui-common';
import { notifySuc<PERSON>, notifyError } from '@tripudiotech/admin-styleguide';
import get from 'lodash/get';
import UserGroupForm from './UserGroupForm';
import AvailableUsers from './AvailableUsers';
import useUserGroup from '../stores/useUserGroup';
import { RESPONSE_ERROR_TYPE, SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';
import { useSchemaDetail } from '@tripudiotech/admin-caching-store';

const CreateNewUserGroupAction = () => {
    const [open, openToggle] = useToggle(false);
    const [selectedUsers, setSelectedUser] = useState([]);
    const { isLoading, createNewUserGroup } = useUserGroup((state) => state);
    const [userGroupSchema, getAttributeName] = useSchemaDetail((state) => [
        state.schema[SYSTEM_ENTITY_TYPE.USER_GROUP],
        state.getAttributeName,
    ]);

    const formRef = useRef(null);

    const handleClick = async () => {
        openToggle.open();
    };

    const onCreateSuccess = (res: any) => {
        const { name, isSomeUserAssignedSuccess } = res;

        if (isSomeUserAssignedSuccess === undefined) {
            notifySuccess(
                <span>
                    User Group <b>{res.name}</b> has been created successfully
                </span>
            );
        } else {
            if (isSomeUserAssignedSuccess) {
                notifySuccess(
                    <span>
                        User Group <b>{res.name}</b> has been created successfully with some assigned users.
                    </span>
                );
            } else {
                notifySuccess(
                    <span>
                        User Group <b>{res.name}</b> has been created successfully with no users assigned. Please try
                        again later.
                    </span>
                );
            }
        }
        formRef.current.resetForm();
        openToggle.close();

        // Dispatch a custom event to refresh the data of business rule
        const refreshProcessListEvent = new CustomEvent('refresh-user-group-table', { detail: true });
        window.dispatchEvent(refreshProcessListEvent);
    };

    const onCreateFailed = (res: any) => {
        formRef.current.setSubmitting(false);
        if (get(res, 'response.data.statusCode', null) === 409) {
            notifyError(
                <Typography>
                    Group named <b>{res?.name || ''}</b> already exists
                </Typography>
            );
            return;
        } else if (get(res, 'response.data.statusCode', null) === 400) {
            if (res.response.data?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE) {
                const invalidAttributeId = res.response.data.metadata.data;
                const invalidAttributeName = getAttributeName(userGroupSchema, invalidAttributeId);
                if (invalidAttributeName && formRef.current) {
                    formRef.current.setFieldError(invalidAttributeName, res.response.data.errorMessage);
                    return;
                }
            }
        }

        notifyError(get(res, 'response.data.errorMessage', 'An error occurred while creating the User Group. Please try again later.'));
    };

    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <Button variant="contained" color="secondary" size="small" onClick={handleClick}>
                Create User Group
            </Button>

            <RightTray
                title="Create User Group"
                componentName="create-user-group"
                open={open}
                onClose={openToggle.close}
                onConfirm={async () => {
                    formRef.current.submitForm();

                    if (!formRef.current.isValid) return;
                    createNewUserGroup(formRef.current.values, selectedUsers, onCreateSuccess, onCreateFailed);
                }}
                confirmText="Create"
                disableCloseOnClickOutside
            >
                <Stack
                    direction="row"
                    sx={{
                        height: '100%',
                        '> div': {
                            maxHeight: 'calc(100vh - 137px)',
                            overflow: 'auto',
                            overflowX: 'hidden',
                        },
                    }}
                >
                    <Box
                        className="general-info--wrapper"
                        sx={{
                            width: '100%',
                        }}
                    >
                        <Box
                            className="general-info"
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '12px',
                                padding: '12px',
                                height: '100%',
                            }}
                        >
                            <SchemaFieldSection>General Information</SchemaFieldSection>
                            <UserGroupForm formRef={formRef} />

                            <SchemaFieldSection>Select users to assign to this group</SchemaFieldSection>
                            <AvailableUsers onSelectionChanged={setSelectedUser} />
                        </Box>
                    </Box>
                </Stack>
            </RightTray>

            {isLoading && <LoadingOverlay />}
        </Box>
    );
};

export default CreateNewUserGroupAction;
