/**
 * Schema tree is parsed and stored as HashMap for performance
 */
type SchemaStore = {
    schemaTreeMap: Record<string, any> | null;
    isLoaded: boolean;
    setSchemaTree: (schemaTree: any) => void;
    getSchemaTree: (force?: boolean) => Record<string, any>;
};
declare const useSchemaTree: import("zustand").UseBoundStore<import("zustand").StoreApi<SchemaStore>>;
export default useSchemaTree;
//# sourceMappingURL=useSchemaTree.d.ts.map