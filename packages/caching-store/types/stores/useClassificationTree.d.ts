import { ClassificationTreeNode } from 'ui-common';
/**
 * Schema tree is parsed and stored as HashMap for performance
 */
type ClassificationTreeStore = {
    classificationTreeMap: Record<string, ClassificationTreeNode> | null;
    isLoaded: boolean;
    setClassificationTree: (schemaTree: any) => void;
    getClassificationTree: (force?: boolean) => Record<string, any>;
};
declare const useClassificationTree: import("zustand").UseBoundStore<import("zustand").StoreApi<ClassificationTreeStore>>;
export default useClassificationTree;
//# sourceMappingURL=useClassificationTree.d.ts.map