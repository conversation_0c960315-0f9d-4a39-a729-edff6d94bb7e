export interface RolesStore {
    companies: any[];
    companiesMap: Record<string, any> | null;
    teams: any[];
    teamsMap: Record<string, any> | null;
    departments: any[];
    departmentsMap: Record<string, any> | null;
    getCompanies: (force?: Boolean) => Promise<any[]>;
    getTeams: (force?: Boolean) => Promise<any[]>;
    getDepartments: (force?: Boolean) => Promise<any[]>;
    getAll: () => Promise<void>;
    searchCompany: (searchText?: string) => Promise<any[]>;
    getDepartmentsByCompany: (companyId: string, searchText?: string) => Promise<any[]>;
    getTeamsByCompany: (companyId: string, searchText?: string) => Promise<any[]>;
    getCompaniesMapFromCompanies: (companies: any[]) => Record<string, any>;
}
declare const useOrg: import("zustand").UseBoundStore<import("zustand").StoreApi<RolesStore>>;
export default useOrg;
//# sourceMappingURL=useOrg.d.ts.map