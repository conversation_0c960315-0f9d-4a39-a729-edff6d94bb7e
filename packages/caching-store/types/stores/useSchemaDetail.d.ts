import { Lifecycle } from '@tripudiotech/admin-api';
import { ISchemaDetail } from 'ui-common';
export interface LifecycleWithState {
    lifeCycle: Lifecycle;
    states: Record<string, any>;
}
export interface SchemaWithLifeCycleDetail extends ISchemaDetail {
    lifecycles?: LifecycleWithState[];
}
type SchemaDetailStore = {
    schema: Record<string, SchemaWithLifeCycleDetail>;
    schemaLifecycle: Record<string, any[]>;
    isLoading: Record<string, boolean>;
    isLoaded: boolean;
    getSchema: (entityTypeName: string, withLifecycle?: boolean, signal?: AbortSignal) => Promise<SchemaWithLifeCycleDetail>;
    fechSchema: (entityTypeName: string, signal?: AbortSignal) => Promise<any>;
    fetchSchemaLifecycle: (entityTypeName: string, signal?: AbortSignal) => Promise<any>;
    getMultipleSchema: (entityTypeNames: string[]) => void;
    getAttributeName: (schemaDetail: SchemaWithLifeCycleDetail, attributeId: string) => string;
};
declare const useSchemaDetail: import("zustand").UseBoundStore<import("zustand").StoreApi<SchemaDetailStore>>;
export default useSchemaDetail;
//# sourceMappingURL=useSchemaDetail.d.ts.map