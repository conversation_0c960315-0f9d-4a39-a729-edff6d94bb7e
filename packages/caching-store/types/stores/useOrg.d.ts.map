{"version": 3, "file": "useOrg.d.ts", "sourceRoot": "", "sources": ["../../src/stores/useOrg.ts"], "names": [], "mappings": "AAuBA,MAAM,WAAW,UAAU;IACvB,SAAS,EAAE,GAAG,EAAE,CAAC;IACjB,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;IACzC,KAAK,EAAE,GAAG,EAAE,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;IACrC,WAAW,EAAE,GAAG,EAAE,CAAC;IACnB,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;IAC3C,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAClD,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9C,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IACpD,MAAM,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IACvD,uBAAuB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IACpF,iBAAiB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAC9E,4BAA4B,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC3E;AAED,QAAA,MAAM,MAAM,yEA8IT,CAAC;AAEJ,eAAe,MAAM,CAAC"}