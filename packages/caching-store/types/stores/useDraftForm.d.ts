type DraftForm = {
    id: string;
    title: string;
    content: string;
    formValues: any;
    viewLocation: {
        contextPath: string;
        fullPath: string;
        query: string;
    };
    options: any;
};
type SideBar = {
    forms: DraftForm[];
    setForms: (forms: DraftForm[]) => void;
    addForm: (form: DraftForm) => void;
    removeForm: (formId: string) => void;
};
declare const useDraftForm: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<SideBar>, "persist"> & {
    persist: {
        setOptions: (options: Partial<import("zustand/middleware").PersistOptions<SideBar, SideBar>>) => void;
        clearStorage: () => void;
        rehydrate: () => Promise<void> | void;
        hasHydrated: () => boolean;
        onHydrate: (fn: (state: SideBar) => void) => () => void;
        onFinishHydration: (fn: (state: SideBar) => void) => () => void;
        getOptions: () => Partial<import("zustand/middleware").PersistOptions<SideBar, SideBar>>;
    };
}>;
export default useDraftForm;
//# sourceMappingURL=useDraftForm.d.ts.map