type FilterStore = {
    appliedFilters: any[];
    setAppliedFilters: (filters: any) => void;
    addFilter: (filter: any) => void;
    removeFilter: (index: number) => void;
};
declare const useFilters: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<FilterStore>, "persist"> & {
    persist: {
        setOptions: (options: Partial<import("zustand/middleware").PersistOptions<FilterStore, FilterStore>>) => void;
        clearStorage: () => void;
        rehydrate: () => Promise<void> | void;
        hasHydrated: () => boolean;
        onHydrate: (fn: (state: FilterStore) => void) => () => void;
        onFinishHydration: (fn: (state: FilterStore) => void) => () => void;
        getOptions: () => Partial<import("zustand/middleware").PersistOptions<FilterStore, FilterStore>>;
    };
}>;
export default useFilters;
//# sourceMappingURL=useFilters.d.ts.map