import { Classification } from '@tripudiotech/admin-api';
import { IClassificationDetail } from 'ui-common';
export type ClassificationDetailStore = {
    classification: Record<string, IClassificationDetail> | null;
    schemaClassification: Record<string, Classification[]>;
    isLoading: Record<string, boolean>;
    isLoaded: boolean;
    getClassification: (name: any, signal?: any) => any;
    fetchClassification: (name: any, signal?: any) => Promise<any>;
    getSchemaClassification: (entityType: string) => Promise<Classification[]>;
};
declare const useClassificationDetail: import("zustand").UseBoundStore<import("zustand").StoreApi<ClassificationDetailStore>>;
export default useClassificationDetail;
//# sourceMappingURL=useClassificationDetail.d.ts.map