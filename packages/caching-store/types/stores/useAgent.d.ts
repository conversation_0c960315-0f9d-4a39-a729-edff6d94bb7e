import { User } from '@tripudiotech/admin-api';
type AgentStore = {
    teams: [];
    companies: [];
    departments: [];
    users: User[];
    agents: any[];
    setData: (data: any) => void;
    getAgents: () => Promise<any>;
    getUsers: () => Promise<void>;
};
declare const useAgent: import("zustand").UseBoundStore<import("zustand").StoreApi<AgentStore>>;
export default useAgent;
//# sourceMappingURL=useAgent.d.ts.map