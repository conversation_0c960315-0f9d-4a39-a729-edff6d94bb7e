.caching-store-variables:
  variables:
    PACKAGE_DIR: packages/caching-store

aws-stag-caching-store-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .caching-store-variables

aws-stag-caching-store-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .caching-store-variables
  needs:
    - aws-stag-caching-store-package

gcp-stag-caching-store-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .caching-store-variables

gcp-stag-caching-store-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .caching-store-variables
  needs:
    - gcp-stag-caching-store-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-caching-store-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .caching-store-variables

gcp-uat-caching-store-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .caching-store-variables
  needs:
    - gcp-uat-caching-store-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json