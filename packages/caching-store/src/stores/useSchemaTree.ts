/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { schemaUrls, fetch } from '@tripudiotech/admin-api';
import pick from 'lodash/pick';

/**
 * Schema tree is parsed and stored as HashMap for performance
 */
type SchemaStore = {
    schemaTreeMap: Record<string, any> | null;
    isLoaded: boolean;
    setSchemaTree: (schemaTree) => void;
    getSchemaTree: (force?: boolean) => Record<string, any>;
};

const useSchemaTree = create<SchemaStore>((set, get) => ({
    schemaTreeMap: null,
    isLoaded: false,
    setSchemaTree: (schemaTreeMap) => set({ schemaTreeMap }),
    getSchemaTree: async (force = false) => {
        if (get().isLoaded && !force) return get().schemaTreeMap;
        const rootSchema = await fetch({
            ...schemaUrls.getSchemaTree,
        });
        let flattenSchemaMap = {};
        flattenTree([rootSchema.data], null, [], flattenSchemaMap);
        set({ schemaTreeMap: flattenSchemaMap, isLoaded: true });
        return flattenSchemaMap;
    },
}));

const flattenTree = (nodes, parent, path, map) => {
    nodes.map((node) => {
        let newPath = [...path, node.name];
        map[node.name] = {
            parent: parent?.name,
            path: newPath,
            ...pick(node, [
                'abstract',
                'classifications',
                'description',
                'disabled',
                'displayName',
                'extendable',
                'id',
                'masterModel',
                'name',
                'system',
                'visible',
                'relations',
                'attributes',
            ]),
        };

        if (node.subTypes) {
            flattenTree(node.subTypes, node, newPath, map);
        }
    });
};

export default useSchemaTree;
