/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, schemaUrls } from '@tripudiotech/admin-api';
import keyBy from 'lodash/keyBy';

export interface PermissionRolesStore {
    permissionRoles: any[];
    permissionRolesMap: Record<string, any> | null;
    getPermissionRoles: (force?: Boolean) => Promise<any[]>;
}

const usePermissionRoles = create<PermissionRolesStore>((set, get) => ({
    permissionRoles: null,
    permissionRolesMap: null,

    getPermissionRoles: async (force = false) => {
        if (get().permissionRoles && !force) return [get().permissionRoles, get().permissionRolesMap];
        try {
            const {
                data: { data: roles },
            } = await fetch({
                ...schemaUrls.getPermissionRoles,
            });
            const rolesMap = keyBy(roles, 'name');

            set({
                permissionRolesMap: rolesMap,
                permissionRoles: roles,
            });
            return [roles, rolesMap];
        } catch (error) {
            console.error(error);
        }
    },
}));

export default usePermissionRoles;
