/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import isEmpty from 'lodash/isEmpty';
import sortBy from 'lodash/sortBy';
import { persist } from 'zustand/middleware';
import { schemaUrls, fetch as apiFetch } from '@tripudiotech/admin-api';
import { GET_UNIT_LIMIT_RANGES } from '@tripudiotech/admin-styleguide';
import { PersistStorage } from '../persist';

const isExpired = (date: number): boolean => {
    return date < Date.now().valueOf();
};

const getExpiredDate = (NoOfDays): number => {
    const expiredDate = new Date();
    expiredDate.setDate(expiredDate.getDate() + NoOfDays);
    return expiredDate.valueOf();
};

type UnitOfMeasureState = {
    quantityKind?: Record<string, any>;
    quantityUnit?: Record<string, any>;
    expiredAt?: number;
    updateQuantityUnit?: (key: string, value: string) => void;
    qdtUnitMapper: (groupId?: string, unit?: string) => string;
    qdtKindMapper: (groupId?: string) => string;
    setQuantityKind: (quantityKind: any[]) => void;
    getQuantityKind: () => void;
    getQuantityUnit: (groupId: string) => void;
};

const MAX_STORED_DATE = 30;

const convertArrayObjectToMap = (data: any[], keyName: string = 'uri'): Record<string, any> => {
    if (isEmpty(data) || !keyName) return {};

    return data.reduce((result: Record<string, any>, d: any) => ({ ...result, [d[keyName]]: d }), {});
};

const useUnitOfMeasure = create(
    persist<UnitOfMeasureState>(
        (set, get) => ({
            quantityKind: {},
            quantityUnit: {},
            expiredAt: 0,
            setQuantityKind: (quantityKind: any[]) => set({ quantityKind }),
            qdtUnitMapper: (kind, unit) => {
                return get().quantityUnit[kind]?.[unit]?.label;
            },
            qdtKindMapper: (kind) => {
                return get().quantityKind[kind]?.label;
            },
            updateQuantityUnit: (kindId: string, units: string) =>
                set((state) => ({
                    ...state,
                    quantityUnit: {
                        ...state.quantityUnit,
                        [kindId]: units,
                    },
                })),
            getQuantityKind: async () => {
                if (isEmpty(get().quantityKind)) {
                    apiFetch({
                        ...schemaUrls.getQuantityGroups,
                        url: `${schemaUrls.getQuantityGroups.url}?limit=${GET_UNIT_LIMIT_RANGES}`,
                    }).then((res) => {
                        const {
                            data: { data },
                        } = res;
                        set({
                            quantityKind: convertArrayObjectToMap(data),
                            expiredAt: getExpiredDate(MAX_STORED_DATE),
                        });
                    });
                }
            },
            getQuantityUnit: async (groupId) => {
                if (!get().quantityUnit[groupId]) {
                    apiFetch({
                        ...schemaUrls.getQuantityUnits,
                        url: `${schemaUrls.getQuantityUnits.url}?limit=${GET_UNIT_LIMIT_RANGES}`,
                        params: {
                            groupId: encodeURIComponent(groupId),
                        },
                    })
                        .then((res) => {
                            const {
                                data: { data },
                            } = res;
                            set((state) => ({
                                quantityUnit: {
                                    ...state.quantityUnit,
                                    [groupId]: convertArrayObjectToMap(sortBy(data, ['label'])),
                                },
                            }));
                        })
                        .catch((e) => {
                            // Log the error message
                            console.warn(e?.response?.message || e.message);
                        });
                }
            },
        }),
        {
            name: 'unit-of-measure-data',
            getStorage: () => PersistStorage,
            partialize: ({
                quantityKind,
                quantityUnit,
                expiredAt,
                updateQuantityUnit,
                qdtUnitMapper,
                qdtKindMapper,
                setQuantityKind,
                getQuantityKind,
                getQuantityUnit,
            }) => ({
                quantityKind,
                quantityUnit,
                expiredAt,
                updateQuantityUnit,
                qdtUnitMapper,
                qdtKindMapper,
                setQuantityKind,
                getQuantityKind,
                getQuantityUnit,
            }),
            onRehydrateStorage: (state) => {
                return (state, error) => {
                    if (error) return;
                    if (isExpired(state.expiredAt)) {
                        useUnitOfMeasure.persist.clearStorage();
                    }
                };
            },
            version: 1,
            migrate: (persistedState: any, version) => {
                if (version === 1) {
                    // Reset the old stored data
                    persistedState = {
                        ...persistedState,
                        quantityKind: {},
                        quantityUnit: {},
                        expiredAt: 0,
                    };
                }

                return persistedState;
            },
        }
    )
);

export default useUnitOfMeasure;
