/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { userService, fetch, entityUrls, User } from '@tripudiotech/admin-api';

type AgentStore = {
    teams: [];
    companies: [];
    departments: [];
    users: User[];
    agents: any[];
    setData: (data) => void;
    getAgents: () => Promise<any>;
    getUsers: () => Promise<void>;
};

const useAgent = create<AgentStore>((set, get) => ({
    teams: [],
    companies: [],
    departments: [],
    users: null,
    agents: null,
    setData: (data) => set(data),
    getUsers: async () => {
        try {
            if (get().users) return;

            const { data } = await userService.getUsers(0, 1000);
            set({ users: data.data[0] });
        } catch (e) {}
    },
    getAgents: async () => {
        try {
            if (get().agents) return get().agents;

            const { data } = await fetch({
                ...entityUrls.getListEntity,
                params: { entityType: 'agent' },
                qs: { limit: 1000 },
                skipToast: true,
            });
            set({ agents: data.data });
            return data.data;
        } catch (e) {}
    },
}));

export default useAgent;
