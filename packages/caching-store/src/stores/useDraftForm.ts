/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import remove from 'lodash/remove';

type DraftForm = {
    id: string;
    title: string;
    content: string;
    formValues: any;
    viewLocation: {
        contextPath: string;
        fullPath: string;
        query: string;
    };
    options: any;
};

type SideBar = {
    forms: DraftForm[];
    setForms: (forms: DraftForm[]) => void;
    addForm: (form: DraftForm) => void;
    removeForm: (formId: string) => void;
};

const DRAFT_FORM_KEY = 'draft-form';

const useDraftForm = create<SideBar>()(
    persist(
        (set, get) => ({
            forms: [],
            setForms: (forms: DraftForm[]) => {
                set({ forms });
            },
            addForm: (form: DraftForm) => {
                set({ forms: [...get().forms, form] });
            },
            removeForm: (formId) => {
                set({
                    forms: remove(get().forms, (form: DraftForm) => {
                        form.id === formId;
                    }),
                });
            },
            findFormById: (formId) => {
                return get().forms.find((form) => form.id === formId);
            },
        }),
        { name: DRAFT_FORM_KEY }
    )
);

export default useDraftForm;
