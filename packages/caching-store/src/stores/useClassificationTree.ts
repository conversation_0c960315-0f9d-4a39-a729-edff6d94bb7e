/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { classificationUrls, fetch } from '@tripudiotech/admin-api';
import pick from 'lodash/pick';
import { ClassificationTreeNode } from 'ui-common';

/**
 * Schema tree is parsed and stored as HashMap for performance
 */
type ClassificationTreeStore = {
    classificationTreeMap: Record<string, ClassificationTreeNode> | null;
    isLoaded: boolean;
    setClassificationTree: (schemaTree) => void;
    getClassificationTree: (force?: boolean) => Record<string, any>;
};

const useClassificationTree = create<ClassificationTreeStore>((set, get) => ({
    classificationTreeMap: null,
    isLoaded: false,
    setClassificationTree: (classificationTreeMap) => set({ classificationTreeMap }),
    getClassificationTree: async (force = false) => {
        if (get().isLoaded && !force) return get().classificationTreeMap;
        const rootClassification = await fetch({
            ...classificationUrls.getClassificationTree,
            qs: { show_number_entity: true },
        });
        let flattenSchemaMap = {};
        flattenTree(rootClassification.data, null, [], flattenSchemaMap);
        set({ classificationTreeMap: flattenSchemaMap, isLoaded: true });
        return flattenSchemaMap;
    },
}));

const flattenTree = (nodes, parent, path, map) => {
    nodes.map((node) => {
        let newPath = [...path, node.name];
        map[node.name] = {
            parent: parent?.name,
            path: newPath,
            ...pick(node, [
                'createdAt',
                'attributeOrder',
                'createdBy',
                'description',
                'disabled',
                'entities',
                'name',
                'system',
                'updatedAt',
                'subTypes',
            ]),
            allowAccessOverride:
                (map[parent?.name]?.allowAccessOverride ?? true) && (node?.allowAccessOverride ?? true),
            accessOverrideDisabledBy: !map[parent?.name]?.allowAccessOverride ? parent?.name : undefined,
        };

        if (node.subTypes) {
            flattenTree(node.subTypes, node, newPath, map);
        }
    });
};

export default useClassificationTree;
