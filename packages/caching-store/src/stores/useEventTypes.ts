/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { EventType } from 'ui-common';
import { create } from 'zustand';
import { fetch, entityUrls } from '@tripudiotech/admin-api';
import keyBy from 'lodash/keyBy';
import isEmpty from 'lodash/isEmpty';
export interface EventTypesStore {
    eventTypes: Record<string, EventType> | null;
    getEventTypes: () => Promise<Record<string, EventType>>;
}

const useEventTypes = create<EventTypesStore>((set, get) => ({
    eventTypes: {},
    schemaDetail: {},
    getEventTypes: async () => {
        if (!isEmpty(get().eventTypes)) return get().eventTypes;

        const { data: eventTypes }: { data: EventType[] } = await fetch({
            ...entityUrls.getAllEventType,
        });

        const eventTypesMap = keyBy(eventTypes, 'name');

        set({ eventTypes: eventTypesMap });
        return eventTypesMap;
    },
}));

export default useEventTypes;
