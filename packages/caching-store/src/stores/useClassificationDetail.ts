/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, classificationUrls, Classification, schemaUrls } from '@tripudiotech/admin-api';
import { IClassificationDetail } from 'ui-common';

export type ClassificationDetailStore = {
    classification: Record<string, IClassificationDetail> | null;
    schemaClassification: Record<string, Classification[]>;
    isLoading: Record<string, boolean>;
    isLoaded: boolean;
    getClassification: (name, signal?) => any;
    fetchClassification: (name, signal?) => Promise<any>;
    getSchemaClassification: (entityType: string) => Promise<Classification[]>;
};

const useClassificationDetail = create<ClassificationDetailStore>((set, get) => ({
    schemaClassification: {},
    classification: {},
    isLoading: {},
    isLoaded: false,
    fetchClassification: async (name, signal?) => {
        try {
            const { data } = await fetch({
                ...classificationUrls.getClassificationDetail,
                params: { name },
                signal,
            });
            set({ classification: { ...get().classification, [name]: data } });
            return data;
        } finally {
            set({ isLoading: { ...get().isLoading, [name]: false } });
            const pendingReqs = Object.values(get().isLoading).filter((loading) => loading);
            if (pendingReqs.length <= 1) {
                set({ isLoaded: true });
            }
        }
    },
    getClassification: async (name, signal?) => {
        const cachedClassification = get().classification[name];
        if (cachedClassification) {
            // stale while revalidate
            get().fetchClassification(name);
            return cachedClassification;
        }

        set({ isLoading: { ...get().isLoading, [name]: true }, isLoaded: false });
        const data = await get().fetchClassification(name, signal);
        return data;
    },
    getSchemaClassification: async (entityType: string) => {
        try {
            if (get().schemaClassification[entityType]) return get().schemaClassification[entityType];

            const { data } = await fetch({
                ...schemaUrls.getSchemaClassification,
                params: { entityType },
                qs: {
                    fields: 'permissions',
                },
            });
            set({
                schemaClassification: {
                    ...get().schemaClassification,
                    [entityType]: data,
                },
            });
            return data;
        } catch (err) {
        } finally {
        }
    },
}));

export default useClassificationDetail;
