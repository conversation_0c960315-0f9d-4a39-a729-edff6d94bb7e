/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type FilterStore = {
    appliedFilters: any[];
    setAppliedFilters: (filters: any) => void;
    addFilter: (filter) => void;
    removeFilter: (index: number) => void;
};

const FILTER_STORE_KEY = 'FILTER_STORE_KEY';

const useFilters = create<FilterStore>()(
    persist(
        (set, get) => ({
            appliedFilters: [],
            setAppliedFilters: (appliedFilters) => {
                set({ appliedFilters });
            },
            addFilter: (filter) => {
                set({ appliedFilters: [...get().appliedFilters, filter] });
            },
            removeFilter: (index) => {
                const newFilters = [...get().appliedFilters];
                newFilters.splice(index, 1);
                set({ appliedFilters: newFilters });
            },
        }),
        { name: FILTER_STORE_KEY }
    )
);

export default useFilters;
