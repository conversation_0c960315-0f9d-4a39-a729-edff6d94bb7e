/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import {
    fetch,
    entityUrls,
    DEFAULT_CLIENT_SIDE_LIMIT,
    SYSTEM_ENTITY_TYPE,
    SYSTEM_RELATION,
} from '@tripudiotech/admin-api';
import keyBy from 'lodash/keyBy';
import { DEFAULT_TABLE_PAGINATION_SIZE, queryBuilder } from 'ui-common';

export interface RolesStore {
    companies: any[];
    companiesMap: Record<string, any> | null;
    teams: any[];
    teamsMap: Record<string, any> | null;
    departments: any[];
    departmentsMap: Record<string, any> | null;
    getCompanies: (force?: Boolean) => Promise<any[]>;
    getTeams: (force?: Boolean) => Promise<any[]>;
    getDepartments: (force?: Boolean) => Promise<any[]>;
    getAll: () => Promise<void>;
    searchCompany: (searchText?: string) => Promise<any[]>;
    getDepartmentsByCompany: (companyId: string, searchText?: string) => Promise<any[]>;
    getTeamsByCompany: (companyId: string, searchText?: string) => Promise<any[]>;
    getCompaniesMapFromCompanies: (companies: any[]) => Record<string, any>;
}

const useOrg = create<RolesStore>((set, get) => ({
    companies: null,
    companiesMap: null,
    teams: null,
    teamsMap: null,
    departments: null,
    departmentsMap: null,
    getCompanies: async (force = false) => {
        if (get().companies && !force) return get().companies;
        try {
            const {
                data: { data },
            } = await fetch({
                ...entityUrls.getListEntity,
                params: {
                    entityType: SYSTEM_ENTITY_TYPE.COMPANY,
                },
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });
            const companiesMap = keyBy(data, 'id');

            set({ companiesMap: companiesMap, companies: data });
            return data;
        } catch (error) {
            console.error(error);
        }
    },
    getTeams: async (force = false) => {
        if (get().teams && !force) return get().teams;
        try {
            const {
                data: { data },
            } = await fetch({
                ...entityUrls.getListEntity,
                params: {
                    entityType: SYSTEM_ENTITY_TYPE.TEAM,
                },
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });
            const teamsMap = keyBy(data, 'id');

            set({ teamsMap: teamsMap, teams: data });
            return data;
        } catch (error) {
            console.error(error);
        }
    },
    getDepartments: async (force = false) => {
        if (get().departments && !force) return get().departments;
        try {
            const {
                data: { data },
            } = await fetch({
                ...entityUrls.getListEntity,
                params: {
                    entityType: SYSTEM_ENTITY_TYPE.DEPARTMENT,
                },
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });
            const departmentsMap = keyBy(data, 'id');

            set({ departmentsMap: departmentsMap, departments: data });
            return data;
        } catch (error) {
            console.error(error);
        }
    },
    getAll: async () => {
        get().getCompanies();
        get().getDepartments();
        get().getTeams();
    },
    searchCompany: async (searchText = '') => {
        try {
            const { data } = await fetch({
                ...entityUrls.getListEntity,
                params: {
                    entityType: SYSTEM_ENTITY_TYPE.COMPANY,
                },
                qs: {
                    limit: DEFAULT_TABLE_PAGINATION_SIZE,
                    query: JSON.stringify(queryBuilder.buildContainsQuery('name', searchText)),
                },
            });
            return data?.data ?? [];
        } catch (error) {
            console.error('Failed to fetch companies:', error);
        }
    },
    getDepartmentsByCompany: async (companyId: string, searchText = '') => {
        if (!companyId) {
            return [];
        }
        try {
            const { data } = await fetch({
                ...entityUrls.getEntityRelations,
                params: {
                    fromEntityId: companyId,
                    relationType: SYSTEM_RELATION.WORKS_FOR,
                    entityType: SYSTEM_ENTITY_TYPE.DEPARTMENT,
                },
                qs: {
                    reverse: true,
                    limit: DEFAULT_TABLE_PAGINATION_SIZE,
                    query: JSON.stringify(queryBuilder.buildContainsQuery('name', searchText)),
                },
            });
            return data?.data ?? [];
        } catch (error) {
            console.error('Failed to fetch departments:', error);
        }
    },
    getTeamsByCompany: async (companyId: string, searchText = '') => {
        if (!companyId) {
            return [];
        }
        try {
            const { data } = await fetch({
                ...entityUrls.getEntityRelations,
                params: {
                    fromEntityId: companyId,
                    relationType: SYSTEM_RELATION.WORKS_FOR,
                    entityType: SYSTEM_ENTITY_TYPE.TEAM,
                },
                qs: {
                    reverse: true,
                    limit: DEFAULT_TABLE_PAGINATION_SIZE,
                    query: JSON.stringify(queryBuilder.buildContainsQuery('name', searchText)),
                },
            });
            return data?.data ?? [];
        } catch (error) {
            console.error('Failed to fetch teams:', error);
        }
    },
    getCompaniesMapFromCompanies: (conpanies: any[]) => keyBy(conpanies, 'id'),
}));

export default useOrg;
