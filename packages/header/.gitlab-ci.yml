.header-variables:
  variables:
    PACKAGE_DIR: packages/header

aws-stag-header-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .header-variables

aws-stag-header-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .header-variables
  needs:
    - aws-stag-header-package

gcp-stag-header-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .header-variables

gcp-stag-header-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .header-variables
  needs:
    - gcp-stag-header-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-header-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .header-variables

gcp-uat-header-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .header-variables
  needs:
    - gcp-uat-header-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json