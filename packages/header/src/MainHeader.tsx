/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Link } from 'react-router-dom';
import { AppBar, IconButton, Toolbar, useMediaQuery, useTheme, Box, MainBurgerIcon } from 'ui-style';
import Logo from './assets/logo.webp';
import { useSideBar } from '@tripudiotech/admin-caching-store';

const MainHeader = () => {
    const theme = useTheme();
    const isTablet = useMediaQuery(theme.breakpoints.down('sm'));

    const [open, toggle] = useSideBar((state) => [state.open, state.toggle]);

    return (
        <>
            <Box
                sx={{
                    '& .MuiPaper-root': {
                        boxShadow: 'none',
                        minHeight: '56px',
                        height: '56px',
                        display: 'flex',
                        justifyContent: 'center',
                        backgroundColor: theme.palette.glide.background.normal.quarternary,
                    },
                }}
            >
                <AppBar sx={{ zIndex: 1201 }}>
                    <Toolbar disableGutters>
                        <IconButton
                            onClick={toggle}
                            sx={{ p: 0, mx: '12px', color: theme.palette.glide.text.normal.tertiary }}
                        >
                            <MainBurgerIcon
                                sx={{
                                    transform: `${open ? 'rotate(0deg)' : 'rotate(180deg)'}`,
                                    transition: 'all .2s',
                                }}
                            />
                        </IconButton>
                        {!isTablet && (
                            <Link to="/dashboard" style={{ marginRight: '24px' }}>
                                <img src={Logo} alt="GlideSysLM Platform" style={{ width: '168px' }} />
                            </Link>
                        )}
                        <Box
                            sx={{
                                display: 'flex',
                                mr: '16px',
                            }}
                        ></Box>
                    </Toolbar>
                </AppBar>
            </Box>
        </>
    );
};

export default MainHeader;
