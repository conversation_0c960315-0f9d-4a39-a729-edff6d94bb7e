/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';
import { AttributeType } from '@tripudiotech/admin-styleguide';

export const DEBOUNCE_THRESHOLD = 300;

export const OptionType = {
    Keyword: 'Keyword',
    Value: 'Value',
    Attribute: 'Attribute',
    Schema: 'Schema',
    SavedFilter: 'SavedFilter',
    History: 'History',
    AppliedFilter: 'AppliedFilter',
    Result: 'Result',
    Empty: 'Empty',
    Tip: 'Tip',
    RecentlyViewed: 'RecentlyViewed',
};

export const GROUP_LABEL = {
    [OptionType.SavedFilter]: 'Saved Filter',
    [OptionType.History]: 'History',
    [OptionType.Attribute]: 'Attribute',
    [OptionType.Schema]: 'Entity Type',
    [OptionType.AppliedFilter]: 'Applied Filter',
    [OptionType.Result]: 'Results',
    [OptionType.Tip]: 'Quick filter',
    [OptionType.RecentlyViewed]: 'Recently Viewed',
};

export const DEFAULT_OPTIONS = [
    {
        label: 'Search by Entity Type',
        group: GROUP_LABEL[OptionType.Tip],
        groupOrder: 1,
        type: OptionType.Tip,
        value: '/Type ',
    },
    {
        label: 'Search for Issues',
        group: GROUP_LABEL[OptionType.Tip],
        type: OptionType.Tip,
        value: SYSTEM_ENTITY_TYPE.ISSUE,
        item: {
            label: 'Issue',
            group: GROUP_LABEL[OptionType.Schema],
            type: OptionType.Schema,
            value: SYSTEM_ENTITY_TYPE.ISSUE,
            originalValue: SYSTEM_ENTITY_TYPE.ISSUE,
        },
        groupOrder: 1,
    },
    {
        label: 'Search for Change Requests',
        group: GROUP_LABEL[OptionType.Tip],
        type: OptionType.Tip,
        value: SYSTEM_ENTITY_TYPE.CHANGE_REQUEST,
        item: {
            label: 'Change Request',
            group: GROUP_LABEL[OptionType.Schema],
            type: OptionType.Schema,
            value: SYSTEM_ENTITY_TYPE.CHANGE_REQUEST,
            originalValue: SYSTEM_ENTITY_TYPE.CHANGE_REQUEST,
        },
        groupOrder: 1,
    },
    {
        label: 'Search for Change Orders',
        group: GROUP_LABEL[OptionType.Tip],
        type: OptionType.Tip,
        value: SYSTEM_ENTITY_TYPE.CHANGE_ORDER,
        item: {
            label: 'Change Order',
            group: GROUP_LABEL[OptionType.Schema],
            type: OptionType.Schema,
            value: SYSTEM_ENTITY_TYPE.CHANGE_ORDER,
            originalValue: SYSTEM_ENTITY_TYPE.CHANGE_ORDER,
        },
        groupOrder: 1,
    },
    {
        label: 'Search for Specifications',
        group: GROUP_LABEL[OptionType.Tip],
        type: OptionType.Tip,
        value: SYSTEM_ENTITY_TYPE.SPECIFICATION,
        item: {
            label: 'Specification',
            group: GROUP_LABEL[OptionType.Schema],
            type: OptionType.Schema,
            value: SYSTEM_ENTITY_TYPE.SPECIFICATION,
            originalValue: SYSTEM_ENTITY_TYPE.SPECIFICATION,
        },
        groupOrder: 1,
    },
];

export const FILTER_CONFIG = { keys: ['groupOrder', 'itemOrder', 'label', 'value', 'filterCompare'] };
export const KEYWORD_PREFIX = '/';
export const TYPE_KEYWORD = `${KEYWORD_PREFIX}Type`;
export const ATTRIBUTE_KEYWORD = `${KEYWORD_PREFIX}Attribute`;
export const SUPPORTED_ATTRIBUTE_TYPES = [
    AttributeType.STRING,
    AttributeType.TEXT,
    AttributeType.INTEGER,
    AttributeType.FLOAT,
    AttributeType.LONG,
];

export const SUPPORTED_QUICK_FILTERS = [
    {
        label: TYPE_KEYWORD,
        value: TYPE_KEYWORD,
        type: OptionType.Keyword,
        group: 'Type',
        itemOrder: 3,
        description: 'Filter by Entity Type',
    },
    {
        label: ATTRIBUTE_KEYWORD,
        value: ATTRIBUTE_KEYWORD,
        type: OptionType.Keyword,
        group: 'Attribute',
        itemOrder: 4,
        description: 'Filter by attributes of specific entity types',
    },
];
