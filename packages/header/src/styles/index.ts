/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { styled } from 'ui-style';

const PREFIX = 'EntityType';

export const classes = {
    box: `${PREFIX}-box`,
    searchContent: `${PREFIX}-searchContent`,
    contentShift: `${PREFIX}-contentShift`,
    fakeToolbar: `${PREFIX}-fakeToolbar`,
    link: `${PREFIX}-link`,
    drawer: `${PREFIX}-drawer`,
    selectedEntityType: `${PREFIX}-selectedEntityType`,
    fileImage: `${PREFIX}-fileImage`,
};

export const Container = styled('div')(({ theme }) => ({
    [`& .${classes.drawer}`]: {
        boxSizing: 'border-box',
        width: 262,
        backgroundColor: '#434950',
    },
    [`& .${classes.searchContent}`]: {
        padding: '2px 4px',
        backgroundColor: '#282C31',
        borderRadius: '8px',
        width: 215,
        height: 32,
        fontSize: 12,
        fontWeight: 500,
        display: 'flex',
    },
    [`& .${classes.selectedEntityType}`]: {
        backgroundColor: '#555C65 !important',
        border: 'solid 1px #A3AFBD !important',
        color: '#A3AFBD !important',
    },
    [`& .${classes.fileImage}`]: {
        width: '100px',
        marginTop: 10,
    },
}));
