/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    ATTRIBUTE_KEYWORD,
    GROUP_LABEL,
    KEYWORD_PREFIX,
    OptionType,
    SUPPORTED_ATTRIBUTE_TYPES,
    TYPE_KEYWORD,
} from '../constants';
import uniqBy from 'lodash/uniqBy';
import get from 'lodash/get';

export const getEmptyResultOption = (searchText) => {
    return [
        {
            label: `No results found for`,
            search: searchText,
            type: OptionType.Empty,
            group: GROUP_LABEL[OptionType.Result],
        },
    ];
};

export const isSavedFilterGroup = (group) => GROUP_LABEL[OptionType.SavedFilter] === group;
export const isAppliedFilterGroup = (group) => GROUP_LABEL[OptionType.AppliedFilter] === group;
export const isFilterGroup = (group) => isAppliedFilterGroup(group) || isSavedFilterGroup(group);

export const containsIgnoreCase = (leftString: string, rightString: string) =>
    leftString?.toLowerCase()?.includes(rightString?.toLowerCase());

export const isEntityTypeKeyword = (searchText) => {
    return containsIgnoreCase(searchText, TYPE_KEYWORD);
};

export const isAttributeKeyword = (searchText) => {
    return containsIgnoreCase(searchText, ATTRIBUTE_KEYWORD);
};

export const containsKeyword = (searchText) => containsIgnoreCase(searchText, KEYWORD_PREFIX);

export const isValidSearch = (searchText) => searchText && searchText.length >= 2 && !containsKeyword(searchText);

export const combineAppliedFilterOptions = (filters, appliedFilters) => {
    return appliedFilters.length > 0
        ? [
              {
                  value: 'appliedFilter',
                  label: 'Applied Filters',
                  group: GROUP_LABEL[OptionType.AppliedFilter],
                  filters: [...appliedFilters],
                  type: OptionType.AppliedFilter,
              },
              ...filters,
          ]
        : [...filters];
};

export const buildAttributeOptions = (appliedFilters: any[], schema: any) => {
    let attributes = getDynamicAttributes(appliedFilters, schema);
    const attributeOptions = attributes.map((attribute) => ({
        label: attribute.displayName || attribute.name,
        value: attribute.name,
        type: OptionType.Attribute,
        group: GROUP_LABEL[OptionType.Attribute],
        originalValue: attribute.name,
        attribute,
    }));
    return attributeOptions;
};

export const formatFilterText = (searchText: string): string => {
    return searchText
        .replace(/\s+/g, ' ')
        .replace(`${TYPE_KEYWORD} `, '')
        .replace(`${ATTRIBUTE_KEYWORD} `, '')
        .replace(ATTRIBUTE_KEYWORD, '')
        .replace(TYPE_KEYWORD, '');
};
function getDynamicAttributes(appliedFilters: any[], schema: any) {
    let attributes = [];
    appliedFilters.forEach((option) => {
        if (option.type === OptionType.Schema) {
            const schemaAttributes = Object.values(get(schema, [option.originalValue, 'attributes'], {})).filter(
                (attr: any) => attr.visible || attr.name === 'revision'
            );
            attributes = attributes.concat(schemaAttributes);
        }
    });
    attributes = uniqBy(attributes, 'name');
    return attributes.filter((attribute) => SUPPORTED_ATTRIBUTE_TYPES.includes(attribute.type));
}
