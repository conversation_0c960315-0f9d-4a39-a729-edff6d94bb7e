/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import * as yup from 'yup';
import get from 'lodash/get';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import {
    Checkbox,
    FormControlLabel,
    Grid,
    MenuItem,
    TextField,
    Typography,
    LocalizationProvider,
    DateTimePicker,
    DatePicker,
    AdapterDateFns,
} from 'ui-style';
import { Field } from 'formik';
import { format } from 'date-fns';
import { RichTextEditor, EntitySelect, AttributeType } from '@tripudiotech/admin-styleguide';

const buildRequireMessage = (displayName) => {
    return `${displayName} is required`;
};

const buildMinMessage = (displayName, minValue) => {
    return `${displayName} must be greater than or equal to ${minValue}`;
};

const buildMaxMessage = (displayName, maxValue) => {
    return `${displayName} must be less than or equal to ${maxValue}`;
};

const buildNumberTypeMessage = (displayName) => `${displayName} must be a number`;

const folderPaths = [{ name: 'OwnerFolder', label: 'Owner Folder' }];

export const formatDate = (date) => {
    try {
        return format(date, 'yyyy-MM-dd');
    } catch (err) {
        return date;
    }
};

export const formatDateTime = (date) => {
    try {
        return format(date, "yyyy-MM-dd'T'HH:mm:ss");
    } catch (err) {
        return date;
    }
};

export const buildValidationSchema = (entityAttributeList) =>
    yup.object().shape(
        entityAttributeList.reduce((prev, cur) => {
            const { name, type, nullable, hyperlink, constraint, displayName, unitOfMeasure } = cur;
            const enumRange = get(constraint, 'enumRange', []);
            const pattern = get(constraint, 'pattern', null);
            const minLength = get(constraint, 'minLength', null);
            const maxLength = get(constraint, 'maxLength', null);
            switch (type) {
                case AttributeType.LONG:
                case AttributeType.FLOAT:
                case AttributeType.INTEGER: {
                    let yupValidator = yup.number().typeError(buildNumberTypeMessage(displayName));

                    if (minLength) {
                        yupValidator = yupValidator.min(Number(minLength), buildMinMessage(displayName, minLength));
                    }
                    if (maxLength) {
                        yupValidator = yupValidator.max(Number(maxLength), buildMaxMessage(displayName, maxLength));
                    }

                    if (isNil(nullable) || Boolean(nullable)) {
                        yupValidator = yupValidator.nullable();
                    } else {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                    }

                    if (!isEmpty(unitOfMeasure)) {
                        return {
                            ...prev,
                            [name]: yupValidator,
                            [`${name}:unit`]: yup.string(),
                        };
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }

                case AttributeType.STRING:
                case AttributeType.TEXT: {
                    if (name === 'fileLocation') {
                        // treated different for entity type document
                        return {
                            ...prev,
                            file: yup.mixed().required('File is required'),
                        };
                    }
                    let yupValidator = yup.string();

                    if (isNil(nullable) || Boolean(nullable)) {
                        yupValidator = yupValidator.nullable();
                    } else {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                    }

                    if (enumRange.length > 0) {
                        yupValidator = yupValidator.oneOf(enumRange);

                        return {
                            ...prev,
                            [name]: yupValidator,
                        };
                    }
                    if (minLength) {
                        yupValidator = yupValidator.min(Number(minLength), buildMinMessage(displayName, minLength));
                    }
                    if (maxLength) {
                        yupValidator = yupValidator.max(Number(maxLength), buildMaxMessage(displayName, maxLength));
                    }

                    if (pattern) {
                        yupValidator = yupValidator.matches(
                            new RegExp(pattern, 'g'),
                            `Value does not match ${pattern}`
                        );
                    }

                    if (hyperlink) {
                        const regMatch =
                            /^((http|https):\/\/)?(www.)?(?!.*(http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+(\/)?.([\w\?[a-zA-Z-_%\/@?]+)*([^\/\w\?[a-zA-Z0-9_-]+=\w+(&[a-zA-Z0-9_]+=\w+)*)?$/;
                        yupValidator = yupValidator.matches(regMatch, `Value should be a valid url`);
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }

                case AttributeType.BOOLEAN: {
                    let yupValidator = yup.boolean();

                    if (isNil(nullable) || Boolean(nullable)) {
                        yupValidator = yupValidator.nullable();
                    } else {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }

                case AttributeType.DATE: {
                    let yupValidator = yup.string().nullable();

                    if (!isNil(nullable) && !Boolean(nullable)) {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }
                case AttributeType.DATE_TIME: {
                    let yupValidator = yup.string().nullable();

                    if (!isNil(nullable) && !Boolean(nullable)) {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }
                default:
                    return prev;
            }
        }, {})
    );

export const buildInitialValue = (entityAttributeList, defaultValues) =>
    entityAttributeList.reduce((prev, cur) => {
        if (cur.type === AttributeType.DATE) {
            const defaultDate = defaultValues
                ? defaultValues[cur.name]
                : cur.defaultValue
                ? formatDate(Date.parse(cur.defaultValue))
                : null;
            return {
                ...prev,
                [cur.name]: defaultDate,
            };
        }
        if (cur.type === AttributeType.DATE_TIME) {
            const defaultDate = defaultValues
                ? defaultValues[cur.name]
                : cur.defaultValue
                ? formatDateTime(Date.parse(cur.defaultValue))
                : null;
            return {
                ...prev,
                [cur.name]: defaultDate,
            };
        }

        if ([AttributeType.LONG, AttributeType.FLOAT, AttributeType.INTEGER].includes(cur.type) && cur.unitOfMeasure) {
            return {
                ...prev,
                [cur.name]: defaultValues ? defaultValues[cur.name] : cur.defaultValue,
                [`${cur.name}:unit`]: get(cur, 'unitOfMeasure.quantityUnit', ''),
            };
        }

        return {
            ...prev,
            [cur.name]: defaultValues ? defaultValues[cur.name] : cur.defaultValue,
            filePath: folderPaths[0].name,
        };
    }, {});

export const buildFormItem = (attribute, setFieldValue, size: 'small' | 'medium' = 'medium') => {
    const { id, displayName, name, description, nullable, unitOfMeasure } = attribute;
    const hasUnitOfMeasure = unitOfMeasure && !isEmpty(unitOfMeasure);
    const isRequired = isNil(nullable) ? false : !Boolean(nullable);

    if (attribute.richText) {
        return (
            <Grid item xs={12} key={id}>
                <Field
                    fullWidth
                    variant="outlined"
                    id={id}
                    label={displayName}
                    name={name}
                    helperText={description}
                    rows={4}
                    disabled={false}
                    required={isRequired}
                    component={RichTextEditor}
                    size={size}
                />
            </Grid>
        );
    }

    switch (attribute.type) {
        case AttributeType.INTEGER:
        case AttributeType.LONG:
        case AttributeType.FLOAT:
            return (
                <Grid item key={id} xs={6}>
                    <Field
                        fullWidth
                        variant="outlined"
                        component={TextField}
                        label={displayName}
                        required={isRequired}
                        name={attribute.name}
                        disabled={false}
                        helperText={attribute.description}
                        InputLabelProps={{ shrink: true }}
                        size={size}
                    />
                </Grid>
            );

        case AttributeType.BOOLEAN:
            return (
                <Grid item key={id} xs={12}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={field.value}
                                                onChange={(e) => setFieldValue(name, e.target.checked)}
                                                name={name}
                                                required={isRequired}
                                            />
                                        }
                                        label={displayName}
                                    />
                                    {isErr && (
                                        <Typography
                                            color="error"
                                            sx={{ fontSize: '0.75rem', fontWeight: 400, marginLeft: '14px' }}
                                        >
                                            {error}
                                        </Typography>
                                    )}
                                </>
                            );
                        }}
                    </Field>
                </Grid>
            );

        case AttributeType.STRING: {
            const dataType = get(attribute, ['constraint', 'dataType'], null);
            const enumRange = get(attribute, ['constraint', 'enumRange'], []);

            if (dataType) {
                return (
                    <Grid item xs={12} sm={6} key={id}>
                        <Field name={name} helperText={description}>
                            {({ field, form, meta: { error, touched } }) => {
                                const isErr = touched && !!error;
                                return (
                                    <>
                                        <EntitySelect
                                            label={displayName}
                                            entityType={dataType}
                                            onChange={(newValue) => {
                                                setFieldValue(name, newValue.value);
                                            }}
                                            defaultValue={field.value}
                                            onBlur={() => form.setFieldTouched(name)}
                                        />
                                        {isErr && (
                                            <Typography
                                                color="error"
                                                sx={{ fontSize: '0.75rem', fontWeight: 400, marginLeft: '14px' }}
                                            >
                                                {error}
                                            </Typography>
                                        )}
                                    </>
                                );
                            }}
                        </Field>
                    </Grid>
                );
            }

            if (enumRange.length > 0) {
                return (
                    <Grid item xs={12} sm={6} key={id}>
                        <Field
                            fullWidth
                            select
                            type="text"
                            variant="outlined"
                            name={name}
                            label={displayName}
                            required={isRequired}
                            helperText={description}
                            component={TextField}
                            InputLabelProps={{ shrink: true }}
                            disabled={false}
                            size={size}
                            onChange={(e) => {
                                setFieldValue(name, e.target.value);
                            }}
                        >
                            {enumRange.map((enumValue) => (
                                <MenuItem value={enumValue} key={enumValue}>
                                    {enumValue}
                                </MenuItem>
                            ))}
                        </Field>
                    </Grid>
                );
            }

            if (attribute.name === 'fileLocation') {
                return (
                    <Grid item key={id} sx={{ width: '100%' }}>
                        <Grid item xs={12}>
                            <Field
                                fullWidth
                                select
                                type="text"
                                variant="outlined"
                                name="filePath"
                                label="Folder Path"
                                size={size}
                                required={isRequired}
                                component={TextField}
                                InputLabelProps={{ shrink: true }}
                                helperText={description}
                            >
                                {folderPaths.map((folder) => (
                                    <MenuItem value={folder.name} key={folder.name}>
                                        {folder.label}
                                    </MenuItem>
                                ))}
                            </Field>
                        </Grid>
                    </Grid>
                );
            } else {
                return (
                    <Grid item xs={12} sm={6} key={id}>
                        <Field
                            fullWidth
                            variant="outlined"
                            component={TextField}
                            required={isRequired}
                            label={displayName}
                            name={name}
                            size={size}
                            helperText={description}
                            disabled={false}
                            InputLabelProps={{ shrink: true }}
                        />
                    </Grid>
                );
            }
        }

        case AttributeType.TEXT: {
            return (
                <Grid item xs={12} sm={12} key={id}>
                    <Field
                        fullWidth
                        multiline
                        variant="outlined"
                        component={TextField}
                        required={isRequired}
                        label={displayName}
                        name={name}
                        size={size}
                        helperText={description}
                        disabled={false}
                        InputLabelProps={{ shrink: true }}
                        sx={{
                            textarea: {
                                resize: 'both',
                                width: '100%',
                                padding: '0px !important',
                                minHeight: '50px',
                            },
                        }}
                    />
                </Grid>
            );
        }

        case AttributeType.DATE:
            return (
                <Grid item xs={12} sm={6} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterDateFns}>
                                    <DatePicker
                                        label={displayName}
                                        value={field.value}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatDate(newValue), true);
                                        }}
                                        disabled={false}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                size={size}
                                                InputLabelProps={{ shrink: true }}
                                                error={isErr}
                                                required={isRequired}
                                                helperText={isErr ? error : description}
                                                fullWidth
                                                onBlur={() => form.setFieldTouched(name)}
                                            />
                                        )}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.DATE_TIME:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterDateFns}>
                                    <DateTimePicker
                                        label={displayName}
                                        value={field.value}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatDateTime(newValue));
                                        }}
                                        renderInput={(params) => {
                                            return (
                                                <TextField
                                                    {...params}
                                                    error={isErr}
                                                    InputLabelProps={{ shrink: true }}
                                                    required={isRequired}
                                                    helperText={isErr ? error : description}
                                                    fullWidth
                                                    size={size}
                                                    onBlur={() => form.setFieldTouched(name)}
                                                />
                                            );
                                        }}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );

        default:
            return null;
    }
};
