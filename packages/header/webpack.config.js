const { merge } = require('webpack-merge');
const singleSpaDefaults = require('webpack-config-single-spa-react-ts');

module.exports = (webpackConfigEnv, argv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: 'tripudiotech',
        projectName: 'header',
        webpackConfigEnv,
        argv,
    });

    return merge(defaultConfig, {
        devtool: false,
        // modify the webpack config however you'd like to by adding to this object
        module: {
            rules: [
                {
                    test: /.m?js/,
                    resolve: {
                        fullySpecified: false,
                    },
                },
            ],
        },
    });
};
