{"name": "@tripudiotech/admin-header", "version": "1.0.0", "scripts": {"start": "webpack serve --port 9001", "start:standalone": "webpack serve --env standalone", "build": "concurrently npm:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "test": "cross-env BABEL_ENV=test jest", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage"}, "devDependencies": {"typescript": "5.7.3", "@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@types/testing-library__jest-dom": "^5.14.1", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "ts-config-single-spa": "^3.0.0", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@types/jest": "^27.0.1", "@types/react": "^17.0.19", "@types/react-dom": "^17.0.9", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "classnames": "^2.3.1", "dexie": "^3.2.2", "dexie-react-hooks": "^1.1.1", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "lodash": "^4.17.21", "match-sorter": "^6.3.4", "react": "^17.0.2", "react-dom": "^17.0.2", "react-dropzone": "^14.2.1", "react-icons": "^4.3.1", "reselect": "^4.1.5", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "tinycolor2": "^1.4.2", "ui-common": "npm:@tripudiotech/ui-common@^1.2.2", "ui-style": "npm:@tripudiotech/ui-style@^1.3.3", "yup": "^0.32.11"}, "types": "dist/tripudiotech-header.d.ts"}