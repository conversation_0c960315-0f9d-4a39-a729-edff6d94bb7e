/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo } from 'react';
import {
    Box,
    LoadingOverlay,
    IconButton,
    EditIcon,
    AnimatedPage,
    FixedHeightContainer,
    Typography,
    Button,
    DeleteIcon,
    OutlinedTabGroup,
    SchemaField,
    Chip,
    MainTooltip,
    Avatar,
    Breadcrumbs,
    Link as MuiLink,
} from 'ui-style';
import useLocalStore from '../store';
import { Link, Outlet, useMatch, useNavigate } from 'react-router-dom';
import get from 'lodash/get';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { fetch, entityUrls, getAvatarUrl, SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';
import { getCompanyType, isCompany } from '../utils/common';
import { formatDateTime } from 'ui-common';
import EditCompanyAction from '../components/EditCompanyAction';

const TABS = [
    {
        id: 'user',
        label: 'User',
        value: 'user',
        component: Link,
        to: 'user',
    },
    {
        id: 'team',
        label: 'Team',
        value: 'team',
        component: Link,
        to: 'team',
    },
    {
        id: 'department',
        label: 'Department',
        value: 'department',
        component: Link,
        to: 'department',
    },
];

const CompanyDetail = () => {
    const [company, agent, getCompany, getAgent, isLoading, clearStore, setIsLoading, setAgent] = useLocalStore(
        (state) => [
            state.company,
            state.agent,
            state.getCompany,
            state.getAgent,
            state.isLoading,
            state.clearStore,
            state.setIsLoading,
            state.setAgent,
        ]
    );
    const matched = useMatch('company/:id/:view/*');
    const teamOrDepMatched = useMatch('company/:id/:view/:agentId');
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const navigate = useNavigate();
    const createdBy = get(company, 'createdBy');
    const representative = get(company, 'properties.representative');
    const state = get(company, 'state.name');
    const [id, view] = useMemo(() => [get(matched, 'params.id'), get(matched, 'params.view')], [matched]);
    const agentId = useMemo(() => get(teamOrDepMatched, 'params.agentId', ''), [teamOrDepMatched]);

    const breadcrumbItems = useMemo(() => {
        let items = [
            {
                label: 'Company',
                to: '/company',
            },
        ];
        if (company) {
            items.push({
                label: get(company, 'properties.name'),
                to: `/company/${company.id}/user`,
            });
        }
        if (agent) {
            items.push({
                label: get(agent, 'properties.name'),
                to: `/company/${company.id}/${agent.properties.type}/${agent.id}`,
            });
        }
        return items;
    }, [agent, company]);
    const selectedEntity = useMemo(() => agent || company, [agent, company]);
    const deleteEntity = useCallback(async () => {
        try {
            setIsLoading(true);
            await fetch({
                ...entityUrls.deleteEntity,
                params: {
                    entityId: selectedEntity.id,
                },
                successMessage: `Successfully deleted ${selectedEntity.properties.name}`,
            });
            if (isCompany(selectedEntity)) {
                navigate('/company');
            } else {
                navigate(`/company/${company.id}/${selectedEntity.properties.type}`);
            }
        } catch (err) {
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    }, [selectedEntity, company]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete Company',
            `Do you really want to delete <b>${selectedEntity?.properties?.name}</b>`,
            deleteEntity,
            'error'
        );
    }, [id, deleteEntity, selectedEntity]);

    useEffect(() => {
        const controller = new AbortController();
        getCompany(id, controller.signal);
    }, [id]);

    useEffect(() => {
        const controller = new AbortController();
        if (view && agentId) {
            getAgent(agentId, view, controller.signal);
        }

        return () => {
            setAgent(null);
        };
    }, [agentId, view]);

    useEffect(() => {
        return () => clearStore();
    }, []);

    return (
        <>
            {isLoading && <LoadingOverlay />}
            {selectedEntity && (
                <AnimatedPage>
                    <FixedHeightContainer>
                        <Box
                            sx={{
                                // height: '104px',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '14px',
                                padding: '12px 16px',
                                pb: 0,
                                borderBottom: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                            }}
                        >
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: '16px' }}>
                                <Box>
                                    <Breadcrumbs
                                        aria-label="breadcrumb"
                                        sx={{
                                            '& .breadcrumb-item': {
                                                color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                                            },
                                        }}
                                    >
                                        {breadcrumbItems.map((item) => (
                                            <MuiLink
                                                key={item.to}
                                                to={item.to}
                                                component={Link}
                                                style={{ textDecoration: 'none' }}
                                            >
                                                <Typography variant="label1-reg" className="breadcrumb-item">
                                                    {item.label}
                                                </Typography>
                                            </MuiLink>
                                        ))}
                                    </Breadcrumbs>
                                    <Box sx={{ display: 'flex', gap: '8px', alignItems: 'center', mt: '8px' }}>
                                        <Typography variant="title3">{selectedEntity?.properties?.name}</Typography>
                                    </Box>
                                </Box>
                                <Box sx={{ display: 'flex', gap: '8px' }}>
                                    <Button
                                        variant="outlined"
                                        onClick={onDelete}
                                        color="error"
                                        size="small"
                                        endIcon={<DeleteIcon />}
                                    >
                                        Delete
                                    </Button>
                                </Box>
                            </Box>
                            <OutlinedTabGroup
                                value={view}
                                variant="scrollable"
                                scrollButtons="auto"
                                allowScrollButtonsMobile
                                aria-label="Company navigation tabs"
                                items={TABS}
                                sx={{
                                    '& .MuiTab-root': {
                                        height: '29px',
                                    },
                                }}
                                size="small"
                            />
                        </Box>
                        <Box
                            sx={{
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                '& .generalInfo': {
                                    width: '468px',
                                    padding: '16px',
                                    borderRight: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                    display: 'flex',
                                    position: 'relative',
                                    flexDirection: 'column',
                                },
                                '& .edit-company-btn': {
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                },
                            }}
                        >
                            <div className="generalInfo">
                                <EditCompanyAction company={company} onSuccess={() => getCompany(id)} />
                                <Typography variant="ol1" sx={{ mb: '8px' }}>
                                    General Information
                                </Typography>
                                <SchemaField label="ID" value={get(selectedEntity, 'id')} />
                                <SchemaField label="Name" value={get(selectedEntity, 'properties.name')} />
                                <SchemaField label="Type" value={getCompanyType(selectedEntity)} />
                                <SchemaField
                                    label="State"
                                    value={
                                        <Chip
                                            size="small"
                                            label={state}
                                            variant={state === 'Active' ? 'status-success' : 'status-error'}
                                        />
                                    }
                                />
                                <SchemaField
                                    label="Description"
                                    value={get(selectedEntity, 'properties.description')}
                                />

                                <SchemaField
                                    label="Representative"
                                    value={
                                        <MainTooltip title={representative}>
                                            <Avatar
                                                style={{ width: '24px', height: '24px' }}
                                                alt={representative}
                                                src={representative ? getAvatarUrl(representative) : ''}
                                            />
                                        </MainTooltip>
                                    }
                                />
                                <SchemaField
                                    label="Created Date"
                                    value={formatDateTime(get(selectedEntity, 'createdAt'))}
                                />
                                <SchemaField
                                    label="Created By"
                                    value={
                                        <MainTooltip title={createdBy}>
                                            <Avatar
                                                style={{ width: '24px', height: '24px' }}
                                                alt={createdBy}
                                                src={createdBy ? getAvatarUrl(createdBy) : ''}
                                            />
                                        </MainTooltip>
                                    }
                                />
                            </div>
                            <Outlet />
                        </Box>
                    </FixedHeightContainer>
                </AnimatedPage>
            )}
        </>
    );
};

export default CompanyDetail;
