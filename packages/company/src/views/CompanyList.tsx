/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import {
    Box,
    ContentHeader,
    FixedHeightContainer,
    styled,
    AnimatedPage,
    tableIcons,
    tableStyles,
    Loading,
    Typography,
    AGGridTablePagination,
    AccountValueRenderer,
} from 'ui-style';
import { entityUrls, fetch, getAvatarUrl } from '@tripudiotech/admin-api';
import get from 'lodash/get';
import { AgGridReact } from '@ag-grid-community/react';
import { Link } from 'react-router-dom';
import CreateNewCompanyAction from '../components/CreateNewCompanyAction';
import { buildSortParams, buildQueryBasedOnFilter, formatDateTime, DEFAULT_TABLE_PAGINATION_SIZE } from 'ui-common';

const Actions = ({ onCreated }) => {
    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <CreateNewCompanyAction onCreated={onCreated} />
        </Box>
    );
};

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    width: '100%',
}));

const CompanyLinkRenderer = ({ value, data }) => {
    return (
        <Typography
            sx={{
                fontSize: '0.875rem',
                color: (theme) => theme.palette.info.main,
                alignItems: 'center',
                display: 'flex',
                height: '100%',
            }}
        >
            <Link to={`/company/${data.id}/user`} style={{ color: 'inherit' }}>
                {value}
            </Link>
        </Typography>
    );
};

const CompanyList = () => {
    const [totalRows, setTotalRows] = useState<number>(0);

    const gridRef = useRef<AgGridReact>(null);

    const handleSetDataSource = useCallback(() => {
        const buildParams = (params) => {
            const { sortModel, startRow, filterModel } = params.request;

            const queryParams = {
                offset: startRow || 0,
                limit: params.api.paginationGetPageSize(),
                ...buildSortParams(sortModel),
            };

            if (filterModel && Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            }
            return queryParams;
        };

        const dataSource = {
            getRows: (params) => {
                params.api.showLoadingOverlay();
                fetch({
                    ...entityUrls.getListEntity,
                    qs: buildParams(params),
                    params: {
                        entityType: 'company',
                    },
                })
                    .then((response: any) => {
                        if (response.status != 200) {
                            params.failCallback();
                            return;
                        }
                        const totalCount = response.data.pageInfo.total;
                        const rowsThisPage = response.data.data;

                        params.successCallback(rowsThisPage, totalCount);
                        setTotalRows(totalCount);
                    })
                    .catch((e) => {
                        // console.log(e)
                    })
                    .finally(() => {
                        gridRef.current?.api.hideOverlay();
                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };

        return dataSource;
    }, []);

    const onGridReady = useCallback(
        (e) => {
            const datasource = handleSetDataSource();
            e.api.setServerSideDatasource(datasource);
        },
        [handleSetDataSource]
    );

    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'serverSide',
            columnDefs: [
                {
                    field: 'id',
                    headerName: 'ID',
                    hide: true,
                },
                {
                    field: 'properties.name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: CompanyLinkRenderer,
                },
                {
                    field: 'properties.type',
                    headerName: 'Type',
                    filter: false,
                },
                {
                    field: 'properties.description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'properties.representative',
                    headerName: 'Representative',
                    minWidth: 110,
                    cellRenderer: AccountValueRenderer,
                    cellRendererParams: {
                        getAvatarUrl: getAvatarUrl,
                    },
                },
                {
                    field: 'createdAt',
                    headerName: 'Created At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                },
                {
                    field: 'createdBy',
                    headerName: 'Created By',
                    minWidth: 110,
                    cellRenderer: AccountValueRenderer,
                    cellRendererParams: {
                        getAvatarUrl: getAvatarUrl,
                    },
                },
            ],
            icons: tableIcons,
            pagination: true,
            rowSelection: 'single',
            onGridReady,
            // Pagination options
            suppressPaginationPanel: true,
            serverSideInfiniteScroll: true,
            cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
            paginationPageSize: DEFAULT_TABLE_PAGINATION_SIZE,
        };
    }, [onGridReady]);
    return (
        <AnimatedPage>
            <FixedHeightContainer>
                <ContentHeader
                    title="Company"
                    actionsRenderer={() => <Actions onCreated={() => gridRef.current.api.refreshServerSide()} />}
                />
                <ContentWrapper sx={{ ...tableStyles, '& .ag-paging-panel': { display: 'none' } }}>
                    <div className="ag-theme-alpine" style={{ width: '100%' }}>
                        <AgGridReact ref={gridRef} {...gridOptions} />
                    </div>
                </ContentWrapper>
                <AGGridTablePagination gridRef={gridRef} totalRows={totalRows} />
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default CompanyList;
