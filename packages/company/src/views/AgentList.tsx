/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
    styled,
    TextField,
    InputAdornment,
    SearchIcon,
    AnimatedPage,
    Typography,
    CloseIcon,
    Loading,
    tableIcons,
    tableStyles,
    AGGridTablePagination,
    AccountValueRenderer,
    Box,
} from 'ui-style';
import {
    formatDateTime,
    buildSortParams,
    buildQueryBasedOnFilter,
    DEFAULT_TABLE_PAGINATION_SIZE,
    queryBuilder,
} from 'ui-common';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { getAvatarUrl, fetch, entityUrls, SYSTEM_RELATION, SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';
import { AgGridReact } from '@ag-grid-community/react';
import { Link, useParams } from 'react-router-dom';
import { useDebounce } from 'usehooks-ts';
import CreateNewUserAction from '../components/CreateNewAgentAction';
import AddUserAction from '../components/AddUserAction';

const Wrapper = styled('div')(({ theme }) => ({
    height: '100%',
    width: '100%',
    display: 'flex',
    '& .member': {
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        padding: '16px 0',
        paddingBottom: 0,
        flexGrow: 1,
    },
    '& .user-table': {
        marginTop: '16px',
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        ...tableStyles,
        '& .ag-paging-panel': { display: 'none' },
    },
    '& .topBar': {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        '& .search': {
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
        },
        marginRight: '16px',
    },
}));

const UserLinkRenderer = ({ value, data }) => {
    const type = data.properties.type;
    return (
        <Typography
            sx={{
                fontSize: '0.875rem',
                color: (theme) => theme.palette.info.main,
                alignItems: 'center',
                display: 'flex',
                height: '100%',
            }}
        >
            <Link to={type === SYSTEM_ENTITY_TYPE.PERSON ? `/user/${data.id}` : data.id} style={{ color: 'inherit' }}>
                {value}
            </Link>
        </Typography>
    );
};

const PERSON_COLUMN_DEFS = [
    {
        field: 'properties.name',
        headerName: 'Name',
        filter: 'agTextColumnFilter',
        cellRenderer: UserLinkRenderer,
    },
    {
        field: 'properties.email',
        headerName: 'Email',
        filter: 'agTextColumnFilter',
    },
    {
        field: 'createdAt',
        headerName: 'Created At',
        valueGetter: (params) => {
            return formatDateTime(get(params.data, 'updatedAt'));
        },
    },
    {
        field: 'createdBy',
        headerName: 'Created By',
        minWidth: 110,
        cellRenderer: AccountValueRenderer,
        cellRendererParams: {
            getAvatarUrl: getAvatarUrl,
        },
    },
];
const COMMON_COLUMN_DEFS = [
    {
        field: 'properties.name',
        headerName: 'Name',
        filter: 'agTextColumnFilter',
        cellRenderer: UserLinkRenderer,
    },
    {
        field: 'createdAt',
        headerName: 'Created At',
        valueGetter: (params) => {
            return formatDateTime(get(params.data, 'updatedAt'));
        },
    },
    {
        field: 'createdBy',
        headerName: 'Created By',
        minWidth: 110,
        cellRenderer: AccountValueRenderer,
        cellRendererParams: {
            getAvatarUrl: getAvatarUrl,
        },
    },
];

const AgentList = ({ type, isCompanyView = false }) => {
    const [searchText, setSearchText] = useState('');
    const debouncedSearchText = useDebounce(searchText, 400);
    const searchRef = useRef('');
    const { id } = useParams();

    const [totalRows, setTotalRows] = useState<number>(0);
    const gridRef = useRef<AgGridReact>(null);

    const onSearchChanged = useCallback((e) => {
        const { value } = e.target;
        setSearchText(value);
    }, []);

    const buildSearchQuery = useCallback(
        (search) => {
            const searchText = search || '';
            return queryBuilder.buildAndOperatorQuery([
                queryBuilder.buildExactQuery(`relation.WORKS_FOR.id`, id),
                queryBuilder.buildOrOperatorQuery([
                    queryBuilder.buildContainsQuery('name', searchText),
                    queryBuilder.buildContainsQuery('description', searchText),
                ]),
            ]);
        },
        [id]
    );

    const handleSetDataSource = useCallback(() => {
        const buildParams = (params) => {
            const { sortModel, startRow, filterModel } = params.request;

            const queryParams = {
                offset: startRow || 0,
                limit: params.api.paginationGetPageSize(),
                ...buildSortParams(sortModel),
            };

            if (Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                if (searchRef.current) {
                    filterConditions.push(buildSearchQuery(searchRef.current));
                }
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else if (searchRef.current) {
                queryParams['query'] = JSON.stringify(buildSearchQuery(searchRef.current));
            } else {
                queryParams['query'] = JSON.stringify(queryBuilder.buildExactQuery(`relation.WORKS_FOR.id`, id));
            }
            return queryParams;
        };

        const dataSource = {
            getRows: (params) => {
                params.api.showLoadingOverlay();
                fetch({
                    ...entityUrls.getListEntity,
                    params: {
                        entityType: type,
                    },
                    qs: {
                        reverse: true,
                        ...buildParams(params),
                    },
                })
                    .then((response: any) => {
                        if (response.status != 200) {
                            params.failCallback();
                            return;
                        }
                        const totalCount = response.data.pageInfo.total;
                        const rowsThisPage = response.data.data;

                        params.successCallback(rowsThisPage, totalCount);
                        setTotalRows(totalCount);
                    })
                    .catch((e) => {
                        // console.log(e)
                    })
                    .finally(() => {
                        gridRef.current?.api.hideOverlay();
                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };

        return dataSource;
    }, [id, type]);

    const onGridReady = useCallback(
        (e) => {
            const datasource = handleSetDataSource();
            e.api.setServerSideDatasource(datasource);
        },
        [handleSetDataSource]
    );

    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'serverSide',
            columnDefs: type === SYSTEM_ENTITY_TYPE.PERSON ? PERSON_COLUMN_DEFS : COMMON_COLUMN_DEFS,
            icons: tableIcons,
            pagination: true,
            rowSelection: 'single',
            onGridReady,
            // Pagination options
            suppressPaginationPanel: true,
            serverSideInfiniteScroll: true,
            cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
            paginationPageSize: DEFAULT_TABLE_PAGINATION_SIZE,
        };
    }, [onGridReady, type, id]);

    useEffect(() => {
        searchRef.current = debouncedSearchText;
        if (gridRef.current?.api) {
            gridRef.current.api.refreshServerSide();
        }
    }, [debouncedSearchText]);

    const onRefresh = useCallback(() => gridRef.current.api.refreshServerSide(), []);
    const label = type === SYSTEM_ENTITY_TYPE.PERSON ? 'User' : type;

    return (
        <AnimatedPage style={{ height: '100%', width: '100%' }}>
            <Wrapper key={type}>
                <div className="member">
                    <div className="topBar">
                        <div className="search">
                            <Typography variant="ol1" sx={{ mx: '16px' }}>
                                {label}
                            </Typography>
                            <TextField
                                className="searchBox"
                                size="small"
                                value={searchText}
                                onChange={onSearchChanged}
                                placeholder="Type to search"
                                sx={{ mx: '16px', '& .MuiOutlinedInput-input': { paddingLeft: '4px !important' } }}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon />
                                        </InputAdornment>
                                    ),
                                    endAdornment: (
                                        <InputAdornment
                                            position="end"
                                            style={{
                                                visibility: isEmpty(searchText) ? 'hidden' : 'visible',
                                                cursor: 'pointer',
                                            }}
                                            onClick={() => setSearchText('')}
                                        >
                                            <CloseIcon sx={{ width: '16px', height: '16px' }} />
                                        </InputAdornment>
                                    ),
                                }}
                            />
                        </div>
                        <Box sx={{ display: 'flex', gap: '8px' }}>
                            {isCompanyView && <CreateNewUserAction onRefresh={onRefresh} companyId={id} type={type} />}
                            <AddUserAction onRefresh={onRefresh} type={type} />
                        </Box>
                    </div>
                    <div className="user-table">
                        <div className="ag-theme-alpine" style={{ width: '100%', height: '100%' }}>
                            <AgGridReact ref={gridRef} {...gridOptions} />
                        </div>
                        <AGGridTablePagination gridRef={gridRef} totalRows={totalRows} />
                    </div>
                </div>
            </Wrapper>
        </AnimatedPage>
    );
};

export default AgentList;
