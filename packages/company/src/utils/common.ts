/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import { entityUrls, SYSTEM_RELATION, SYSTEM_ENTITY_TYPE, fetch } from '@tripudiotech/admin-api';
const MAPPING = {
    InternalCompany: 'Internal Company',
    ExternalCompany: 'External Company',
};
export const getCompanyType = (entity) => {
    const type = get(entity, 'properties.type');
    return MAPPING[type] || type;
};

export const isCompany = (entity) => {
    return [
        SYSTEM_ENTITY_TYPE.COMPANY,
        SYSTEM_ENTITY_TYPE.INTERNAL_COMPANY,
        SYSTEM_ENTITY_TYPE.EXTERNAL_COMPANY,
    ].includes(get(entity, 'properties.type'));
};

export const handleTeamOrDepChanged = (assignedValues, newValues, userEntityId) => {
    let requests = [];
    const removedList = assignedValues.filter(
        (assignedValue) => !newValues.some((newValue) => newValue.id === assignedValue.id)
    );
    const addedList = newValues.filter(
        (newValue) => !assignedValues.some((assignedValue) => newValue.id === assignedValue.id)
    );

    removedList.forEach((team) => {
        requests.push(
            fetch({
                ...entityUrls.deleteRelation,
                params: {
                    fromEntityId: userEntityId,
                    relationType: SYSTEM_RELATION.WORKS_FOR,
                    toEntityId: team.id,
                },
            })
        );
    });
    addedList.forEach((team) => {
        requests.push(
            fetch({
                ...entityUrls.createRelation,
                params: {
                    fromEntityId: userEntityId,
                    relationType: SYSTEM_RELATION.WORKS_FOR,
                    toEntityId: team.id,
                },
            })
        );
    });
    return requests;
};
