/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { entityUrls, fetch, SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';

type LocalStore = {
    isLoading: boolean;
    company: any;
    getCompany: (id: string, signal?) => any;
    getAgent: (id: string, type: string, signal?) => any;
    clearStore: () => void;
    setIsLoading: (value: boolean) => void;
    setAgent: (value: any) => void;
    // department or team
    agent: any;
};

const initialStates = {
    isLoading: false,
    company: null,
    agent: null,
};

const useLocalStore = create<LocalStore>((set, get) => ({
    ...initialStates,
    setIsLoading: (isLoading: boolean) => set({ isLoading }),
    getCompany: async (id: string, signal?) => {
        set({ isLoading: true });
        const { data } = await fetch({
            ...entityUrls.getEntityById,
            params: {
                entityType: SYSTEM_ENTITY_TYPE.COMPANY,
                entityId: id,
            },
            signal,
        });
        set({ company: data, isLoading: false });
    },
    async getAgent(id, type, signal) {
        set({ isLoading: true });
        const { data } = await fetch({
            ...entityUrls.getEntityById,
            params: {
                entityType: type,
                entityId: id,
            },
            signal,
        });
        set({ agent: data, isLoading: false });
    },
    setAgent(value) {
        set({ agent: value });
    },
    clearStore: () => set({ ...initialStates }),
}));

export default useLocalStore;
