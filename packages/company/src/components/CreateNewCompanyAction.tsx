/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Box,
    Button,
    PlusIcon,
    RightTray,
    styled,
    Typography,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    Loading,
    LoadingOverlay,
} from 'ui-style';
import { useCallback, useRef, useMemo, useState, useEffect } from 'react';
import { fetch, entityUrls, SYSTEM_ENTITY_TYPE, AxiosResponse, RESPONSE_ERROR_TYPE } from '@tripudiotech/admin-api';
import { useSchemaDetail, useImport } from '@tripudiotech/admin-caching-store';
import { ISchemaDetail, AttributeSchema, schemaHelper, useToggle } from 'ui-common';
import { buildFormItem, buildInitialValue, buildValidationSchema } from '../utils/formBuilder';
import { Formik, Form } from 'formik';
import ImportableForm from './ImportableForm';

const FormContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 'auto',
    '& .generalInfo': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '12px',
    },
    '& .sectionLabel': {
        color: theme.palette.info.main,
    },
}));

const COMPANY_TYPE_OPTIONS = [
    {
        label: 'Internal Company',
        value: SYSTEM_ENTITY_TYPE.INTERNAL_COMPANY,
    },
    {
        label: 'External Company',
        value: SYSTEM_ENTITY_TYPE.EXTERNAL_COMPANY,
    },
];
const CreateNewCompanyAction = ({ onCreated }) => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);
    const [schema, getMultipleSchema, getAttributeName]: [Record<string, ISchemaDetail>, any, any] = useSchemaDetail(
        (state) => [state.schema, state.getMultipleSchema, state.getAttributeName]
    );
    const [selectedFile, setSelectedFile] = useState(null);
    const [useUpload, toggleUseUpload] = useToggle(false);
    const importFile = useImport((state) => state.import);
    const [selectedType, setSelectedType] = useState<string>('');
    const schemaDetail = useMemo(() => schema[selectedType], [selectedType, schema]);
    const isSchemaLoaded = schema[SYSTEM_ENTITY_TYPE.INTERNAL_COMPANY] && schema[SYSTEM_ENTITY_TYPE.EXTERNAL_COMPANY];
    const [loading, setLoading] = useState<boolean>(false);

    const attributes: AttributeSchema[] = useMemo(() => {
        if (schemaDetail) {
            return schemaHelper
                .getSchemaOrderedAttributes(schemaDetail)
                .filter((attribute: AttributeSchema) => attribute.visible && attribute.mutable);
        }
        return [];
    }, [schemaDetail]);

    const [initialValues, validationSchema] = useMemo(() => {
        return [buildInitialValue(attributes), buildValidationSchema(attributes)];
    }, [attributes]);

    useEffect(() => getMultipleSchema([SYSTEM_ENTITY_TYPE.INTERNAL_COMPANY, SYSTEM_ENTITY_TYPE.EXTERNAL_COMPANY]), []);

    const handleSubmit = useCallback(async (entityType, values) => {
        try {
            setLoading(true);
            await fetch({
                ...entityUrls.createEntity,
                params: {
                    entityType,
                },
                data: {
                    attributes: values,
                },
                successMessage: (
                    <span>
                        Company
                        <b> {values.name} </b>
                        has been created successfully
                    </span>
                ),
            });
            onCreated();
            openToggle.close();
        } catch (err) {
            const error: AxiosResponse = err.response;
            if (error.status === 400 && error.data?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE) {
                const invalidAttributeId = error.data.metadata.data;
                const invalidAttributeName = getAttributeName(schemaDetail, invalidAttributeId);
                if (invalidAttributeName && formRef.current) {
                    formRef.current.setFieldError(invalidAttributeName, error.data.errorMessage);
                }
            }
            return false;
        } finally {
            setLoading(false);
        }
    }, [schemaDetail]);

    const onDropAccepted = (acceptedFiles) => {
        const file = acceptedFiles[0];
        setSelectedFile(file);
    };

    useEffect(() => {
        if (!open) {
            setSelectedFile(null);
        }
    }, [open]);

    const onImport = async () => {
        setLoading(true);
        try {
            await importFile({ file: selectedFile, entityType: selectedType });
            openToggle.close();
        } catch (err) {
        } finally {
            setLoading(false);
        }
    };
    return (
        <>
            {loading && <LoadingOverlay />}
            <Button variant="contained" color="secondary" onClick={openToggle.open} size="small" endIcon={<PlusIcon />}>
                Create Company
            </Button>
            <RightTray
                title={`Create new company`}
                componentName={'create-new-user'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={() => {
                    if (useUpload) {
                        onImport();
                    }
                    formRef.current.submitForm();
                }}
                disabled={useUpload && !selectedFile}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <FormContainer>
                        {isSchemaLoaded ? (
                            <Formik
                                enableReinitialize
                                initialValues={initialValues}
                                validationSchema={validationSchema}
                                onSubmit={(values, { setSubmitting }) => {
                                    handleSubmit(selectedType, values);
                                    setSubmitting(false);
                                }}
                                innerRef={formRef}
                            >
                                {({ values, errors, setFieldValue }) => (
                                    <Form className="generalInfo">
                                        <Typography variant="label2-med" className="sectionLabel">
                                            General Information
                                        </Typography>
                                        <FormControl required fullWidth>
                                            <InputLabel id="type">Type</InputLabel>
                                            <Select
                                                label="Type"
                                                labelId="type"
                                                name="type"
                                                size="medium"
                                                value={selectedType}
                                                onChange={(e) => setSelectedType(e.target.value)}
                                            >
                                                {COMPANY_TYPE_OPTIONS.map(({ label, value }) => (
                                                    <MenuItem key={value} id={value} value={value}>
                                                        {label}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                        {selectedType && (
                                            <ImportableForm
                                                schemaDetail={schemaDetail}
                                                selectedFile={selectedFile}
                                                setSelectedFile={setSelectedFile}
                                                useUpload={useUpload}
                                                toggleUseUpload={toggleUseUpload}
                                            >
                                                <>
                                                    {attributes?.map((attribute) =>
                                                        buildFormItem(attribute, setFieldValue)
                                                    )}
                                                </>
                                            </ImportableForm>
                                        )}
                                    </Form>
                                )}
                            </Formik>
                        ) : (
                            <Loading />
                        )}
                    </FormContainer>
                </Box>
            </RightTray>
        </>
    );
};

export default CreateNewCompanyAction;
