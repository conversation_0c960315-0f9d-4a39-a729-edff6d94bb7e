/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    FormikTextField,
    LocalizationProvider,
    DatePicker,
    AdapterDateFns,
    LoadingOverlay,
    RichTextField,
    Autocomplete,
    styled,
    Typography,
    TextField,
} from 'ui-style';
import { Formik, Form, Field } from 'formik';
import { useEffect, useMemo, useState } from 'react';
import isNil from 'lodash/isNil';
import { AttributeType, formatDate, formatDateStringToDate } from '@tripudiotech/admin-styleguide';
import { useRoles, useOrg, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import * as yup from 'yup';
import {
    AxiosResponse,
    filterAndSortAttributesForEdit,
    RESPONSE_ERROR_TYPE,
    SYSTEM_ENTITY_TYPE,
} from '@tripudiotech/admin-api';
import ImportableForm from './ImportableForm';

const FormContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 'auto',
    '& .generalInfo': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '12px',
    },
    '& .sectionLabel': {
        color: theme.palette.info.main,
    },
}));

const DynamicAttributes = ({ attributes, setFieldValue, disableAttr }) => {
    return (
        <>
            {attributes.map((attribute: any) => {
                if (!attribute.visible) return;
                const { id, displayName, name, description, nullable } = attribute;
                const isRequired = isNil(nullable) ? false : !Boolean(nullable);
                if (attribute.richText) {
                    return (
                        <Field
                            key={id}
                            fullWidth
                            variant="outlined"
                            id={id}
                            label={displayName}
                            name={name}
                            helperText={description}
                            disabled={disableAttr.includes(name)}
                            required={isRequired}
                            InputLabelProps={{ shrink: true }}
                            component={RichTextField}
                        />
                    );
                }
                switch (attribute.type) {
                    case AttributeType.STRING:
                        return (
                            <Field
                                key={id}
                                fullWidth
                                variant="outlined"
                                component={FormikTextField}
                                required={isRequired}
                                label={displayName}
                                name={name}
                                helperText={description}
                                disabled={disableAttr.includes(name)}
                                InputLabelProps={{ shrink: true }}
                            />
                        );
                    case AttributeType.DATE:
                        return (
                            <Field name={name} helperText={description} key={id}>
                                {({ field, form, meta: { error, touched } }) => {
                                    const isErr = touched && !!error;
                                    return (
                                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                                            <DatePicker
                                                label={displayName}
                                                value={formatDateStringToDate(field.value, 'yyyy-MM-DD')}
                                                onChange={(newValue) => {
                                                    if (newValue?.toString() !== 'Invalid Date') {
                                                        setFieldValue(
                                                            name,
                                                            // Note: Don't change the format. Backend is expecting YYYY-MM-DD format. This format must match the value parsing format above
                                                            // Otherwise it breaks editing the date
                                                            formatDate(newValue?.toISOString(), 'yyyy-MM-DD'),
                                                            true
                                                        );
                                                    }
                                                }}
                                                disabled={disableAttr.includes(name)}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        InputLabelProps={{ shrink: true }}
                                                        error={isErr}
                                                        required={isRequired}
                                                        helperText={isErr ? error : description}
                                                        fullWidth
                                                        onBlur={(e) => {
                                                            form.setFieldTouched(name);
                                                            const newValue = e.target.value;
                                                            // set the value typed even if it is invalid so that invalid dates are captured and validated by yup
                                                            // and error message is shown
                                                            setFieldValue(name, newValue, true);
                                                        }}
                                                    />
                                                )}
                                                inputFormat="yyyy-MM-dd" // Note: different date format betweeen picker and moment. For moment, equivalent is yyyy-MM-DD
                                            />
                                        </LocalizationProvider>
                                    );
                                }}
                            </Field>
                        );
                }
            })}
        </>
    );
};

const UserForm = ({ formRef, handleSubmit, type, importableFormProps = {} }) => {
    const { roles, getRoles } = useRoles();
    const [userRoles, setUserRole] = useState([]);
    const [userDepartments, setUserDepartments] = useState([]);
    const [userTeams, setUserTeams] = useState([]);

    const [companies, departments, teams, getAll] = useOrg((state) => [
        state.companiesMap,
        state.departments,
        state.teams,
        state.getAll,
    ]);
    const [schemaMap, getSchema, getAttributeName] = useSchemaDetail((state) => [
        state.schema,
        state.getSchema,
        state.getAttributeName,
    ]);
    const agentSchema = useMemo(() => schemaMap[type], [type, schemaMap]);
    const attributes = useMemo(() => {
        return filterAndSortAttributesForEdit(
            Object.values(agentSchema?.attributes || {}),
            agentSchema?.entityType?.attributeOrder
        );
    }, [agentSchema]);
    const isPerson = useMemo(() => type === SYSTEM_ENTITY_TYPE.PERSON, [type]);
    const onRoleChange = (_, newValue) => {
        setUserRole(newValue);
    };

    const onDepartmentChange = (_, newValue) => {
        setUserDepartments(newValue);
    };

    const onTeamChange = (_, newValue) => {
        setUserTeams(newValue);
    };

    useEffect(() => {
        getRoles();
        getAll();
        getSchema(type);
    }, [type]);

    const validationSchema = useMemo(() => {
        if (!agentSchema) return null;
        const validateRule = {};
        attributes.forEach((attribute) => {
            if (!attribute.visible || attribute.nullable) return;
            if (!validateRule[attribute.name]) {
                validateRule[attribute.name] = yup.string().required(`${attribute.displayName} is required`);
            }
        });
        return yup.object().shape(validateRule);
    }, [agentSchema, isPerson]);

    const initialValues = useMemo(() => {
        if (!agentSchema) return null;
        const initValue = {
            firstName: '',
            companyId: '',
            lastName: '',
        };
        attributes.forEach((attribute: any) => {
            if (!attribute.visible) return;
            initValue[attribute.name] = '';
        });
        return initValue;
    }, [agentSchema]);

    const onError = (error: AxiosResponse) => {
        if (error.status === 400 && error.data?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE) {
            const invalidAttributeId = error.data.metadata.data;
            const invalidAttributeName = getAttributeName(agentSchema, invalidAttributeId);
            if (invalidAttributeName && formRef.current) {
                formRef.current.setFieldError(invalidAttributeName, error.data.errorMessage);
            }
        }
    };

    return (
        <>
            {(!companies || !agentSchema) && <LoadingOverlay />}
            <FormContainer>
                <Formik
                    enableReinitialize
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={(values, { setSubmitting }) => {
                        handleSubmit(values, userRoles, userDepartments, userTeams, onError);
                        setSubmitting(false);
                    }}
                    innerRef={formRef}
                >
                    {({ setFieldValue }) => (
                        <Form>
                            <div className="generalInfo">
                                <Typography variant="label2-med" className="sectionLabel">
                                    General Information
                                </Typography>
                                {isPerson && (
                                    <>
                                        <Autocomplete
                                            options={departments ? departments : []}
                                            multiple
                                            getOptionLabel={(option: any) => option?.properties?.name || option?.name}
                                            isOptionEqualToValue={(option: any, value: any) => option?.id === value?.id}
                                            id="department"
                                            value={userDepartments}
                                            onChange={onDepartmentChange}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    variant="outlined"
                                                    label="Department"
                                                    size="small"
                                                    InputLabelProps={{ shrink: true }}
                                                />
                                            )}
                                        />
                                        <Autocomplete
                                            options={teams ? teams : []}
                                            multiple
                                            getOptionLabel={(option: any) => option?.properties?.name || option?.name}
                                            isOptionEqualToValue={(option: any, value: any) => option?.id === value?.id}
                                            id="team"
                                            value={userTeams}
                                            onChange={onTeamChange}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    variant="outlined"
                                                    label="Team"
                                                    size="small"
                                                    InputLabelProps={{ shrink: true }}
                                                />
                                            )}
                                        />
                                    </>
                                )}
                                {isPerson ? (
                                    <DynamicAttributes
                                        attributes={attributes}
                                        setFieldValue={setFieldValue}
                                        disableAttr={[]}
                                    />
                                ) : (
                                    <ImportableForm schemaDetail={agentSchema} {...(importableFormProps as any)}>
                                        <DynamicAttributes
                                            attributes={attributes}
                                            setFieldValue={setFieldValue}
                                            disableAttr={[]}
                                        />
                                    </ImportableForm>
                                )}
                                {isPerson && (
                                    <Autocomplete
                                        options={roles ? roles : []}
                                        multiple
                                        getOptionLabel={(option: any) => option.name}
                                        isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
                                        id="role"
                                        value={userRoles}
                                        onChange={onRoleChange}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                variant="outlined"
                                                label="Role"
                                                size="small"
                                                InputLabelProps={{ shrink: true }}
                                            />
                                        )}
                                    />
                                )}
                            </div>
                        </Form>
                    )}
                </Formik>
            </FormContainer>
        </>
    );
};

export default UserForm;
