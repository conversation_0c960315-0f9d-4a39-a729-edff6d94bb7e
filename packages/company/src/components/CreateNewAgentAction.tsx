/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useRef, useState } from 'react';
import { useToggle } from 'ui-common';
import { Box, Button, PlusIcon, RightTray } from 'ui-style';
import UserForm from './UserForm';
import useLocalStore from '../store';
import { fetch, userUrls, entityUrls, SYSTEM_ENTITY_TYPE, SYSTEM_RELATION } from '@tripudiotech/admin-api';
import get from 'lodash/get';
import { handleTeamOrDepChanged } from '../utils/common';
import { useDialog, useImport } from '@tripudiotech/admin-caching-store';

function prepareRequest(data, roles) {
    const cloneObj = Object.assign({}, data);
    delete cloneObj['companyId'];
    return {
        firstName: data.firstName,
        lastName: data.lastName,
        username: data.email,
        description: data.description,
        properties: cloneObj,
        roles: roles.map((r) => r.name),
    };
}

const CreateNewAgentAction = ({ onRefresh, companyId, type }) => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);
    const [loading, setLoading] = useLocalStore((state) => [state.isLoading, state.setIsLoading]);
    const { onOpenDialog } = useDialog()
    const [selectedFile, setSelectedFile] = useState(null);
    const [useUpload, toggleUseUpload] = useToggle(false);
    const importFile = useImport((state) => state.import);

    const handleConfirmCreateEntity = (warningMessage, confirmRequest, onError) => {
        onOpenDialog('Review Warnings', warningMessage, async () => {
            try {
                await fetch(confirmRequest)
                onRefresh();
                openToggle.close();
            } catch (error) {
                onError && onError(error.response);
                console.error(error);
            } finally {
                setLoading(false);
            }
        }, 'info');
    }

    const handleSubmit = useCallback(
        async (values, roles, departments, teams, onError) => {
            try {
                setLoading(true);
                if (type === SYSTEM_ENTITY_TYPE.PERSON) {
                    const res = await fetch({
                        ...userUrls.createUser,
                        params: { companyId },
                        data: prepareRequest(values, roles),
                        successMessage: (
                            <span>
                                User <b>{values.firstName}</b> has been created successfully. An email has been sent to
                                the user to finish the registration.
                            </span>
                        ),
                    });
                    const userEntityId = get(res, 'data.id');
                    let reqs = [];
                    if (roles.length > 0) {
                        reqs.push(
                            fetch({
                                ...userUrls.assignRole,
                                params: { userId: userEntityId },
                                data: roles.map((r) => r.name),
                            })
                        );
                    }
                    reqs.concat(handleTeamOrDepChanged([], departments, userEntityId));
                    reqs.concat(handleTeamOrDepChanged([], teams, userEntityId));
                    await Promise.all(reqs);
                } else {
                    const res = await fetch({
                        ...entityUrls.createEntity,
                        params: {
                            entityType: type,
                        },
                        data: {
                            attributes: {
                                ...values,
                            },
                            relations: [
                                {
                                    name: SYSTEM_RELATION.WORKS_FOR,
                                    entityId: companyId,
                                    entityType: SYSTEM_ENTITY_TYPE.COMPANY,
                                },
                                {
                                    name: SYSTEM_RELATION.OWNED_BY,
                                    entityId: companyId,
                                    entityType: SYSTEM_ENTITY_TYPE.COMPANY,
                                },
                            ],
                        },
                    });

                    // Handle business rule warning
                    const rules = res.data?.businessRuleResults;
                    const warningHandShakeId = res.data?.otherParameters?.warningHandShakeId;
                    if (rules?.length > 0 && warningHandShakeId) {
                        const confirmRequest = {
                            ...entityUrls.createEntity,
                            params: {
                                entityType: type,
                            },
                            data: {
                                attributes: {
                                    ...values,
                                },
                                relations: [
                                    {
                                        name: SYSTEM_RELATION.WORKS_FOR,
                                        entityId: companyId,
                                        entityType: SYSTEM_ENTITY_TYPE.COMPANY,
                                    },
                                    {
                                        name: SYSTEM_RELATION.OWNED_BY,
                                        entityId: companyId,
                                        entityType: SYSTEM_ENTITY_TYPE.COMPANY,
                                    },
                                ],
                                otherParameters: {
                                    warningHandShakeId: warningHandShakeId,
                                },
                            },
                        }
                        const warningListHtml = `
                            <div>
                                <p> Some business rules were violated</p>
                                <ul style="margin: 4px;">
                                    ${rules.map((rule) => `<li>${rule.message}</li>`).join('')}
                                </ul>
                                <p><strong>Do you still want to proceed?</strong></p>
                            </div>
                        `;
                        handleConfirmCreateEntity(warningListHtml, confirmRequest, onError)
                        return;
                    }
                }
                onRefresh();
                openToggle.close();
            } catch (error) {
                onError && onError(error.response);
                console.error(error);
            } finally {
                setLoading(false);
            }
        },
        [companyId, type]
    );

    const label = `Create ${type === SYSTEM_ENTITY_TYPE.PERSON ? 'User' : type}`;
    const handleImport = async () => {
        setLoading(true);
        try {
            await importFile({ file: selectedFile, entityType: type });
            openToggle.close();
        } catch (err) {
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Button variant="contained" color="secondary" onClick={openToggle.open} size="small" endIcon={<PlusIcon />}>
                {label}
            </Button>
            <RightTray
                title={label}
                componentName={'create-new-agent'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Create"
                onConfirm={() => (useUpload ? handleImport() : formRef.current.submitForm())}
                disabled={useUpload && !selectedFile}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <UserForm
                        formRef={formRef}
                        importableFormProps={{
                            selectedFile,
                            setSelectedFile,
                            useUpload,
                            toggleUseUpload,
                        }}
                        handleSubmit={handleSubmit}
                        type={type}
                    />
                </Box>
            </RightTray>
        </>
    );
};

export default CreateNewAgentAction;
