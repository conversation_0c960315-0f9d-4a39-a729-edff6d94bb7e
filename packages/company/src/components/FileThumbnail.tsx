/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SvgIcon, SvgIconProps } from 'ui-style';

export const FileThumbnail = (props: SvgIconProps) => {
    return (
        <SvgIcon {...props} viewBox="0 0 24 24" fill="none">
            <g style={{ mixBlendMode: 'multiply' }} opacity="0.2">
                <g style={{ mixBlendMode: 'multiply' }} opacity="0.2">
                    <path
                        d="M21.8894 2.02148H6.69063C6.1625 2.02148 5.73438 2.44961 5.73438 2.97773V21.349C5.73438 21.8771 6.1625 22.3052 6.69063 22.3052H21.8894C22.4175 22.3052 22.8456 21.8771 22.8456 21.349V2.97773C22.8456 2.44961 22.4175 2.02148 21.8894 2.02148Z"
                        fill="#161616"
                    />
                </g>
            </g>
            <g style={{ mixBlendMode: 'multiply' }} opacity="0.12">
                <g style={{ mixBlendMode: 'multiply' }} opacity="0.12">
                    <path
                        d="M21.8894 2.02148H6.69063C6.1625 2.02148 5.73438 2.44961 5.73438 2.97773V21.349C5.73438 21.8771 6.1625 22.3052 6.69063 22.3052H21.8894C22.4175 22.3052 22.8456 21.8771 22.8456 21.349V2.97773C22.8456 2.44961 22.4175 2.02148 21.8894 2.02148Z"
                        fill="white"
                    />
                </g>
            </g>
            <path
                d="M14.6003 2.03132H6.72534C6.60082 2.02981 6.47726 2.05322 6.36193 2.10018C6.24659 2.14713 6.14182 2.21668 6.05376 2.30474C5.96571 2.39279 5.89616 2.49757 5.8492 2.6129C5.80224 2.72824 5.77883 2.8518 5.78034 2.97632V7.09382H14.6453L14.6003 2.03132Z"
                fill="#21A366"
            />
            <path
                d="M21.8896 2.03132H14.5996V7.09382H22.8459V2.96507C22.8444 2.84097 22.8185 2.71837 22.7696 2.60428C22.7208 2.49019 22.6499 2.38684 22.5611 2.30013C22.4723 2.21342 22.3673 2.14505 22.2521 2.09893C22.1369 2.05281 22.0137 2.02983 21.8896 2.03132Z"
                fill="#33C481"
            />
            <path d="M22.8346 12.1562H14.5996V17.23H22.8346V12.1562Z" fill="#107C41" />
            <path
                d="M14.5985 17.23V12.1562H5.73347V21.3475C5.73196 21.472 5.75537 21.5956 5.80233 21.7109C5.84928 21.8262 5.91883 21.931 6.00689 22.0191C6.09494 22.1071 6.19972 22.1767 6.31505 22.2236C6.43039 22.2706 6.55395 22.294 6.67847 22.2925H21.8885C22.0135 22.294 22.1376 22.2706 22.2536 22.2238C22.3695 22.177 22.475 22.1076 22.564 22.0197C22.6529 21.9318 22.7235 21.8271 22.7717 21.7118C22.8199 21.5964 22.8447 21.4726 22.8447 21.3475V17.23H14.5985Z"
                fill="#185C37"
            />
            <path d="M14.6106 7.08398H5.73438V12.1577H14.6106V7.08398Z" fill="#107C41" />
            <path d="M22.8346 7.08398H14.5996V12.1577H22.8346V7.08398Z" fill="#21A366" />
            <path
                d="M11.1114 6.45312H1.61641C1.08828 6.45312 0.660156 6.88125 0.660156 7.40937V16.9044C0.660156 17.4325 1.08828 17.8606 1.61641 17.8606H11.1114C11.6395 17.8606 12.0677 17.4325 12.0677 16.9044V7.40937C12.0677 6.88125 11.6395 6.45312 11.1114 6.45312Z"
                fill="#107C41"
            />
            <path
                d="M3.60645 15.25L5.5977 12.1563L3.7752 9.07375H5.24895L6.23895 11.0313C6.31429 11.1683 6.37825 11.3113 6.4302 11.4587C6.49241 11.3083 6.56379 11.1617 6.64395 11.02L7.76895 9.0625H9.11894L7.1952 12.1563L9.11894 15.2725H7.67895L6.55395 13.1013C6.49404 13.0137 6.44494 12.9193 6.4077 12.82C6.37029 12.9172 6.32515 13.0113 6.2727 13.1013L5.0802 15.2725L3.60645 15.25Z"
                fill="white"
            />
            <g style={{ mixBlendMode: 'soft-light' }} opacity="0.5">
                <path
                    style={{ mixBlendMode: 'soft-light' }}
                    opacity="0.5"
                    d="M11.1114 6.45312H1.61641C1.08828 6.45312 0.660156 6.88125 0.660156 7.40937V16.9044C0.660156 17.4325 1.08828 17.8606 1.61641 17.8606H11.1114C11.6395 17.8606 12.0677 17.4325 12.0677 16.9044V7.40937C12.0677 6.88125 11.6395 6.45312 11.1114 6.45312Z"
                    fill="url(#paint0_linear_4553_75420)"
                />
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_4553_75420"
                    x1="2.64016"
                    y1="5.71062"
                    x2="10.0877"
                    y2="18.6031"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stop-color="white" stop-opacity="0.5" />
                    <stop offset="1" stop-opacity="0.7" />
                </linearGradient>
            </defs>
        </SvgIcon>
    );
};

export default FileThumbnail;
