/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Box,
    IconButton,
    EditIcon,
    RightTray,
    styled,
    Typography,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    Loading,
    LoadingOverlay,
} from 'ui-style';
import { useCallback, useRef, useMemo, useState, useEffect } from 'react';
import { fetch, entityUrls, SYSTEM_ENTITY_TYPE } from '@tripudiotech/admin-api';
import { useSchemaDetail, useOrg } from '@tripudiotech/admin-caching-store';
import { ISchemaDetail, AttributeSchema, schemaHelper, useToggle } from 'ui-common';
import { buildFormItem, buildInitialValue, buildValidationSchema } from '../utils/formBuilder';
import { Formik, Form } from 'formik';
import get from 'lodash/get';

const FormContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 'auto',
    '& .generalInfo': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '12px',
    },
    '& .sectionLabel': {
        color: theme.palette.info.main,
    },
}));

const COMPANY_TYPE_OPTIONS = [
    {
        label: 'Internal Company',
        value: SYSTEM_ENTITY_TYPE.INTERNAL_COMPANY,
    },
    {
        label: 'External Company',
        value: SYSTEM_ENTITY_TYPE.EXTERNAL_COMPANY,
    },
];

const EditCompanyAction = ({ company, onSuccess }) => {
    const [open, openToggle] = useToggle();
    const formRef = useRef(null);
    const [schema, getMultipleSchema]: [Record<string, ISchemaDetail>, any] = useSchemaDetail((state) => [
        state.schema,
        state.getMultipleSchema,
    ]);
    const selectedType = get(company, 'properties.type');
    const schemaDetail = useMemo(() => schema[selectedType], [selectedType, schema]);
    const isSchemaLoaded = schema[SYSTEM_ENTITY_TYPE.INTERNAL_COMPANY] && schema[SYSTEM_ENTITY_TYPE.EXTERNAL_COMPANY];
    const [loading, setLoading] = useState<boolean>(false);
    const attributes: AttributeSchema[] = useMemo(() => {
        if (schemaDetail) {
            return schemaHelper
                .getSchemaOrderedAttributes(schemaDetail)
                .filter((attribute: AttributeSchema) => attribute.visible);
        }
        return [];
    }, [schemaDetail]);
    const { getCompanies } = useOrg();

    const [initialValues, validationSchema] = useMemo(() => {
        return [buildInitialValue(attributes, company?.properties), buildValidationSchema(attributes)];
    }, [attributes]);

    useEffect(() => getMultipleSchema([SYSTEM_ENTITY_TYPE.INTERNAL_COMPANY, SYSTEM_ENTITY_TYPE.EXTERNAL_COMPANY]), []);

    const handleSubmit = useCallback(
        async (values) => {
            try {
                setLoading(true);
                await fetch({
                    ...entityUrls.updateEntity,
                    params: {
                        entityId: company?.id,
                    },
                    data: {
                        attributes: values,
                    },
                    successMessage: (
                        <span>
                            Company
                            <b> {values.name} </b>
                            has been updated successfully
                        </span>
                    ),
                });
                onSuccess();
                openToggle.close();
                setLoading(false);
                getCompanies(true);
            } catch (err) {
                setLoading(false);
                return false;
            } finally {
            }
        },
        [company]
    );

    return (
        <>
            {loading && <LoadingOverlay />}
            <IconButton className="edit-company-btn" size="small" onClick={openToggle.open}>
                <EditIcon />
            </IconButton>
            <RightTray
                title={`Edit Company`}
                componentName={'edit-company'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Save"
                onConfirm={() => {
                    formRef.current.submitForm();
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <FormContainer>
                        {isSchemaLoaded ? (
                            <Formik
                                enableReinitialize
                                initialValues={initialValues}
                                validationSchema={validationSchema}
                                onSubmit={(values, { setSubmitting }) => {
                                    handleSubmit(values);
                                    setSubmitting(false);
                                }}
                                innerRef={formRef}
                            >
                                {({ values, errors, setFieldValue }) => (
                                    <Form className="generalInfo">
                                        <Typography variant="label2-med" className="sectionLabel">
                                            General Information
                                        </Typography>
                                        <FormControl required fullWidth>
                                            <InputLabel id="type">Type</InputLabel>
                                            <Select
                                                label="Type"
                                                labelId="type"
                                                name="type"
                                                size="medium"
                                                value={selectedType}
                                                readOnly
                                                disabled
                                            >
                                                {COMPANY_TYPE_OPTIONS.map(({ label, value }) => (
                                                    <MenuItem key={value} id={value} value={value}>
                                                        {label}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                        {attributes.map((attribute) => buildFormItem(attribute, setFieldValue))}
                                    </Form>
                                )}
                            </Formik>
                        ) : (
                            <Loading />
                        )}
                    </FormContainer>
                </Box>
            </RightTray>
        </>
    );
};

export default EditCompanyAction;
