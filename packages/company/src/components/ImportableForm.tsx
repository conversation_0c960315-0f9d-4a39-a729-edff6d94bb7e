/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import {
    Box,
    Button,
    Typography,
    Fade,
    RadioGroup,
    Radio,
    FormControlLabel,
    DocumentIcon,
    UploadIcon,
    EmptyDocument,
    IconButton,
    DeleteIcon,
} from 'ui-style';
import { ISchemaDetail, schemaHelper } from 'ui-common';
import { CSVLink } from 'react-csv';
import Dropzone from 'react-dropzone';
import FileThumbnail from './FileThumbnail';

type FormProps = {
    schemaDetail: ISchemaDetail;
    useUpload: boolean;
    toggleUseUpload: any;
    children: any;
    selectedFile: any;
    setSelectedFile: any;
};
const ImportableForm = ({
    schemaDetail,
    useUpload,
    toggleUseUpload,
    children,
    selectedFile,
    setSelectedFile,
}: FormProps) => {
    const csv = schemaHelper.getSchemaCsvHeaders(schemaDetail);

    return (
        <Box
            sx={{
                '& .create-option': {
                    margin: 0,
                    padding: '4px 16px',
                    border: (theme) => `2px solid ${theme.palette.glide.stroke.normal.primary}`,
                    borderRadius: '4px',
                    display: 'flex',
                    gap: '24px',
                    flexGrow: 1,
                    justifyContent: 'space-between',
                    '&.selected': {
                        borderColor: (theme) => theme.palette.glide.info,
                    },
                    color: (theme) => theme.palette.glide.text.normal.inverseTertiary,
                    fontWeight: 600,
                },
                '& .attributes-form': {
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '12px',
                    marginTop: '24px',
                },
                '& .preview-template': {
                    display: 'flex',
                    gap: '16px',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    flexWrap: 'wrap',
                },
            }}
        >
            <RadioGroup
                sx={{
                    display: 'flex',
                    gap: '16px',
                }}
                value={useUpload}
                row
                onChange={toggleUseUpload.toggle}
            >
                <FormControlLabel
                    labelPlacement="start"
                    value={false}
                    control={<Radio />}
                    label={
                        <Box sx={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                            <DocumentIcon
                                sx={{
                                    width: '24px !important',
                                    height: '24px !important',
                                }}
                            />
                            Use the form
                        </Box>
                    }
                    className={`create-option ${!useUpload && 'selected'}`}
                />
                <FormControlLabel
                    labelPlacement="start"
                    value={true}
                    control={<Radio />}
                    label={
                        <Box sx={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                            <UploadIcon
                                sx={{
                                    width: '24px !important',
                                    height: '24px !important',
                                }}
                            />
                            Import from CSV
                        </Box>
                    }
                    className={`create-option ${useUpload && 'selected'}`}
                />
            </RadioGroup>
            <Fade unmountOnExit in={!useUpload} exit={false}>
                <Box className="attributes-form">{children}</Box>
            </Fade>
            <Fade in={useUpload} unmountOnExit exit={false}>
                <Box className="attributes-form">
                    <Typography variant="label2-med" className="sectionLabel">
                        Upload file
                    </Typography>
                    <Box className="preview-template">
                        <Typography
                            variant="bo1"
                            sx={{
                                color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                                fontWeight: 400,
                            }}
                        >
                            Download the template to see an example of the format required.
                        </Typography>
                        <CSVLink
                            data={[]}
                            headers={csv}
                            target="_blank"
                            filename={`${schemaDetail?.entityType.displayName} Import Template.csv`}
                        >
                            <Button
                                variant="contained-blue"
                                endIcon={<UploadIcon sx={{ transform: 'rotate(180deg)' }} />}
                                size="small"
                            >
                                Download template
                            </Button>
                        </CSVLink>
                    </Box>
                    <Dropzone multiple={false} onDropAccepted={(files) => setSelectedFile(files[0])}>
                        {({ getRootProps, getInputProps, isDragActive }) => (
                            <Box
                                {...getRootProps()}
                                sx={{
                                    width: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '8px',
                                    padding: '32px',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    border: (theme) =>
                                        `2px dashed ${
                                            isDragActive
                                                ? theme.palette.glide.stroke.hover.primary
                                                : theme.palette.glide.stroke.normal.primary
                                        }`,
                                    cursor: selectedFile ? 'unset' : 'pointer',
                                    borderRadius: '4px',
                                    position: 'relative',
                                }}
                            >
                                {selectedFile ? (
                                    <>
                                        <FileThumbnail style={{ height: '120px', width: '120px' }} />
                                        <Typography variant="bo2" sx={{ display: 'block' }}>
                                            {selectedFile.name}
                                        </Typography>
                                        <IconButton
                                            color="error"
                                            onClick={() => setSelectedFile(null)}
                                            sx={{
                                                position: 'absolute',
                                                top: '8px',
                                                right: '8px',
                                            }}
                                            size="small"
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                    </>
                                ) : (
                                    <>
                                        <input {...getInputProps()} />
                                        <EmptyDocument style={{ width: '140px', height: '112px' }} />
                                        <Typography variant="title2">Drop or Upload a file</Typography>
                                        <Typography variant="bo2" sx={{ display: 'block' }}>
                                            You can drop a file or click here
                                        </Typography>
                                    </>
                                )}
                            </Box>
                        )}
                    </Dropzone>
                    <Typography
                        variant="bo1"
                        sx={{
                            color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                            fontWeight: 400,
                        }}
                    >
                        It may take some time to import the data. We will notify you once it is completed.
                    </Typography>
                </Box>
            </Fade>
        </Box>
    );
};

export default ImportableForm;
