/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Box, Button, PlusIcon, RightTray, Loading, Chip, tableStyles } from 'ui-style';
import useLocalStore from '../store';
import { fetch, entityUrls, SYSTEM_RELATION } from '@tripudiotech/admin-api';
import { useParams } from 'react-router-dom';
import { AgGridReact } from '@ag-grid-community/react';
import { buildSortParams, buildQueryBasedOnFilter, DEFAULT_TABLE_PAGINATION_SIZE, useToggle } from 'ui-common';

const AddUserAction = ({ onRefresh, type }) => {
    const [open, openToggle] = useToggle();
    const { id } = useParams();
    const [loading, setLoading] = useLocalStore((state) => [state.isLoading, state.setIsLoading]);
    const [selectedRows, setSelectedRows] = useState([]);
    const handleSubmit = useCallback(async () => {
        try {
            setLoading(true);
            let requests = [];
            selectedRows.forEach((row) => {
                requests.push(
                    fetch({
                        ...entityUrls.createRelation,
                        params: {
                            fromEntityId: row.id,
                            relationType: SYSTEM_RELATION.WORKS_FOR,
                            toEntityId: id,
                        },
                    })
                );
            });
            await Promise.all(requests);
            onRefresh();
            openToggle.close();
            setLoading(true);
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    }, [id, type, selectedRows]);

    const gridRef = useRef<AgGridReact>(null);
    const onSelectionChanged = useCallback(() => {
        setSelectedRows(gridRef.current.api.getSelectedRows());
    }, []);

    const gridOptions: any = useMemo(
        () => ({
            defaultColDef: {
                flex: 1,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'properties.name',
                    headerName: 'Full Name',
                    filter: false,
                    checkboxSelection: true,
                    cellRenderer: ({ data, value }) => {
                        const { firstName, lastName } = data;
                        if (value) return value;

                        return `${firstName} ${lastName}`;
                    },
                },
                {
                    field: 'properties.email',
                    headerName: 'Email',
                    cellRenderer: ({ data, value }) => {
                        const { email } = data;
                        if (value) return value;

                        return email;
                    },
                },
                {
                    field: 'state.name',
                    headerName: 'Status',
                    cellRenderer: function (params) {
                        return (
                            <Chip
                                size="small"
                                label={params.value}
                                variant={params.value === 'Active' ? 'status-success' : 'status-error'}
                            />
                        );
                    },
                },
            ],
            loadingOverlayComponent: Loading,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            showDisabledCheckboxes: true,
            cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
            paginationPageSize: DEFAULT_TABLE_PAGINATION_SIZE,
            rowSelection: 'multiple',
            onSelectionChanged,
        }),
        []
    );
    const createServerSideDatasource = () => {
        const buildParams = (params) => {
            const { sortModel, startRow, filterModel } = params.request;

            const queryParams = {
                offset: startRow || 0,
                limit: DEFAULT_TABLE_PAGINATION_SIZE,
                ...buildSortParams(sortModel),
            };

            if (filterModel && Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            }
            return queryParams;
        };

        const dataSource = {
            getRows: (params) => {
                params.api.showLoadingOverlay();
                fetch({
                    ...entityUrls.getListEntity,
                    qs: buildParams(params),
                    params: {
                        entityType: 'Person',
                    },
                })
                    .then((response: any) => {
                        if (response.status != 200) {
                            params.failCallback();
                            return;
                        }
                        const totalCount = response.data.pageInfo.total;
                        const rowsThisPage = response.data.data;

                        params.successCallback(rowsThisPage, totalCount);
                    })
                    .catch((e) => {
                        // console.log(e)
                    })
                    .finally(() => {
                        gridRef.current?.api.hideOverlay();
                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };

        return dataSource;
    };

    const onGridReady = useCallback((params) => {
        const datasource = createServerSideDatasource();
        params.api.setServerSideDatasource(datasource);
    }, []);

    return (
        <>
            <Button variant="contained" color="secondary" onClick={openToggle.open} size="small" endIcon={<PlusIcon />}>
                Add User
            </Button>
            <RightTray
                title={'Add User'}
                componentName={'add-new-agent'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Add"
                disabled={selectedRows.length === 0}
                onConfirm={handleSubmit}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                        ...tableStyles,
                    }}
                >
                    <AgGridReact
                        className="ag-theme-alpine"
                        {...gridOptions}
                        ref={gridRef}
                        onGridReady={onGridReady}
                    ></AgGridReact>
                </Box>
            </RightTray>
        </>
    );
};

export default AddUserAction;
