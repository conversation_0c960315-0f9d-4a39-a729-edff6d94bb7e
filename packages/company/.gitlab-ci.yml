.company-variables:
  variables:
    PACKAGE_DIR: packages/company

aws-stag-company-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .company-variables

aws-stag-company-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .company-variables
  needs:
    - aws-stag-company-package

gcp-stag-company-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .company-variables

gcp-stag-company-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .company-variables
  needs:
    - gcp-stag-company-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-company-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .company-variables

gcp-uat-company-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .company-variables
  needs:
    - gcp-uat-company-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json