.common-variables:
  variables:
    PACKAGE_DIR: packages/common

aws-stag-common-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .common-variables

aws-stag-common-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .common-variables
  needs:
    - aws-stag-common-package

gcp-stag-common-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .common-variables

gcp-stag-common-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .common-variables
  needs:
    - gcp-stag-common-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-common-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .common-variables

gcp-uat-common-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .common-variables
  needs:
    - gcp-uat-common-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json