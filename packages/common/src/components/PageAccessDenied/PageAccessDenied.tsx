/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from "react";
import { navigateToUrl } from "single-spa";
import "./styles.css";
import Glide from "../../images/glide.svg";
import Security from "../../images/security.png";

const PageAccessDenied = () => {
    const navigateBack = () => {
        navigateToUrl('/dashboard');
    };

    return (
    <div className="access-denied-root">
        <div className="container">
            <img className="logo" src={Glide} alt="Glide" />
            <img className="logo" src={Security} alt="Security" />
            <h1>Access Denied</h1>
            <p>The page or resource you were trying to reach is absolutely forbidden for some reason.</p>
            <button
                id="page-access-denied-back-button"
                onClick={navigateBack}
                style={{
                    backgroundColor: 'transparent',
                    color: '#DBDFEA',
                    fontSize: '14px',
                    textDecoration: 'none',
                    border: '1px solid #FFFFFF',
                    borderRadius: '8px',
                    padding: '10px 25px',
                    marginTop: '40px',
                    cursor: 'pointer',
                    marginBottom: '4em',
                }}
            >
                Back to Dashboard
            </button>
        </div>
    </div>
    );
}

export default PageAccessDenied;