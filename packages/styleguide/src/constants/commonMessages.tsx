/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
const commonMessages = {
    noPermission: "You don't have permission to perform this action.",
    addGlobalAlternateSuccess: 'Successfully added Global Alternate',
    addContextualAlternate: 'Successfully added selected <b>Contextual alternates</b>',
    failedToAddCA: 'An error occurred while adding selected <b>Contextual alternates</b>. Please try again later',
    failedToAdd<PERSON>: 'An error occurred while adding selected <b>Global alternates</b>. Please try again later',
    removeGlobalAlternateSuccess: 'Successfully removed Global Alternate',
    removeContextualAlternateSuccess: 'Successfully removed selected <b>Contextual Alternates</b>',
    removeContextualAlternateFailed:
        'An error occured while removing selected Contextual Alternates. Please try again later',
    removeGlobalAlternateFailed: 'An error occured while removing selected Global Alternates. Please try again later',
    askToDeleteGlobalAlternate: 'Do you want to remove the selected Global Alternates: ',
    selectItemToRemove: 'Select item(s) to remove',
    clickToLock: 'Click to lock',
    clickToUnLock: 'Click to unlock',
    entityLocked: 'This entity is locked',
    lockedBy: 'This entity is locked by',
    successfullyAddedBOMs: 'Successfully added BOMs',
    successfullyRemovedBOMs: 'Successfully removed BOMs',
    failedToAddingBOMs:
        'An error occurred while adding the BOM data. Please try again later or contact the Administrator for further support',
    successfullyToReplaceBOMs: 'Successfully replaced BOM component',
    failedToReplaceBOMs: 'An error occurs while replacing BOMs',
    failedToRemoveBOMs: 'An error occurs while removing BOMs',
    failedSwapping: 'An error occurs while swapping. Please try again later',
    deleteMasterEntityNotice: 'Deleting a Master entity will delete all its revisions.',
    confirmAction: 'Do you want to do this action?',
    failedToDeleteEntity: 'An error occurs while delete entities',
    failedApplyingClassification:
        'An error occurs while applying classifications for selected entities. Please try again later',
    failedRemoveEntitiesClassification: 'An error occurs while removing selected entities. Please try again later',
    successfullyApplyingClassification: 'Successfully applied classifications for selected entities',
    deleteSelectedEntities: 'Do you want to delete the selected entities?',
    failedRemoveSpecification: 'An error occurs while removing selected specification. Please try again later',
    successfullRemoveSpecification: 'Successfully removed selected specification',
    successfullRemoveDocumentReference: 'Successfully removed selected document reference',
    issueError: 'An error occurs while creating the issue. Please try again later.',
    successRequest: 'Successfully executed the request',
    failedRequest: 'An error occurred while executing the request. Please try again later',
} as const;

export default commonMessages;
