/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { NOTIFICATION_EVENT } from '../constants/notification';

export const TOAST_PERSIST_KEYS = {
    REPORTING_ISSUE: 'REPORTING_ISSUE',
    CREATE_CHANGE_REQUEST: 'CREATE_CHANGE_REQUEST',
};

export const notify = (content, options = {}) => {
    window.dispatchEvent(new CustomEvent(NOTIFICATION_EVENT.NEW_NOTIFICATION, { detail: { content, options } }));
};

export const notifySuccess = (content, options = {}) => {
    window.dispatchEvent(
        new CustomEvent(NOTIFICATION_EVENT.NEW_NOTIFICATION, {
            detail: { content, options: { ...options, type: NOTIFICATION_EVENT.SUCCESS } },
        })
    );
};

export const notifyError = (content, options = {}) => {
    window.dispatchEvent(
        new CustomEvent(NOTIFICATION_EVENT.NEW_NOTIFICATION, {
            detail: { content, options: { ...options, type: NOTIFICATION_EVENT.ERROR } },
        })
    );
};

export const notifyBusinessRuleErrors = (rules, options = {}) => {
    window.dispatchEvent(
        new CustomEvent(NOTIFICATION_EVENT.BUSINESS_RULE, {
            detail: {
                content: 'Some business rules are not satisfied to perform this action',
                rules,
                options: { ...options, type: NOTIFICATION_EVENT.ERROR },
            },
        })
    );
};

export const persistToast = (data, options = {}) => {
    window.dispatchEvent(
        new CustomEvent(NOTIFICATION_EVENT.NEW_NOTIFICATION, {
            detail: {
                ...data,
                options: {
                    type: NOTIFICATION_EVENT.INFO,
                    ...options,
                },
                isForm: true,
            },
        })
    );
};

export const dismiss = (id?) => {
    console.log('dismiss toast id', id);
    window.dispatchEvent(
        new CustomEvent(NOTIFICATION_EVENT.NEW_NOTIFICATION, {
            detail: { id, options: { toastId: id, type: NOTIFICATION_EVENT.DISMISS } },
        })
    );
};
