/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { PERMISSION } from '@tripudiotech/admin-api';

type DetailEntity = {
    createdAt: string;
    createdBy: string;
    disabled: boolean;
    id: string;
    permissions: Record<string, string | boolean | number>;
    properties: any;
    updatedAt: string;
    lockedBy: any;
    isFirstLoad?: boolean;
    alternates?: any[];
    isLocked?: boolean;
};

export const checkingCanUnlock = (entity: DetailEntity, userInfor: { email: string }) => {
    const currUserEmail = get(userInfor, 'email', '');
    const lockedBy = get(entity, 'lockedBy.email', false);
    const canUnlockPer = get(entity, 'permissions.canUnlock', false);
    const isLocker = lockedBy === currUserEmail;

    return isLocker && canUnlockPer;
};

export const getPermission = (detailEntity: DetailEntity, key) => {
    return get(detailEntity, ['permissions', key]);
};

export const isEntityLocked = (detailEntity: DetailEntity) => {
    return !isEmpty(get(detailEntity, 'lockedBy', {}));
};

export const checkingPermission = (
    entity: DetailEntity,
    userInfor: { email: string },
    perName: string | string[],
    skipLockCheck = false
) => {
    if (!perName) return false;
    const hasPermission =
        typeof perName === 'string'
            ? getPermission(entity, perName)
            : perName.every((permissionName) => getPermission(entity, permissionName));

    if (!isEntityLocked(entity) || skipLockCheck) {
        return hasPermission;
    }
    const currUserEmail = get(userInfor, 'email', '');
    const lockedBy = get(entity, 'lockedBy.email', false);
    const canUnlock = getPermission(entity, PERMISSION.CAN_UNLOCK);
    const isLocker = lockedBy === currUserEmail;

    return hasPermission && (isLocker || canUnlock);
};
