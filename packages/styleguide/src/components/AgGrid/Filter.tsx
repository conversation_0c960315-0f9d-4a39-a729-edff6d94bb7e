/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { extractAndOrCriterias, getCriteriasQuery } from '@tripudiotech/admin-api';
import isEmpty from 'lodash/isEmpty';
import { AttributeType } from '../../constants/Attribute';

export const filterParams = {
    filterOptions: ['contains', 'equals', 'blank', 'notBlank'],
};

export const filterParamsByType = {
    [AttributeType.DATE]: {
        filterOptions: ['equals', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual'],
    },
    [AttributeType.DATE_TIME]: {
        filterOptions: ['equals', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual'],
    },
    [AttributeType.INTEGER]: {
        filterOptions: ['equals', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual'],
    },
    [AttributeType.FLOAT]: {
        filterOptions: ['equals', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual'],
    },
    [AttributeType.LONG]: {
        filterOptions: ['equals'],
    },
    default: {
        filterOptions: ['contains', 'equals', 'blank', 'notBlank'],
    },
};

function createFilter(mapper, key, item) {
    if (item.operator) {
        const condition1 = mapper(key, item.condition1);
        const condition2 = mapper(key, item.condition2);

        return extractAndOrCriterias(item.operator, [condition1, condition2]);
    }

    return mapper(key, item);
}

export const buildSortParams = (sortModel) => {
    if (sortModel && sortModel.length > 0) {
        const { colId, sort } = sortModel[0];
        return {
            sortField: colId.split('.')[1],
            sortDirection: sort === 'desc' ? 'DESCENDING' : 'ASCENDING',
        };
    }
    return null;
};

export const buildQueryBasedOnFilter = (filterConditions, filterModel) => {
    if (isEmpty(filterConditions) && isEmpty(filterModel)) {
        return {};
    }

    Object.keys(filterModel).forEach(function (key) {
        const item = filterModel[key];
        if (key.includes('properties')) {
            key = key.substring(key.indexOf('.') + 1);
        }

        switch (item.filterType) {
            case 'set': {
                item.type = 'in';
                const condition = createFilter(getCriteriasQuery, key, item);
                if (condition) {
                    filterConditions.push(condition);
                }
                break;
            }
            case 'text':
            case 'number':
            case 'date': {
                const condition = createFilter(getCriteriasQuery, key, item);
                if (condition) {
                    filterConditions.push(condition);
                }
                break;
            }
            default:
                break;
        }
    });

    if (isEmpty(filterConditions)) return {};

    return extractAndOrCriterias('AND', filterConditions);
};
