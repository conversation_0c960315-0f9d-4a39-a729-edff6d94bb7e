/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import get from 'lodash/get';
/**
 *
 * @param props { Link }
 * Must pass Link component from react-router-dom in the context of current micro-frontend
 *
 */
export default function EntityNameRenderer(props) {
    let { data, Link, value, node, targetPath = 'properties' } = props;

    if (!value) {
        return null;
    }
    if (!data) {
        const firstChild = get(node, ['childrenAfterGroup', '0', 'data']);
        data = firstChild;
    }

    const id = get(data, ['id']);
    const type = get(data, ['properties', 'type'], get(data, ['type']));
    return (
        <Link
            style={{
                textDecoration: 'none',
                color: '#2F54EB',
                fontFamily: 'Work Sans',
                fontSize: '14px',
                fontWeight: '400',
                lineHeight: '18px',
                wordWrap: 'break-word',
                overflow: 'hidden',
            }}
            to={`/detail/${type}/${id}/${targetPath}`}
        >
            {value}
        </Link>
    );
}
