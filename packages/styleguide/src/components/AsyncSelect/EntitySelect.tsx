/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useEffect, useState } from 'react';
import { buildOrOperatorQuery, entityUrls, buildContainsQuery, fetch } from '@tripudiotech/admin-api';
import AsyncSelect from './AsyncSelect';
import isNull from 'lodash/isNull';

const EntitySelect = ({
    defaultValue,
    entityType,
    label,
    isMulti = false,
    onChange = (value) => {},
    onBlur = () => {},
    ...others
}) => {
    const [value, setValue] = useState(null);
    const [isLoaded, setIsLoaded] = useState(false);
    const getInitialOptions = async () => {
        const { data } = await fetch({
            ...entityUrls.getEntityById,
            params: { entityType, entityId: defaultValue },
            skipToast: true,
        });
        setValue({
            value: data.id,
            label: `${data.properties.name}${data.properties.revision ? ' - ' + data.properties.revision : ''}`,
        });
    };

    const loadOptions = async (search, loadedOptions) => {
        try {
            const offset = loadedOptions?.length || 0;
            const { data } = await fetch({
                ...entityUrls.getListEntity,
                params: { entityType },
                qs: {
                    offset: loadedOptions?.length || 0,
                    limit: 20,
                    sortField: 'name',
                    query: JSON.stringify(
                        buildOrOperatorQuery([
                            buildContainsQuery('name', search),
                            buildContainsQuery('description', search),
                        ])
                    ),
                },
            });
            const rows = data?.data;
            const options = rows.map((option) => ({
                value: option.id,
                type: option?.properties?.type,
                label: `${option.properties.name}${
                    option.properties.revision ? ' - ' + option.properties.revision : ''
                }`,
            }));
            const hasMore = offset < data.pageInfo.total;
            return {
                options,
                hasMore,
            };
        } catch {
            return {
                options: [],
                hasMore: false,
            };
        }
    };

    const onSelectChange = (newValue) => {
        setValue(newValue);
        onChange(newValue);
    };

    useEffect(() => {
        if (defaultValue && isNull(value) && defaultValue?.length > 0) {
            getInitialOptions().then(() => setIsLoaded(true));
        } else {
            setIsLoaded(true);
        }
    }, [defaultValue, value]);

    return (
        <AsyncSelect
            label={label}
            closeMenuOnSelect
            loadOptions={loadOptions}
            getOptionValue={(option) => option.value}
            onChange={onSelectChange}
            value={value}
            onBlur={onBlur}
            isMulti={isMulti}
            {...others}
        />
    );
};

export default EntitySelect;
