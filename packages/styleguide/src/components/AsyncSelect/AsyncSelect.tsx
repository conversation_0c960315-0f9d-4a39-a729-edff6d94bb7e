/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box } from 'ui-style';
import React from 'react';
import { AsyncPaginate } from 'react-select-async-paginate';

const customStyles = {
    control: (provided, state) => ({
        ...provided,
        boxShadow: 'none',
        minHeight: '43px',
        '&:hover': {
            border: '1px solid #2F54EB',
        },
        borderRadius: '2px',
        border: state.isFocused ? '1px solid #2F54EB !important' : '1px solid #D6D6D6',
    }),
    valueContainer: (provided, state) => ({
        ...provided,
        padding: '8px',
    }),
    indicatorSeparator: () => ({
        display: 'none',
    }),
    multiValue: (provided, state) => ({
        ...provided,
        padding: '4px 6px',
        backgroundColor: '#EBEBEB',
    }),
    placeholder: (provided, state) => ({
        ...provided,
        padding: '4px 6px',
        paddingLeft: '0px',
        fontSize: '14px',
        color: '#6E6E6E',
    }),
    singleValue: (provided, state) => ({
        ...provided,
        padding: '4px 6px',
        paddingLeft: '0px',
        fontSize: '14px',
        color: '#434343',
    }),
    multiValueLabel: (provided, state) => ({
        ...provided,
        fontSize: '12px',
        fontFamily: 'Work Sans',
        fontWeight: 500,
        color: '#434343',
    }),
    multiValueRemove: (provided, state) => ({
        ...provided,
        backgroundColor: '#EBEBEB !important',
        color: '#434343 !important',
        marginLeft: '8px',
        cursor: 'pointer',
    }),
    menu: (provided, state) => ({
        ...provided,
        backgroundColor: '#F2F2F2',
        height: '100px',
    }),
    menuPortal: (provided, state) => ({
        ...provided,
        zIndex: 9999,
    }),
    option: (provided, { isFocused }) => ({
        ...provided,
        backgroundColor: isFocused ? '#EBEBEB' : '#F2F2F2',
        fontWeight: 400,
        color: '#777777',
        padding: '11px 16px',
        fontSize: '14px',
    }),
};
const AsyncSelect = ({ value, loadOptions, onChange, label = '', isMulti = false, ...others }) => {
    return (
        <Box
            sx={{
                position: 'relative',
                '& label': {
                    position: 'absolute',
                    top: '-8px',
                    zIndex: 1,
                    left: '8px',
                    pl: '4px',
                    pr: '8px',
                    backgroundColor: '#FFFFFF',
                    color: '#777777',
                    fontWeight: 500,
                    fontFamily: 'Work Sans',
                    fontSize: '12px',
                    lineHeight: '135%',
                    letterSpacing: '-0.2px',
                },
            }}
        >
            <label>{label}</label>
            <AsyncPaginate
                styles={customStyles}
                value={value}
                loadOptions={loadOptions}
                onChange={onChange}
                menuPosition="fixed"
                menuPlacement="auto"
                menuPortalTarget={document.body}
                isMulti={isMulti}
                {...others}
            />
        </Box>
    );
};

export default AsyncSelect;
