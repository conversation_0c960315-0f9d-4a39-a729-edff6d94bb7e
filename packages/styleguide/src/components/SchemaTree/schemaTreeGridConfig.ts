/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const getIndentClass = (params) => {
    let indent = 0;
    let node = params.node;
    while (node && node.parent) {
        indent++;
        node = node.parent;
    }
    return 'ag-cell-wrapper hyperlink indent-' + indent;
};

export const getContextMenuCallback = (gridRef) => {
    return () => {
        return [
            {
                name: 'Sort A → Z',
                action: () => {
                    if (gridRef.current) {
                        gridRef.current.columnApi.applyColumnState({
                            state: [{ colId: 'displayName', sort: 'asc' }],
                        });
                    }
                },
            },
            {
                name: 'Sort Z → A',
                action: () => {
                    if (gridRef.current) {
                        gridRef.current.columnApi.applyColumnState({
                            state: [{ colId: 'displayName', sort: 'desc' }],
                        });
                    }
                },
            },
            'separator',
            {
                name: 'Expand selected rows',
                action: () => {
                    if (gridRef.current) {
                        gridRef.current.api.getSelectedNodes().forEach((node) => node.setExpanded(true));
                    }
                },
            },
            'expandAll',
            'contractAll',
            'separator',
            'copy',
            'export',
        ];
    };
};
