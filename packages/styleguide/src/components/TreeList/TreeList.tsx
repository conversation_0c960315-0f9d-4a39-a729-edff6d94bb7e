/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
    Box,
    InputAdornment,
    styled,
    TextField,
    IconButton,
    Collapse,
    CloseIcon,
    GroupContracted,
    GroupExpanded,
    SearchIcon,
} from 'ui-style';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import debounce from 'lodash/debounce';
import classnames from 'classnames';

const Wrapper = styled(Box)(({ theme }) => ({
    background: '#FFFFFF',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    padding: '8px',
    '& .treeHeader': {
        borderBottom: '1px solid #ADB1B7',
        paddingBottom: '12px',
        paddingTop: '4px',
    },
    '& .treeList': {
        width: '100%',
        flexGrow: 1,
        overflow: 'auto',
    },
    '& .root': {
        marginTop: 0,
        paddingLeft: 0,
        width: 'max-content',
        minWidth: 'calc(100% - 10px)',
        '&:before': { border: 'none' },
        '&:after': { display: 'none' },
        '& > li': {
            '&:before': {
                borderTop: 'none !important',
            },
            '&:after': {
                display: 'none',
            },
        },
    },
    '& ul': {
        listStyle: 'none',
        paddingLeft: '32px',
        position: 'relative',
        '&:before': {
            content: '""',
            display: 'block',
            position: 'absolute',
            top: -8,
            left: 16,
            borderLeft: `1px solid #434343`,
            height: '100%',
        },
        '&:after': {
            content: '""',
            display: 'block',
            position: 'absolute',
            height: '5px',
            width: '5px',
            borderRadius: '100%',
            background: '#434343',
            top: -11,
            left: 14,
            zIndex: 1,
        },
    },
    '& li': {
        position: 'relative',
        minWidth: '100%',
        width: 'max-content',
        '&:before': {
            content: '""',
            display: 'block',
            height: '100%',
            left: -16,
            top: 16,
            width: 16,
            position: 'absolute',
            borderTop: `1px solid #434343`,
        },
        '&:after': {
            content: '""',
            display: 'block',
            position: 'absolute',
            height: '5px',
            width: '5px',
            borderRadius: '100%',
            background: '#434343',
            left: -2,
            top: 14,
            zIndex: 1,
        },
        '&:last-child:before': {
            backgroundColor: '#FFFFFF',
            bottom: 0,
            height: 'auto',
            width: '16px',
        },
    },
    '& .treeItem': {
        background: '#F7F8FC',
        width: '100%',
        paddingRight: '8px',
        whiteSpace: 'nowrap',
        margin: '8px 0',
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
        '&.selected': {
            border: '1px solid #2F54EB',
        },
        '&.hightlighted': {
            border: `2px solid #2F54EB`,
        },
    },
}));

const StyledIconButton = styled(IconButton)(({ theme }) => ({
    padding: 0,
    width: '32px',
    height: '32px',
    '&:hover': {
        background: 'none',
    },
    zIndex: 10,
}));

const ExpandButton = ({ onClick }) => {
    return (
        <StyledIconButton onClick={onClick}>
            <GroupContracted />
        </StyledIconButton>
    );
};

const UnExpandableButton = () => {
    return <div style={{ width: '8px', height: '32px' }}></div>;
};

const CollapseButton = ({ onClick }) => {
    return (
        <StyledIconButton onClick={onClick}>
            <GroupExpanded />
        </StyledIconButton>
    );
};

const defaultItemRenderer = (node, onClick: (id) => void) => <span onClick={() => onClick(node.id)}>{node.title}</span>;
/**
 * TreeList Component
 * @returns Uncontrolled Tree list component, You need to handle select, expand state yourself
 */
const TreeList = ({
    data = [],
    defaultState = {},
    defaultSelectedId = '',
    enableCheckbox = false,
    itemRenderer = defaultItemRenderer,
    onSelected = (id) => {},
    selectedValue = null,
}) => {
    const [search, setSearch] = useState('');
    const [treeData, setTreeData] = useState(data);
    const [treeState, setTreeState] = useState(defaultState);
    const [selectedId, setSelectedId] = useState(defaultSelectedId);

    useEffect(() => {
        if (selectedValue) {
            setSelectedId(selectedValue);
        }
    }, [selectedValue]);

    const onExpand = (id) => {
        setTreeState({
            ...treeState,
            [id]: {
                ...treeState[id],
                expanded: true,
            },
        });
    };

    const onCollapse = (id) => {
        setTreeState({
            ...treeState,
            [id]: {
                ...treeState[id],
                expanded: false,
            },
        });
    };
    const onSelect = (id) => {
        setSelectedId(id);
        onSelected(id);
    };

    const renderTree = (tree, treeState) => {
        return tree.map((node, idx) => {
            const { id, title, children } = node;
            const expanded = get(treeState, [id, 'expanded'], false);
            const selected = id === selectedId;
            const hasChildren = !isEmpty(children);
            const hightlighted = !isEmpty(search) && title.toLowerCase().includes(search.toLowerCase());

            return (
                <li key={`${id}-${idx}`}>
                    <div
                        className={classnames('treeItem', {
                            selected,
                            hightlighted,
                        })}
                    >
                        {hasChildren ? (
                            expanded ? (
                                <CollapseButton onClick={() => onCollapse(id)} />
                            ) : (
                                <ExpandButton onClick={() => onExpand(id)} />
                            )
                        ) : (
                            <UnExpandableButton />
                        )}
                        {itemRenderer(node, onSelect)}
                    </div>
                    {hasChildren && (
                        <Collapse component={'ul' as any} timeout={100} in={expanded} unmountOnExit>
                            {renderTree(children, treeState)}
                        </Collapse>
                    )}
                </li>
            );
        });
    };

    const filter = (treeData, search) => {
        let newState = { ...treeState };
        const getNodes = (result, object) => {
            if (object.title.toLowerCase().includes(search.toLowerCase())) {
                result.push(object);
                return result;
            }
            if (Array.isArray(object.children)) {
                const nodes = object.children.reduce(getNodes, []);
                if (nodes.length) {
                    result.push({ ...object, nodes });
                    newState[object.id].expanded = true;
                }
            }
            return result;
        };
        const newTree = treeData.reduce(getNodes, []);

        return [newTree, newState];
    };

    const tree = useMemo(() => {
        return renderTree(treeData, treeState);
    }, [treeData, treeState, selectedId]);

    const onSearchChanged = useCallback((e) => {
        const { value } = e.target;
        setSearch(value);
    }, []);

    const filterTreeData = useCallback(
        debounce((search, data) => {
            if (isEmpty(search)) {
                setTreeData(data);
                return;
            }
            const [filtered, newState] = filter(data, search);
            setTreeData(filtered);
            setTreeState(newState);
        }, 150),
        []
    );

    useEffect(() => {
        filterTreeData(search, data);
    }, [search, data]);

    return (
        <Wrapper>
            <div className="treeHeader">
                <TextField
                    sx={{
                        '& input': {
                            paddingLeft: '4px !important',
                            fontSize: '14px',
                        },
                        background: '#FFFFFF',
                    }}
                    size="small"
                    onChange={onSearchChanged}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                        endAdornment: (
                            <InputAdornment
                                position="end"
                                style={{ visibility: isEmpty(search) ? 'hidden' : 'visible', cursor: 'pointer' }}
                                onClick={() => setSearch('')}
                            >
                                <CloseIcon sx={{ width: '16px', height: '16px' }} />
                            </InputAdornment>
                        ),
                    }}
                    value={search}
                    placeholder="Search something..."
                    fullWidth
                    variant="outlined"
                />
            </div>
            <div className="treeList">
                <ul className="root">{tree}</ul>
            </div>
        </Wrapper>
    );
};

export default TreeList;
