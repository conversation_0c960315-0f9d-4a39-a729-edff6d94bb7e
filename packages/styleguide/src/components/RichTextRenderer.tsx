/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import DOMPurify from 'dompurify';

const SANITIZE_CONFIG = {
    ALLOWED_TAGS: [
        'b',
        'i',
        'em',
        'strong',
        'a',
        'p',
        'ul',
        'li',
        'ol',
        'br',
        'span',
        'blockquote',
        'pre',
        'code',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
    ],
    ALLOWED_ATTR: ['href', 'target', 'rel', 'class', 'style'],
    RETURN_TRUSTED_TYPE: true,
};
export const sanitizeRichText = (html: string): string => DOMPurify.sanitize(html, SANITIZE_CONFIG);
interface RichTextRendererProps extends React.HTMLAttributes<HTMLDivElement> {
    html: string;
}

const RichTextRenderer = ({ html, ...others }: RichTextRendererProps) => {
    const cleanHtml = sanitizeRichText(html);
    return <div dangerouslySetInnerHTML={{ __html: cleanHtml }} {...others} />;
};

export default RichTextRenderer;
