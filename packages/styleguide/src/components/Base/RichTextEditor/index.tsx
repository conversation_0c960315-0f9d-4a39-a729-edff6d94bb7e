/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useRef, useEffect } from 'react';
import { convertFromHTML, ContentState, convertToRaw, EditorState, convertFromRaw } from 'draft-js';
import { TextField, Box, IconButton, styled, StyledComponent, ExpandIcon } from 'ui-style';
import { getIn } from 'formik';
import classnames from 'classnames';
import { stateToHTML } from 'draft-js-export-html';
import { Editor } from 'react-draft-wysiwyg';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

export const classes = {
    rteRoot: 'rte-root',
    focus: 'focus',
    readOnly: 'readOnly',
};

export const Root: StyledComponent<any> = styled('div')(({ theme }) => ({
    fontSize: '0.875rem',
    fontFamily: 'Work Sans',
    '& fieldset': {
        borderRadius: '4px',
        paddingBottom: '12px',
        border: `1px solid ${theme.palette.glide.stroke.normal.main}`,
        cursor: 'default',
        [`&:hover`]: {
            borderColor: theme.palette.glide.stroke.hover.blue,
        },
    },
    [`.${classes.focus}`]: {
        borderColor: theme.palette.glide.stroke.hover.blue,
        borderWidth: '2px',
    },
    '& legend': {
        color: theme.palette.text?.primary,
        fontWeight: 600,
        fontSize: '12px',
        userSelect: 'none',
        padding: '0 8px',
    },
    '& .DraftEditor-editorContainer': {
        height: '100%',
        maxHeight: '400px',
        width: '100%',
        cursor: 'text',
    },
    '& .rdw-editor-main': {
        overflowX: 'hidden',
    },
    '& .toolbar': {
        border: 'none',
    },
}));

const RichTextEditor = ({
    field,
    form: { touched, errors, setFieldValue, setFieldTouched },
    meta,
    defaultRte,
    readOnly,
    ...props
}) => {
    const [focused, setFocused] = useState(false);
    const [multilineTextBox, setMultilineTextBox] = useState(!defaultRte);
    const richTextRef = useRef(field.defaultValue);

    const { name, defaultValue, label, styles = {}, ...rest } = field;
    const { helperText } = props;

    const handleOnFocus = () => {
        setFocused(true);
    };

    const fieldError = getIn(errors, field.name);
    const showError = getIn(touched, field.name) && !!fieldError;
    const contentHTML = convertFromHTML(field.value || '');
    const state = ContentState.createFromBlockArray(contentHTML.contentBlocks, contentHTML.entityMap);
    const [editorState, setEditorState] = useState(EditorState.createEmpty());
    useEffect(() => {
        if (field.value) {
            setEditorState(EditorState.createWithContent(convertFromRaw(convertToRaw(state))));
        }
    }, [field.value]);
    return (
        <Box key="box-contain" style={{ position: 'relative' }}>
            {multilineTextBox && (
                <TextField
                    {...props}
                    rows={4}
                    multiline
                    name={field.name}
                    value={field.value}
                    onChange={field.onChange}
                    InputProps={{
                        readOnly: readOnly,
                    }}
                    sx={{
                        textarea: {
                            resize: 'both',
                            width: '100%',
                            padding: '0px !important',
                        },
                    }}
                    InputLabelProps={{ shrink: true }}
                    variant="outlined"
                    helperText={showError ? fieldError : helperText}
                    error={showError}
                />
            )}

            {!multilineTextBox && (
                <Root {...styles}>
                    <fieldset
                        className={classnames({
                            [classes.focus]: focused,
                            [classes.readOnly]: readOnly,
                        })}
                    >
                        <legend style={{ fontFamily: 'Work Sans' }}>{props.label}</legend>
                        <Editor
                            {...props}
                            toolbarClassName="toolbar"
                            onEditorStateChange={(value) => {
                                const newValue = stateToHTML(value.getCurrentContent());
                                if (newValue !== field.value) {
                                    richTextRef.current = stateToHTML(value.getCurrentContent());
                                }
                                setEditorState(value);
                            }}
                            name={field.name}
                            readOnly={readOnly}
                            onFocus={handleOnFocus}
                            label={props.label || props.name}
                            toolbarHidden={readOnly}
                            onBlur={() => {
                                setFocused(false);
                                setFieldTouched(field.name);
                                setFieldValue(field.name, richTextRef.current);
                            }}
                            inheritFontSize
                            editorState={editorState}
                        />
                    </fieldset>
                    {helperText && !showError && (
                        <p className="MuiFormHelperText-root MuiFormHelperText-sizeMedium MuiFormHelperText-contained css-15chf9y-MuiFormHelperText-root">
                            {helperText}
                        </p>
                    )}
                    {showError && (
                        <p className="MuiFormHelperText-root Mui-error MuiFormHelperText-sizeMedium MuiFormHelperText-contained css-15chf9y-MuiFormHelperText-root">
                            {fieldError}
                        </p>
                    )}
                </Root>
            )}
            {!readOnly && (
                <IconButton
                    onClick={() => {
                        setMultilineTextBox((prev) => !prev);
                    }}
                    sx={{
                        position: 'absolute',
                        top: 8,
                        right: 3,
                        width: '30px',
                        height: '30px',
                    }}
                >
                    <ExpandIcon />
                </IconButton>
            )}
        </Box>
    );
};

RichTextEditor.defaultProps = {
    defaultRte: false,
};

export default RichTextEditor;
