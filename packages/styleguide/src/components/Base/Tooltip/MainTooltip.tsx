/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Tooltip } from 'ui-style';
import React from 'react';

const getVariantStyles = (variant) => {
    if (variant === 'error') {
        return {
            tooltip: {
                sx: {
                    backgroundColor: '#D83C31',
                    color: '#FFFFFF',
                    p: 1,
                    fontSize: '12px',
                    borderRadius: '2px',
                    opacity: '0.95 !important',
                },
            },
            arrow: { sx: { color: '#D83C31', opacity: '0.95 !important' } },
        };
    }
    if (variant === 'info') {
        return {
            tooltip: {
                sx: {
                    backgroundColor: '#475B6B',
                    color: '#FFFFFF',
                    p: 1,
                    fontSize: '12px',
                    borderRadius: '2px',
                },
            },
            arrow: { sx: { color: '#475B6B' } },
        };
    }
    return {
        tooltip: {
            sx: {
                backgroundColor: '#26334C',
                color: '#FFFFFF',
                p: 1,
                fontSize: '12px',
                borderRadius: '2px',
            },
        },
        arrow: { sx: { color: '#26334C' } },
    };
};

const MainTooltip = ({ children, title, placement, variant = 'default', ...others }) => {
    const variantStyles = getVariantStyles(variant);
    return (
        <Tooltip componentsProps={variantStyles} arrow placement={placement} title={title} {...others}>
            {children}
        </Tooltip>
    );
};

MainTooltip.defaultProps = {
    placement: 'bottom',
    title: '',
};
export default MainTooltip;
