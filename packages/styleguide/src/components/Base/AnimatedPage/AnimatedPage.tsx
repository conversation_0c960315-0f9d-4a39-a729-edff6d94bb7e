/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { motion, type Variants, type MotionProps } from 'framer-motion';
import { ReactNode } from 'react';

interface AnimatedPageProps extends MotionProps {
    animation?: Variants;
    className?: string;
    children: ReactNode;
}

const animations = [
    {
        initial: { opacity: 0, y: 30 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: 50 },
    },
    {
        initial: { opacity: 0, y: -30 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -50 },
    },
];
const getRandomAnimation = () => {
    return animations[(animations.length * Math.random()) | 0];
};
const AnimatedPage = ({ children, animation, ...others }: AnimatedPageProps) => {
    const animationVariant = animation || getRandomAnimation();
    return (
        <motion.div
            variants={animationVariant}
            initial="initial"
            animate="animate"
            exit="exit"
            {...others}
            transition={{ duration: 0.35 }}
        >
            {children}
        </motion.div>
    );
};
export default AnimatedPage;
