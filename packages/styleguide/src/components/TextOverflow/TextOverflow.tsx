/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { cloneElement, useCallback, useMemo, useState } from 'react';
import { MainTooltip } from '../../tripudiotech-styleguide';

const isOverflow = (elem) => elem.scrollHeight > elem.clientHeight;

const TruncatedText = ({ children, lines, innerRef }) => {
    const styles = useMemo(() => {
        return {
            overflow: 'hidden',
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
            WebkitLineClamp: parseInt(lines),
            whiteSpace: 'pre-wrap',
            fontSize: 'inherit',
            color: 'inherit',
            fontWeight: 'inherit',
        };
    }, [lines]);

    const childrenEl = children.type ? children : <span>{children}</span>;
    return cloneElement(childrenEl, {
        style: { ...childrenEl.props.style, ...styles },
        ref: innerRef,
    });
};

const TextOverflow = ({
    children = '',
    lines = 4,
    maxLines = 100,
    withTooltip = false,
    showMoreButton,
    showLessButton,
    ...others
}) => {
    const [show, setShow] = useState(false);
    const [overflow, setOverflow] = useState(false);

    const visibleLines = show ? maxLines : lines;

    const handleElementOverflow = (element) => {
        if (element) {
            setOverflow(isOverflow(element));
        }
    };
    const toggleShow = useCallback(() => {
        setShow((prev) => !prev);
    }, []);
    const enableShowMore = overflow && !show && showMoreButton;
    const enableShowLess = show && showLessButton;

    return (
        <MainTooltip {...others} placement="bottom" title={overflow && withTooltip && !show ? children : ''}>
            <span>
                <TruncatedText lines={visibleLines} innerRef={handleElementOverflow}>
                    {children}
                </TruncatedText>
                {enableShowMore && showMoreButton(toggleShow)}
                {enableShowLess && showLessButton(toggleShow)}
            </span>
        </MainTooltip>
    );
};

export default TextOverflow;
