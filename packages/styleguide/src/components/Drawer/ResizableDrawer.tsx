/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useEffect, useState, useCallback } from 'react';
import { Drawer } from 'ui-style';
import { debounce } from 'lodash';

const ResizableDrawer = ({
    children,
    minWidth = 450,
    maxWidth = 1400,
    defaultWidth = 450,
    open,
    onClose,
    componentName,
    disableCloseOnClickOutside = false,
    ...others
}) => {
    const [isResizing, setIsResizing] = useState(false);
    const [newWidth, setNewWidth] = useState(null);
    const enableResize = useCallback(() => {
        setIsResizing(true);
    }, [setIsResizing]);

    const disableResize = useCallback(() => {
        setIsResizing(false);
    }, [setIsResizing]);

    const resize = useCallback(
        (e: MouseEvent) => {
            if (isResizing) {
                const newWidth = document.body.offsetWidth - (e.clientX - document.body.offsetLeft);
                if (newWidth > minWidth && newWidth < maxWidth) {
                    setNewWidth(newWidth);
                }
            }
        },
        [minWidth, isResizing, setNewWidth]
    );

    useEffect(() => {
        document.addEventListener('mousemove', resize);
        document.addEventListener('mouseup', disableResize);

        return () => {
            document.removeEventListener('mousemove', resize);
            document.removeEventListener('mouseup', disableResize);
        };
    }, [disableResize, resize]);

    useEffect(() => {
        storeNewWidth(newWidth);
    }, [newWidth]);

    const storeNewWidth = useCallback(
        debounce((newWidth) => {
            if (!newWidth) {
                // load from local storage at the first time
                localStorage.setItem(componentName, JSON.stringify({ resizedWidth: getResizedWith() }));
            } else {
                localStorage.setItem(componentName, JSON.stringify({ resizedWidth: newWidth }));
            }
        }, 3000),
        []
    );

    useEffect(() => {
        const resizedWidth = getResizedWith();
        setNewWidth(resizedWidth);
    }, []);

    function getResizedWith() {
        const component = localStorage.getItem(componentName);
        if (component) {
            return JSON.parse(component).resizedWidth;
        } else {
            return defaultWidth;
        }
    }

    return (
        <Drawer
            anchor="right"
            style={{
                height: '100%',
                position: 'relative',
                zIndex: 1300,
            }}
            PaperProps={{ style: { width: newWidth, overflowX: 'hidden', maxWidth: '100vw' } }}
            open={open}
            onClose={() => {
                !disableCloseOnClickOutside && onClose();
            }}
            {...others}
        >
            <div
                onMouseDown={enableResize}
                style={{
                    width: '5px',
                    cursor: 'ew-resize',
                    padding: '4px 0 0',
                    position: 'absolute',
                    top: 0,
                    bottom: 0,
                    zIndex: 100,
                }}
            />
            {children}
        </Drawer>
    );
};
export default ResizableDrawer;
