/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const DRAWER_COMPONENT_NAME = {
    BROWSE_CATALOG: 'BrowseCatalog',
    CLASSIFY: 'Classify',
    RELATION_SUMMARY: 'RelationSummary',
    CREATE_PALLETE: 'CreateEntity',
    ADD_BOM: 'AddBOM',
    ADD_CONTEXTUAL_ALTERNATE: 'AddContextualAlternate',
    ADD_GLOBAL_ALTERNATE: 'AddGlobalAlternate',
    GRANT_ACCESS: 'GrantAccess',
    REMOVE_CONTEXTUAL_ALTERNATE: 'RemoveContextualAlternate',
    REPLACE_BOM: 'ReplaceBom',
    ADD_SPECIFICATION: 'AddSpecification',
    CLASSIFY_ENTITY_CATALOG: 'ClassifyEntityCatalog',
    ADD_EXISTING_SPECIFICATION: 'ADD_EXISTING_SPECIFICATION',
    EDIT_PROPERTIES_PANEL: 'EDIT_PROPERTIES_PANEL',
    ADD_RELATIONS: 'ADD_RELATIONS',
    PROCESS_TASK_DETAIL: 'PROCESS_TASK_DETAIL',
    ADD_NEW_DOCUMENT_REF: 'ADD_NEW_DOCUMENT_REF',
    CREATE_ENTITY_RELATION: 'CREATE_ENTITY_RELATION',
    CREATE_CHANGE: 'CREATE_CHANGE',
};
