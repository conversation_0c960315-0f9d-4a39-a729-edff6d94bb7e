/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, NoResultIcon, Typography } from 'ui-style';

const NoRowsOverlay = ({ search, initialMessage, minLength, component = undefined }) => {
    const searchText = search?.current ? search.current : '';
    const defaultMsg = (
        <Typography sx={{ mt: 2, fontSize: '16px', px: 4 }}>
            We couldn't find any information related to <b>{searchText}</b>. Please try refining your search criteria.
        </Typography>
    );

    const renderMsg = () => {
        if (component) {
            return component;
        }
        return initialMessage && minLength ? (
            searchText.length < minLength ? (
                <Typography sx={{ mt: 2, fontSize: '16px', px: 4 }}>Seach two entities to compare</Typography>
            ) : (
                defaultMsg
            )
        ) : (
            defaultMsg
        );
    };
    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                p: 2,
            }}
            className="no-rows-overlay"
        >
            <NoResultIcon sx={{ mt: '-64px' }} />
            {renderMsg()}
        </Box>
    );
};

export default NoRowsOverlay;
