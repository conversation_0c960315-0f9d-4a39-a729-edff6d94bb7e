/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import moment from 'moment';
const DEFAULT_DATETIME_FORMAT = 'yyyy-MM-DD HH:mm:ss';
const DEFAULT_DATE_FORMAT = 'yyyy-MM-DD';

export { default as Loading } from './components/Loading';
export { default as InfiniteLoading } from './components/InfiniteLoading';
export { default as RichTextEditor } from './components/Base/RichTextEditor';
export { default as LoadingOverlay } from './components/LoadingOverlay';
export { default as RichTextRenderer, sanitizeRichText } from './components/RichTextRenderer';
export const formatDateTime = (datetime: string, format?: string) => {
    if (!datetime) return '';
    return moment
        .utc(datetime)
        .local()
        .format(format || DEFAULT_DATETIME_FORMAT);
};

export const formatSystemDateTime = (datetime: Date) => {
    try {
        // Convert local time to UTC
        const adjustedDate = new Date(datetime);
        adjustedDate.setSeconds(0);
        adjustedDate.setMilliseconds(0);

        return adjustedDate.toISOString();
    } catch (err) {
        return '';
    }
};

export const formatSystemDate = (date: Date) => {
    try {
        return date.toISOString().slice(0, 10);
    } catch (err) {
        return '';
    }
};

export const formatDate = (datetime: string, format?: string) => {
    if (!datetime) return null;
    return formatDateTime(datetime, format || DEFAULT_DATE_FORMAT);
};

export const formatDateStringToDate = (dateTime: string, format = DEFAULT_DATE_FORMAT) => {
    return moment(dateTime, format).toDate();
};

export const formatDateTimeFromTimestampMs = (timestamp: number, format?: string) => {
    try {
        return moment.unix(timestamp / 1000).format(format || DEFAULT_DATETIME_FORMAT);
    } catch {
        return timestamp;
    }
};
export { useDebounce } from './utils/useDebounce';
export { AttributeType } from './constants/Attribute';
export { NOTIFICATION_EVENT } from './constants/notification';
export {
    notify,
    notifySuccess,
    notifyError,
    persistToast,
    dismiss,
    TOAST_PERSIST_KEYS,
    notifyBusinessRuleErrors,
} from './utils/notificationUtils';
export { default as AnimatedPage } from './components/Base/AnimatedPage/AnimatedPage';
export { default as NoRowsOverlay } from './components/NoRowsOverlay/NoRowsOverlay';
export { default as TreeList } from './components/TreeList/TreeList';
export { default as TreeContainer } from './components/SchemaTree/TreeContainer';
export { default as SchemaDetailViewContainer } from './components/SchemaTree/DetailViewContainer';
export { default as commonMessages } from './constants/commonMessages';
export { default as MainTooltip } from './components/Base/Tooltip/MainTooltip';
export { default as TextOverflow } from './components/TextOverflow/TextOverflow';
export { filterParams, filterParamsByType, buildQueryBasedOnFilter, buildSortParams } from './components/AgGrid/Filter';
export { default as ResizableDrawer } from './components/Drawer/ResizableDrawer';
export { DRAWER_COMPONENT_NAME } from './components/Drawer/DrawerConstants';
export { default as EntityNameRenderer } from './components/AgGrid/EntityNameRenderer';
export { default as OwnerRenderer } from './components/AgGrid/OwnerRenderer';
export { default as AsyncSelect } from './components/AsyncSelect/AsyncSelect';
export { default as EntitySelect } from './components/AsyncSelect/EntitySelect';
export { checkingCanUnlock, checkingPermission, isEntityLocked } from './utils/checkingPermission';
export { GET_UNIT_LIMIT_RANGES } from './constants/common';
export { getRelationDisplayName } from './utils/helper';
export * from './utils/gridUtils';
