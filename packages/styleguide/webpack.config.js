const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const { mergeWithRules } = require('webpack-merge');

module.exports = (webpackConfigEnv, argv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: 'tripudiotech',
        projectName: 'styleguide',
        webpackConfigEnv,
        argv,
    });
    const sourceMap = process.env.SOURCE_MAP || false;

    const config = mergeWithRules({
        module: {
            rules: {
                test: 'match',
                use: 'replace',
            },
        },
    })(defaultConfig, {
        module: {
            rules: [
                {
                    test: /\.(scss|sass)$/,
                    use: [
                        {
                            loader: 'style-loader',
                        },
                        {
                            loader: 'css-loader',
                        },
                        {
                            loader: 'sass-loader',
                        },
                    ],
                },
                {
                    test: /.m?js/,
                    resolve: {
                        fullySpecified: false,
                    },
                },
            ],
        },
        devtool: sourceMap,
        externals: ['react', 'react-dom'],
    });
    return config;
};
