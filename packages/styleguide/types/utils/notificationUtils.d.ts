export declare const TOAST_PERSIST_KEYS: {
    REPORTING_ISSUE: string;
    CREATE_CHANGE_REQUEST: string;
};
export declare const notify: (content: any, options?: {}) => void;
export declare const notifySuccess: (content: any, options?: {}) => void;
export declare const notifyError: (content: any, options?: {}) => void;
export declare const notifyBusinessRuleErrors: (rules: any, options?: {}) => void;
export declare const persistToast: (data: any, options?: {}) => void;
export declare const dismiss: (id?: any) => void;
//# sourceMappingURL=notificationUtils.d.ts.map