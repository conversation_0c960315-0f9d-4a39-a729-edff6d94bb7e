type DetailEntity = {
    createdAt: string;
    createdBy: string;
    disabled: boolean;
    id: string;
    permissions: Record<string, string | boolean | number>;
    properties: any;
    updatedAt: string;
    lockedBy: any;
    isFirstLoad?: boolean;
    alternates?: any[];
    isLocked?: boolean;
};
export declare const checkingCanUnlock: (entity: DetailEntity, userInfor: {
    email: string;
}) => string | number | boolean;
export declare const getPermission: (detailEntity: DetailEntity, key: any) => string | number | boolean;
export declare const isEntityLocked: (detailEntity: DetailEntity) => boolean;
export declare const checkingPermission: (entity: DetailEntity, userInfor: {
    email: string;
}, perName: string | string[], skipLockCheck?: boolean) => string | number | boolean;
export {};
//# sourceMappingURL=checkingPermission.d.ts.map