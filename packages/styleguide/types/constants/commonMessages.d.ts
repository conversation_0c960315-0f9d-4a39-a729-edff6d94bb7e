declare const commonMessages: {
    readonly noPermission: "You don't have permission to perform this action.";
    readonly addGlobalAlternateSuccess: "Successfully added Global Alternate";
    readonly addContextualAlternate: "Successfully added selected <b>Contextual alternates</b>";
    readonly failedToAddCA: "An error occurred while adding selected <b>Contextual alternates</b>. Please try again later";
    readonly failedToAddGA: "An error occurred while adding selected <b>Global alternates</b>. Please try again later";
    readonly removeGlobalAlternateSuccess: "Successfully removed Global Alternate";
    readonly removeContextualAlternateSuccess: "Successfully removed selected <b>Contextual Alternates</b>";
    readonly removeContextualAlternateFailed: "An error occured while removing selected Contextual Alternates. Please try again later";
    readonly removeGlobalAlternateFailed: "An error occured while removing selected Global Alternates. Please try again later";
    readonly askToDeleteGlobalAlternate: "Do you want to remove the selected Global Alternates: ";
    readonly selectItemToRemove: "Select item(s) to remove";
    readonly clickToLock: "Click to lock";
    readonly clickToUnLock: "Click to unlock";
    readonly entityLocked: "This entity is locked";
    readonly lockedBy: "This entity is locked by";
    readonly successfullyAddedBOMs: "Successfully added BOMs";
    readonly successfullyRemovedBOMs: "Successfully removed BOMs";
    readonly failedToAddingBOMs: "An error occurred while adding the BOM data. Please try again later or contact the Administrator for further support";
    readonly successfullyToReplaceBOMs: "Successfully replaced BOM component";
    readonly failedToReplaceBOMs: "An error occurs while replacing BOMs";
    readonly failedToRemoveBOMs: "An error occurs while removing BOMs";
    readonly failedSwapping: "An error occurs while swapping. Please try again later";
    readonly deleteMasterEntityNotice: "Deleting a Master entity will delete all its revisions.";
    readonly confirmAction: "Do you want to do this action?";
    readonly failedToDeleteEntity: "An error occurs while delete entities";
    readonly failedApplyingClassification: "An error occurs while applying classifications for selected entities. Please try again later";
    readonly failedRemoveEntitiesClassification: "An error occurs while removing selected entities. Please try again later";
    readonly successfullyApplyingClassification: "Successfully applied classifications for selected entities";
    readonly deleteSelectedEntities: "Do you want to delete the selected entities?";
    readonly failedRemoveSpecification: "An error occurs while removing selected specification. Please try again later";
    readonly successfullRemoveSpecification: "Successfully removed selected specification";
    readonly successfullRemoveDocumentReference: "Successfully removed selected document reference";
    readonly issueError: "An error occurs while creating the issue. Please try again later.";
    readonly successRequest: "Successfully executed the request";
    readonly failedRequest: "An error occurred while executing the request. Please try again later";
};
export default commonMessages;
//# sourceMappingURL=commonMessages.d.ts.map