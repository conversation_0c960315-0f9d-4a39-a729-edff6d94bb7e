declare const ResizableDrawer: ({ children, minWidth, maxWidth, defaultWidth, open, onClose, componentName, disableCloseOnClickOutside, ...others }: {
    [x: string]: any;
    children: any;
    minWidth?: number;
    maxWidth?: number;
    defaultWidth?: number;
    open: any;
    onClose: any;
    componentName: any;
    disableCloseOnClickOutside?: boolean;
}) => import("react/jsx-runtime").JSX.Element;
export default ResizableDrawer;
//# sourceMappingURL=ResizableDrawer.d.ts.map