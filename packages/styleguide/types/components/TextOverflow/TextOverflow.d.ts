declare const TextOverflow: ({ children, lines, maxLines, withT<PERSON><PERSON>, showMore<PERSON>utton, showLessButton, ...others }: {
    [x: string]: any;
    children?: string;
    lines?: number;
    maxLines?: number;
    withTooltip?: boolean;
    showMoreButton: any;
    showLessButton: any;
}) => import("react/jsx-runtime").JSX.Element;
export default TextOverflow;
//# sourceMappingURL=TextOverflow.d.ts.map