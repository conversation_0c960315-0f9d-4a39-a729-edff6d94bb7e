import { type Variants, type MotionProps } from 'framer-motion';
import { ReactNode } from 'react';
interface AnimatedPageProps extends MotionProps {
    animation?: Variants;
    className?: string;
    children: ReactNode;
}
declare const AnimatedPage: ({ children, animation, ...others }: AnimatedPageProps) => import("react/jsx-runtime").JSX.Element;
export default AnimatedPage;
//# sourceMappingURL=AnimatedPage.d.ts.map