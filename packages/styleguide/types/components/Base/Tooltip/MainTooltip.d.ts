declare const MainTooltip: {
    ({ children, title, placement, variant, ...others }: {
        [x: string]: any;
        children: any;
        title: any;
        placement: any;
        variant?: string;
    }): import("react/jsx-runtime").JSX.Element;
    defaultProps: {
        placement: string;
        title: string;
    };
};
export default MainTooltip;
//# sourceMappingURL=MainTooltip.d.ts.map