/**
 * TreeList Component
 * @returns Uncontrolled Tree list component, You need to handle select, expand state yourself
 */
declare const TreeList: ({ data, defaultState, defaultSelectedId, enableCheckbox, itemRenderer, onSelected, selectedValue, }: {
    data?: any[];
    defaultState?: {};
    defaultSelectedId?: string;
    enableCheckbox?: boolean;
    itemRenderer?: (node: any, onClick: (id: any) => void) => import("react/jsx-runtime").JSX.Element;
    onSelected?: (id: any) => void;
    selectedValue?: any;
}) => import("react/jsx-runtime").JSX.Element;
export default TreeList;
//# sourceMappingURL=TreeList.d.ts.map