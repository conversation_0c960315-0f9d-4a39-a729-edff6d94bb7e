export declare const filterParams: {
    filterOptions: string[];
};
export declare const filterParamsByType: {
    [x: string]: {
        filterOptions: string[];
    };
    default: {
        filterOptions: string[];
    };
};
export declare const buildSortParams: (sortModel: any) => {
    sortField: any;
    sortDirection: string;
};
export declare const buildQueryBasedOnFilter: (filterConditions: any, filterModel: any) => {};
//# sourceMappingURL=Filter.d.ts.map