{"name": "@tripudiotech/admin-styleguide", "version": "1.0.0", "scripts": {"start": "concurrently npm:serve npm:emit-types", "serve": "webpack serve --port 9006", "start:standalone": "webpack serve --env standalone", "emit-types": "tsc --watch --emitDeclarationOnly", "build": "concurrently npm:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "test": "cross-env BABEL_ENV=test jest", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage"}, "devDependencies": {"typescript": "5.7.3", "@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@types/testing-library__jest-dom": "^5.14.1", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "react": "^17.0.2", "react-dom": "^17.0.2", "ts-config-single-spa": "^3.0.0", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0", "@types/draft-js": "^0.11.7"}, "dependencies": {"@ag-grid-community/client-side-row-model": "^28.2.0", "@ag-grid-community/core": "^28.2.0", "@ag-grid-community/csv-export": "^28.2.0", "@ag-grid-community/infinite-row-model": "^28.2.0", "@ag-grid-community/react": "^28.2.0", "@ag-grid-enterprise/charts": "^28.2.0", "@ag-grid-enterprise/clipboard": "28.2.0", "@ag-grid-enterprise/column-tool-panel": "28.2.0", "@ag-grid-enterprise/excel-export": "^28.2.0", "@ag-grid-enterprise/filter-tool-panel": "28.2.0", "@ag-grid-enterprise/master-detail": "^28.2.0", "@ag-grid-enterprise/menu": "28.2.0", "@ag-grid-enterprise/range-selection": "28.2.0", "@ag-grid-enterprise/row-grouping": "^28.2.0", "@ag-grid-enterprise/server-side-row-model": "^28.2.0", "@ag-grid-enterprise/set-filter": "28.2.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/material": "^5.18.0", "@mui/x-tree-view": "^8.8.0", "@mui/icons-material": "^5.18.0", "@mui/x-date-pickers": "^5.0.17", "@types/jest": "^27.0.1", "@types/react": "^17.0.19", "@types/react-dom": "^17.0.9", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "date-fns": "^2.29.3", "formik": "^2.2.9", "formik-mui": "^5.0.0-alpha.0", "formik-mui-x-date-pickers": "^0.0.1", "framer-motion": "^6.3.11", "idb-keyval": "^6.2.0", "lodash": "^4.17.21", "moment": "^2.29.1", "react-draft-wysiwyg": "^1.14.7", "react-resizable-panels": "^0.0.36", "react-select": "^5.4.0", "react-select-async-paginate": "^0.6.2", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "ui-common": "npm:@tripudiotech/ui-common@^1.2.2", "ui-style": "npm:@tripudiotech/ui-style@^1.3.3", "usehooks-ts": "^2.9.1", "yup": "^0.32.11", "zustand": "^4.1.1", "dompurify": "^3.2.6"}, "types": "types/tripudiotech-styleguide.d.ts"}