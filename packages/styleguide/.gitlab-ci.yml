.styleguide-variables:
  variables:
    PACKAGE_DIR: packages/styleguide

aws-stag-styleguide-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .styleguide-variables

aws-stag-styleguide-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .styleguide-variables
  needs:
    - aws-stag-styleguide-package

gcp-stag-styleguide-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .styleguide-variables

gcp-stag-styleguide-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .styleguide-variables
  needs:
    - gcp-stag-styleguide-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-styleguide-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .styleguide-variables

gcp-uat-styleguide-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .styleguide-variables
  needs:
    - gcp-uat-styleguide-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json