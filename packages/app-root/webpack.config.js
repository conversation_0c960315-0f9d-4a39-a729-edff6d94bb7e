const { merge } = require('webpack-merge');
const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');
const singleSpaDefaults = require('webpack-config-single-spa-ts');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const dotenv = require('dotenv');

dotenv.config();

const BUILD_DIR = path.resolve(__dirname, 'dist');

module.exports = (webpackConfigEnv, argv) => {
    const orgName = 'tripudiotech';
    const defaultConfig = singleSpaDefaults({
        orgName,
        projectName: 'root-config',
        webpackConfigEnv,
        argv,
        disableHtmlGeneration: true,
    });
    const isLocal = (process.env.IS_LOCAL ?? 'true') === 'true';
    return merge(defaultConfig, {
        // modify the webpack config however you'd like to by adding to this object
        plugins: [
            new HtmlWebpackPlugin({
                inject: false,
                template: process.env.ENTRY_LOCATION || 'src/index.ejs',
                templateParameters: {
                    isLocal,
                    orgName,
                },
            }),
            new webpack.DefinePlugin({
                'process.env': JSON.stringify(process.env),
            }),
            new CopyPlugin({
                patterns: [
                    {
                        from: `public/assets`,
                        to: `${BUILD_DIR}/assets`,
                        noErrorOnMissing: true,
                    },
                ],
            }),
        ],
        devtool: false,
    });
};
