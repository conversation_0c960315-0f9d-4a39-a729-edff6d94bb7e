!function(){function t(t,e){return(e||"")+" (SystemJS https://git.io/JvFET#"+t+")"}!function(e){function r(){throw Error(t(5))}function n(){}function i(t,e){function n(t){f.push((function(e){u[t]=e.__useDefault?e.default:e}))}for(var i={},o={exports:i},u=[],f=[],s=0,l=0;t.length>l;l++){var a=t[l],c=f.length;"require"===a?(u[l]=r,s++):"module"===a?(u[l]=o,s++):"exports"===a?(u[l]=i,s++):n(l),s&&(t[c]=a)}s&&(t.length-=s);var g=e;return[t,function(t){return t({default:i,__useDefault:!0}),{setters:f,execute:function(){var e=g.apply(i,u);void 0!==e&&(o.exports=e),t(o.exports),t("default",o.exports)}}}]}function o(t,e){return t instanceof Array?[t,e]:"object"==typeof t?[[],function(){return t}]:"function"==typeof t?[g,t]:void 0}function u(t,e){s||(s=e,Promise.resolve().then((function(){s=null}))),f=e,System.registerRegistry[t]=System.getRegister(),f=null}var f,s,l,a=e.System.constructor.prototype,c=[[],function(){return{}}],g=["require","exports","module"],p=a.register;a.register=function(t,e,r){l="string"==typeof t?r:e,p.apply(this,arguments)};var y=a.instantiate;a.instantiate=function(){return v=null,y.apply(this,arguments)};var v,d,h=a.getRegister;a.getRegister=function(){if(f)return f;var t=s;s=null;var e=h.call(this);if(e&&e[1]===l)return e;var r=v;return v=null,t||(r?i(r,d):e||c)},e.define=function(e,r,f){var s;if("string"==typeof e){if(s=o(r,f),v){if(!System.registerRegistry)throw Error(t(6));return u(e,i(s[0],s[1])),v=[],void(d=n)}System.registerRegistry&&u(e,i([].concat(s[0]),s[1])),e=r,r=f}s=o(e,r),v=s[0],d=s[1]},e.define.amd={}}("undefined"!=typeof self?self:global)}();//# sourceMappingURL=amd.min.js.map
