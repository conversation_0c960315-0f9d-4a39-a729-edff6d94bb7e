<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="robots" content="noindex, nofollow" />
        <meta content="width=device-width, initial-scale=1" name="viewport" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <title>Glide SysLM</title>
        <!--
    Remove this if you only support browsers that support async/await.
    This is needed by babel to share largeish helper code for compiling async/await in older
    browsers. More information at https://github.com/single-spa/create-single-spa/issues/112
  -->
        <script src="/assets/js/runtime.min.js"></script>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link href="/assets/favicon-dark-16.png" rel="icon" type="image/png" media="(prefers-color-scheme: dark)" />
        <link href="/assets/favicon-dark-32.png" rel="icon" type="image/png" media="(prefers-color-scheme: dark)" />
        <link href="/assets/favicon-light-16.png" rel="icon" type="image/png" media="(prefers-color-scheme: light)" />
        <link href="/assets/favicon-light-32.png" rel="icon" type="image/png" media="(prefers-color-scheme: light)" />
        <link
            href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700&display=swap"
            rel="stylesheet"
            crossorigin="anonymous"
        />
        <!--
    This CSP allows any SSL-enabled host and for arbitrary eval(), but you should limit these directives further to increase your app's security.
    Learn more about CSP policies at https://content-security-policy.com/#directive
  -->
        <!-- <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' https: localhost:*; script-src 'unsafe-inline' 'unsafe-eval' https: localhost:*; connect-src https: localhost:* ws://localhost:*; style-src 'unsafe-inline' https:; object-src 'none';"
    /> -->
        <meta name="importmap-type" content="systemjs-importmap" />
        <!-- If you wish to turn off import-map-overrides for specific environments (prod), uncomment the line below -->
        <!-- More info at https://github.com/joeldenning/import-map-overrides/blob/master/docs/configuration.md#domain-list -->
        <!-- <meta name="import-map-overrides-domains" content="denylist:prod.example.com" /> -->

        <!-- Shared dependencies go into this import map. Your shared dependencies must be of one of the following formats:

    1. System.register (preferred when possible) - https://github.com/systemjs/systemjs/blob/master/docs/system-register.md
    2. UMD - https://github.com/umdjs/umd
    3. Global variable

    More information about shared dependencies can be found at https://single-spa.js.org/docs/recommended-setup#sharing-with-import-maps.
  -->
        <script crossorigin type="systemjs-importmap">
            {
                "imports": {
                    "single-spa": "/assets/js/single-spa.min.js",
                    "react": "/assets/js/react.production.min.js",
                    "react-dom": "/assets/js/react-dom.production.min.js"
                }
            }
        </script>
        <link
            rel="preload"
            href="/assets/js/single-spa.min.js"
            as="script"
        />

        <!-- Add your organization's prod import map URL to this script's src  -->
        <!-- <script type="systemjs-importmap" src="/importmap.json"></script> -->

        <% if (isLocal) { %>
        <script type="systemjs-importmap">
            {
                "imports": {
                    "@tripudiotech/admin-root-config": "//localhost:9000/tripudiotech-root-config.js",
                    "@tripudiotech/admin-header": "//localhost:9001/tripudiotech-header.js",
                    "@tripudiotech/admin-dashboard": "//localhost:9002/tripudiotech-dashboard.js",
                    "@tripudiotech/admin-schema": "//localhost:9003/tripudiotech-schema.js",
                    "@tripudiotech/admin-classification": "//localhost:9005/tripudiotech-classification.js",
                    "@tripudiotech/admin-business-rule": "//localhost:9008/tripudiotech-business-rule.js",
                    "@tripudiotech/admin-lifecycle": "//localhost:9009/tripudiotech-lifecycle.js",
                    "@tripudiotech/admin-role": "//localhost:9011/tripudiotech-role.js",
                    "@tripudiotech/admin-integration": "//localhost:9035/tripudiotech-integration.js",
                    "@tripudiotech/admin-user": "//localhost:9010/tripudiotech-user.js",
                    "@tripudiotech/admin-company": "//localhost:9035/tripudiotech-company.js",
                    "@tripudiotech/admin-sidebar": "//localhost:9004/tripudiotech-sidebar.js",
                    "@tripudiotech/admin-styleguide": "//localhost:9006/tripudiotech-styleguide.js",
                    "@tripudiotech/admin-caching-store": "//localhost:9015/tripudiotech-caching-store.js",
                    "@tripudiotech/admin-api": "//localhost:9007/tripudiotech-api.js",
                    "@tripudiotech/admin-notification": "//localhost:9019/tripudiotech-notification.js",
                    "@tripudiotech/admin-common": "//localhost:9022/tripudiotech-common.js",
                    "@tripudiotech/admin-process": "//localhost:9013/tripudiotech-process.js",
                    "@tripudiotech/admin-permission-management-role": "//localhost:9012/tripudiotech-permission-management-role.js",
                    "@tripudiotech/admin-user-group-management": "//localhost:9023/tripudiotech-user-group-management.js",
                    "@tripudiotech/admin-app-tool": "//localhost:9014/tripudiotech-app-tool.js",
                    "@tripudiotech/admin-global-setting": "//localhost:9020/tripudiotech-global-setting.js",
                    "@tripudiotech/admin-recycle-bin": "//localhost:9021/tripudiotech-recycle-bin.js"
                }
            }
        </script>
        <% } else { %>
        <script type="systemjs-importmap" src="/importmap.json"></script>
        <% } %>
        <!--
    If you need to support Angular applications, uncomment the script tag below to ensure only one instance of ZoneJS is loaded
    Learn more about why at https://single-spa.js.org/docs/ecosystem-angular/#zonejs
  -->

        <script src="/assets/js/import-map-overrides.js"></script>
        <% if (isLocal) { %>
        <script src="/assets/js/system.js"></script>
        <script src="/assets/js/amd.js"></script>
        <% } else { %>
        <script src="/assets/js/system.min.js"></script>
        <script src="/assets/js/amd.min.js"></script>
        <% } %>

        <template id="single-spa-layout">
            <single-spa-router base="admin">
                <div class="root-app">
                    <application name="@tripudiotech/admin-header" loader="main"></application>
                    <application name="@tripudiotech/admin-sidebar" loader="main" props="authentication"></application>
                    <application name="@tripudiotech/admin-notification" loader="main"></application>
                    <div class="root-app-content">
                        <redirect from="/" to="/admin/schema"></redirect>
                        <redirect from="/admin/dashboard" to="/admin/schema"></redirect>
                        <redirect from="/admin" to="/admin/schema"></redirect>
                        <route path="/">
                            <application name="@tripudiotech/admin-dashboard" loader="main"></application>
                        </route>
                        <route path="schema">
                            <application name="@tripudiotech/admin-schema" loader="main"></application>
                        </route>
                        <route path="digital-thread">
                            <application name="@tripudiotech/admin-schema" loader="main"></application>
                        </route>
                        <route path="classification">
                            <application name="@tripudiotech/admin-classification" loader="main"></application>
                        </route>
                        <route path="attribute">
                            <application name="@tripudiotech/admin-schema" loader="main"></application>
                        </route>
                        <route path="process">
                            <application name="@tripudiotech/admin-process" loader="main"></application>
                        </route>
                        <route path="business-rule">
                            <application name="@tripudiotech/admin-business-rule" loader="main"></application>
                        </route>
                        <route path="role">
                            <application name="@tripudiotech/admin-role" loader="main"></application>
                        </route>
                        <route path="integration">
                            <application name="@tripudiotech/admin-integration" loader="main"></application>
                        </route>
                        <route path="user">
                            <application name="@tripudiotech/admin-user" loader="main"></application>
                        </route>
                        <route path="company">
                            <application name="@tripudiotech/admin-company" loader="main"></application>
                        </route>
                        <route path="lifecycle">
                            <application name="@tripudiotech/admin-lifecycle" loader="main"></application>
                        </route>
                        <route path="permission-role">
                            <application name="@tripudiotech/admin-permission-management-role" loader="main"></application>
                        </route>
                        <route path="user-group-management">
                            <application name="@tripudiotech/admin-user-group-management" loader="main"></application>
                        </route>
                        <route path="app-tool">
                            <application name="@tripudiotech/admin-app-tool" loader="main"></application>
                        </route>
                        <route path="global-setting">
                            <application name="@tripudiotech/admin-global-setting" loader="main"></application>
                        </route>
                          <route path="recycle-bin">
                            <application name="@tripudiotech/admin-recycle-bin" loader="main"></application>
                        </route>
                        <route default>
                            <application name="@tripudiotech/admin-common" loader="main"></application>
                        </route>
                    </div>
                </div>
            </single-spa-router>
        </template>

        <style>
            div {
                box-sizing: border-box;
            }
            html,
            body {
                background-color: #ffffff;
                margin: 0;
                padding: 0;
                font-family: 'Work Sans';
                font-size: 16px;
            }
            @media only screen and (min-width: 0px) {
                html,
                body {
                    font-size: 14px;
                }
            }
            @media only screen and (min-width: 600px) {
                html,
                body {
                    font-size: 14px;
                }
            }
            @media (min-width: 900px) {
                html,
                body {
                    font-size: 16px;
                }
            }
            #initial-loading,
            #loading-container {
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                position: fixed;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            #initial-loading {
                background-color: #ffffff;
                z-index: 2147483645;
            }

            .loader {
                width: 40px;
                height: 40px;
                border: 3px solid #334466;
                border-bottom-color: transparent;
                border-radius: 50%;
                display: inline-block;
                box-sizing: border-box;
                background-color: #ffffff;
                animation: rotation 1s linear infinite;
            }

            @keyframes rotation {
                0% {
                    transform: rotate(0deg);
                }
                100% {
                    transform: rotate(360deg);
                }
            }

            .root-app {
                display: flex;
                max-width: 100vw;
                overflow-x: hidden;
                position: relative;
            }
            ::-webkit-scrollbar {
                width: 7px;
                height: 7px;
                background: '#434343';
            }

            /* Track */
            ::-webkit-scrollbar-track {
                box-shadow: 'inset 0 0 6px #334466';
                background: #dedede;
            }

            /* Handle */
            ::-webkit-scrollbar-thumb {
                background: #cacaca;
            }

            /* Handle on hover */
            ::-webkit-scrollbar-thumb:hover {
                background: #334466;
            }
            .root-app-content {
                -webkit-box-flex: 1;
                flex-grow: 1;
                overflow: hidden;
            }
            .Toastify__toast {
                font-family: 'Work Sans' !important;
            }
        </style>
    </head>

    <body id="page-top">
        <div id="initial-loading">
            <div class="loader"></div>
        </div>

        <noscript> You need to enable JavaScript to run this app. </noscript>

        <script>
            System.import('@tripudiotech/admin-root-config');
        </script>
        <import-map-overrides-full show-when-local-storage="devtools" dev-libs></import-map-overrides-full>
    </body>
</html>
