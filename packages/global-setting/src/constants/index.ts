/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

export const DATE_FORMAT_OPTIONS = [
    { label: 'MM/DD/YYYY (U.S.)', format: 'MM/DD/YYYY' },
    { label: 'DD/MM/YYYY (EU, Vietnam)', format: 'DD/MM/YYYY' },
    { label: 'YYYY-MM-DD (ISO 8601)', format: 'YYYY-MM-DD' },
    { label: 'DD.MM.YYYY (Germany, Russia)', format: 'DD.MM.YYYY' },
    { label: 'YYYY/MM/DD (China, Japan, Korea)', format: 'YYYY/MM/DD' },
    { label: 'DD-MM-YYYY (India)', format: 'DD-MM-YYYY' },
    { label: 'dddd, MMMM Do YYYY (Long form)', format: 'dddd, MMMM Do YYYY' },
];

export const DATE_TIME_FORMAT_OPTIONS = [
    { label: 'MM/DD/YYYY hh:mm A (U.S. 12h)', format: 'MM/DD/YYYY hh:mm A' },
    { label: 'MM/DD/YYYY HH:mm:ss (U.S. 24h)', format: 'MM/DD/YYYY HH:mm:ss' },
    { label: 'DD/MM/YYYY HH:mm (EU 24h)', format: 'DD/MM/YYYY HH:mm' },
    { label: 'YYYY-MM-DD HH:mm:ss (ISO style)', format: 'YYYY-MM-DD HH:mm:ss' },
    { label: 'DD.MM.YYYY HH:mm (Germany, Russia)', format: 'DD.MM.YYYY HH:mm' },
    { label: 'YYYY/MM/DD HH:mm (Asia 24h)', format: 'YYYY/MM/DD HH:mm' },
    { label: 'DD-MM-YYYY hh:mm A (India 12h)', format: 'DD-MM-YYYY hh:mm A' },
    { label: 'dddd, MMMM Do YYYY, h:mm:ss A (Long form)', format: 'dddd, MMMM Do YYYY, h:mm:ss A' },
];

export const NUMERIC_CONFIGS = ['retention.object.days', 'retention.asset.days'];
export const DATE_CONFIGS = ['formatting.date'];
export const DATETIME_CONFIGS = ['formatting.datetime'];
export const GLOBAL_SETTINGS = [...NUMERIC_CONFIGS, ...DATE_CONFIGS, ...DATETIME_CONFIGS];

export const GLOBAL_SETTINGS_DESCRIPTION: Record<(typeof GLOBAL_SETTINGS)[number], string> = {
    'retention.asset.days': 'Number of days to retain deleted Files',
    'retention.object.days': 'Number of days to retain deleted Entities',
    'formatting.date': 'Date format displayed on the UI',
    'formatting.datetime': 'Datetime format displayed on the UI',
};

export interface GlobalSetting {
    createdBy: string;
    createdDate: string;
    id: string;
    key: string;
    value: string;
    description: string;
}
