/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useRef, useEffect, useState, forwardRef, memo, useImperativeHandle } from 'react';
import { type ICellEditorParams } from '@ag-grid-community/core';
import {
    DATE_CONFIGS,
    DATE_FORMAT_OPTIONS,
    DATE_TIME_FORMAT_OPTIONS,
    DATETIME_CONFIGS,
    NUMERIC_CONFIGS,
} from '../constants';
import { MenuItem, Select } from 'ui-style';

// Numeric input component
const NumericInput = memo(
    forwardRef<any, { value: number; onChange: (value: number) => void }>(({ value, onChange }, ref) => (
        <input
            type="number"
            ref={ref}
            value={value}
            onChange={(event) => onChange(parseInt(event.target.value) || 0)}
            style={{ width: '100%', display: 'flex', height: '100%' }}
        />
    ))
);

// Date/Time select component
const DateTimeSelect = memo(
    forwardRef<any, { value: string; onChange: (value: string) => void; isDateTime: boolean }>(
        ({ value, onChange, isDateTime }, ref) => (
            <Select
                fullWidth
                ref={ref}
                size="small"
                value={value}
                onChange={(e) => onChange(e.target.value as string)}
                defaultOpen
            >
                {(isDateTime ? DATE_TIME_FORMAT_OPTIONS : DATE_FORMAT_OPTIONS).map((option) => (
                    <MenuItem key={option.format} value={option.format}>
                        {option.label}
                    </MenuItem>
                ))}
            </Select>
        )
    )
);

export const CellEditor = memo(
    forwardRef((props: ICellEditorParams, ref) => {
        const isNumeric = NUMERIC_CONFIGS.includes(props.data?.key);
        const isDateOrTime = [...DATETIME_CONFIGS, ...DATE_CONFIGS].includes(props.data?.key);
        const isDateTime = DATETIME_CONFIGS.includes(props.data?.key);

        const [value, setValue] = useState(isNumeric ? parseInt(props.value) || 0 : props.value);

        const refInput = useRef<HTMLInputElement>(null);

        useEffect(() => {
            refInput.current?.focus();
        }, []);

        useImperativeHandle(ref, () => {
            return {
                getValue() {
                    return value;
                },
                isCancelBeforeStart() {
                    return false;
                },
                isCancelAfterEnd() {
                    return false;
                },
            };
        });

        if (isNumeric) {
            return <NumericInput ref={refInput} value={value as number} onChange={setValue} />;
        }

        if (isDateOrTime) {
            return (
                <DateTimeSelect ref={refInput} value={value as string} onChange={setValue} isDateTime={isDateTime} />
            );
        }

        return <div />;
    })
);
