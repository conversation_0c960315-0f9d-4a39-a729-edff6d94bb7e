/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useRef, useMemo, useState } from 'react';
import { Box, styled, tableIcons, tableStyles, Loading, LoadingOverlay } from 'ui-style';
import { DEFAULT_TABLE_PAGINATION_SIZE, formatDateTime } from 'ui-common';
import { authenticationService } from '@tripudiotech/admin-api';
import { AgGridReact } from '@ag-grid-community/react';
import { type GridOptions } from '@ag-grid-community/core';
import get from 'lodash/get';
import { GlobalSetting, GLOBAL_SETTINGS, GLOBAL_SETTINGS_DESCRIPTION, NUMERIC_CONFIGS } from '../constants';
import { CellEditor } from '../components/CellEditor';

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    height: '100%',
    width: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const fetchGlobalSettings = async () => {
    const globalSettings = await authenticationService.getGlobalSettings();
    const mapOfSettingNameToSetting = new Map<string, GlobalSetting>();
    globalSettings.data?.forEach((setting) => mapOfSettingNameToSetting.set(setting.key, setting));
    const rowData: GlobalSetting[] = GLOBAL_SETTINGS.map((globalSetting) => {
        const setting = mapOfSettingNameToSetting.get(globalSetting);
        if (setting === undefined) {
            return {
                key: globalSetting,
                id: globalSetting,
                createdBy: undefined,
                createdDate: undefined,
                value: undefined,
                description: GLOBAL_SETTINGS_DESCRIPTION[globalSetting],
            };
        }
        return {
            ...setting,
            description: GLOBAL_SETTINGS_DESCRIPTION[globalSetting],
        };
    });
    return rowData;
};

const GlobalSettingList = () => {
    const gridRef = useRef<AgGridReact>(null);
    const [loading, setLoading] = useState(false);

    const gridOptions: GridOptions = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'key',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'value',
                    headerName: 'Value',
                    editable: true,
                    cellEditor: CellEditor,
                    onCellValueChanged: (params) => {
                        setLoading(true);
                        if (params.data.createdBy === undefined) {
                            authenticationService
                                .createGlobalSetting(params.data.key, params.newValue)
                                .catch((e) => {
                                    console.error(
                                        'Error while creating new global setting ' + params.data.key + ': ' + e
                                    );
                                })
                                .finally(() => {
                                    fetchGlobalSettings().then((data) => {
                                        gridRef.current.api.setRowData(data);
                                        setLoading(false);
                                    });
                                });
                        } else {
                            authenticationService
                                .updateGlobalSetting(params.data.key, params.data.id, params.newValue)
                                .catch((e) => {
                                    console.error(
                                        'Error while updating global setting for ' + params.data.key + ': ' + e
                                    );
                                })
                                .finally(() => {
                                    fetchGlobalSettings().then((data) => {
                                        gridRef.current.api.setRowData(data);
                                        setLoading(false);
                                    });
                                });
                        }
                    },
                },
                {
                    field: 'createdBy',
                    headerName: 'Created By',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'createdDate',
                    headerName: 'Created At',
                    filter: 'agTextColumnFilter',
                    valueGetter: (params) => {
                        if (params.data.createdDate !== undefined) {
                            return formatDateTime(get(params.data, 'createdDate'));
                        }
                    },
                },
                {
                    field: 'updatedDate',
                    headerName: 'Updated At',
                    filter: 'agTextColumnFilter',
                    valueGetter: (params) => {
                        if (params.data.updatedDate !== undefined) {
                            return formatDateTime(get(params.data, 'updatedDate'));
                        }
                    },
                },
            ],
            icons: tableIcons,
            cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
            rowModelType: 'clientSide',
            serverSideInfiniteScroll: true,
            suppressRowClickSelection: true,
            suppressPaginationPanel: true,
            pagination: true,
        };
    }, [gridRef]);

    return (
        <Box
            sx={{
                height: '100%',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <ContentWrapper>
                <Box className="ag-theme-alpine" sx={{ height: '100%' }}>
                    <AgGridReact
                        ref={gridRef}
                        {...gridOptions}
                        onGridReady={({ api }) => {
                            fetchGlobalSettings().then((data) => api.setRowData(data));
                        }}
                    />
                </Box>
            </ContentWrapper>
            {loading && <LoadingOverlay />}
        </Box>
    );
};

export default GlobalSettingList;
