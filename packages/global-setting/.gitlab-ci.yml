.global-setting-variables:
  variables:
    PACKAGE_DIR: packages/global-setting

aws-stag-global-setting-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .global-setting-variables

aws-stag-global-setting-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .global-setting-variables
  needs:
    - aws-stag-global-setting-package

gcp-stag-global-setting-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .global-setting-variables

gcp-stag-global-setting-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .global-setting-variables
  needs:
    - gcp-stag-global-setting-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-global-setting-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .global-setting-variables

gcp-uat-global-setting-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .global-setting-variables
  needs:
    - gcp-uat-global-setting-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json