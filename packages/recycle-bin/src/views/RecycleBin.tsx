/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useRef, useState } from "react";
import {
  formatDateTime,
  ISchemaDetail,
  buildQueryBasedOnFilter,
} from "ui-common";
import {
  Loading,
  tableIcons,
  Typography,
  AGGridTablePagination,
  Chip,
  MainTooltip,
  IconButton,
  FilterIcon,
  RefreshIcon,
} from "ui-style";
import { AgGridReact } from "@ag-grid-community/react";
import { type GridOptions, type ColDef } from "@ag-grid-community/core";
import { useParams } from "react-router-dom";
import get from "lodash/get";
import { useSchemaDetail, useDialog } from "@tripudiotech/admin-caching-store";
import { fetch, entityUrls } from "@tripudiotech/admin-api";
import { SchemaDetailViewContainer } from "@tripudiotech/admin-styleguide";
import {
  getDynamicFilterType,
  getStatus,
  StatusMapping,
} from "../utils/gridUtils";
import { CUSTOM_EVENTS } from "../constants";

// TODO: Move to ui-common once the all the endpoints used in admin support sort
// by multiple fields
export const buildSortParams = (sortModel) => {
  if (sortModel && sortModel.length > 0) {
    return {
      sort: sortModel.map((sortField) => {
        const { colId, sort } = sortField;
        // for lifecycle state we pass the filter as `state.name`
        // for the properties, we strip off properties from `properties.name`
        const field = colId.includes("state") ? colId : colId.split(".")[1];
        return `${field},${sort === "desc" ? "DESCENDING" : "ASCENDING"}`;
      }),
    };
  }
  return null;
};

const StatusCellRenderer = ({ data }) => {
  const { state } = data;
  const { status } = getStatus(state);
  const label = state?.name;
  return label ? (
    <Chip size="small" label={label} variant={StatusMapping[status].variant} />
  ) : null;
};

const DEFAULT_COL_DEFS: ColDef[] = [
  {
    field: "properties.name",
    headerName: "Name",
    filter: "agTextColumnFilter",
    checkboxSelection: true,
    showDisabledCheckboxes: true,
  },
  {
    field: "properties.type",
    headerName: "Description",
    filter: "agTextColumnFilter",
    hide: true,
  },
  {
    field: "state.name",
    headerName: "Status",
    cellRenderer: StatusCellRenderer,
    flex: 1,
    filter: false,
    minWidth: 100,
    headerTooltip: "Current state of the entity",
    cellStyle: {
      alignItems: "center",
      display: "flex",
    },
    hide: true, // TODO: hiding by default for now because for Discussion it shows as Active when soft deleted.
  },
  {
    field: "lifecycle.name",
    headerName: "Lifecycle",
    minWidth: 150,
    flex: 1,
    editable: false,
    filter: "agTextColumnFilter",
  },
  {
    field: "properties.description",
    headerName: "Description",
    filter: "agTextColumnFilter",
  },
  {
    field: "owner.name",
    headerName: "Owner",
    cellClass: "hyperlinks",
    filter: false,
    hide: false,
  },
  {
    field: "createdAt",
    headerName: "Created At",
    valueGetter: (params) => {
      return formatDateTime(get(params.data, "createdAt"));
    },
  },
  {
    field: "updatedAt",
    headerName: "Updated At",
    valueGetter: (params) => {
      return formatDateTime(get(params.data, "updatedAt"));
    },
  },
];

const buildColDefs = (schemaDetail: ISchemaDetail): ColDef[] => {
  const columnDefs: ColDef[] = [...DEFAULT_COL_DEFS];
  if (!schemaDetail?.attributes) return columnDefs;
  const attributes = Object.values(schemaDetail?.attributes);
  const existingAttributeNames = DEFAULT_COL_DEFS.map((colDef) => colDef.field);
  if (attributes && attributes.length > 0) {
    attributes.forEach((att) => {
      if (!existingAttributeNames.includes(`properties.${att.name}`)) {
        if (att.name === "revision") {
          columnDefs.push({
            field: "properties.revision",
            headerName: "Revision",
            filter: "agTextColumnFilter",
            cellStyle: () => {
              return {
                color: "#52C41A",
              };
            },
          });
        } else {
          columnDefs.push({
            field: `properties.${att.name}`,
            headerName: att.displayName || att.name,
            filter: getDynamicFilterType(att),
            headerTooltip: att.description || att.displayName,
          });
        }
      }
    });
  }
  return columnDefs;
};

const GRID_OPTIONS: GridOptions = {
  animateRows: true,
  loadingOverlayComponent: Loading,
  defaultColDef: {
    sortable: true,
    resizable: true,
    flex: 1,
    filter: true,
    floatingFilter: false,
    enableRowGroup: true,
    editable: false,
    cellStyle: () => ({
      display: "block",
      alignItems: "center",
    }),
    minWidth: 140,
  },
  rowModelType: "serverSide",
  rowSelection: "single",
  serverSideInfiniteScroll: true,
  headerHeight: 32,
  rowHeight: 32,
  icons: tableIcons,
  enableCellChangeFlash: true,
  pagination: true,
  suppressPaginationPanel: true,
};

const RecycleBin = () => {
  const { schemaName } = useParams();
  const [schemaDetail, getSchema, schemaDetailLoaded]: [
    ISchemaDetail,
    (entityName: string, signal?: any) => Promise<ISchemaDetail>,
    boolean
  ] = useSchemaDetail((state) => [
    state.schema[schemaName],
    state.getSchema,
    state.isLoaded,
  ]);
  const onOpenDialog = useDialog((state) => state.onOpenDialog);

  const [totalRows, setTotalRows] = useState<number>(0);
  const gridRef = useRef<AgGridReact>(null);
  const [selectedRows, setSelectedRows] = useState([]);

  const toggleColumnFilters = () => {
    let colDefs = gridRef.current.api.getColumnDefs();
    const enabled = !Boolean(
      colDefs.some((colDef: any) => colDef.floatingFilter)
    );

    colDefs.forEach((colDef: any) => {
      colDef.floatingFilter = enabled;
    });
    gridRef.current.api.setColumnDefs(colDefs);
    gridRef.current.api.setAutoGroupColumnDef({
      floatingFilter: enabled,
    });
    gridRef.current.api.refreshHeader();
  };

  const handleRestore = useCallback(() => {
    if (selectedRows.length === 0) return;
    const entityToRestore = selectedRows[0];
    const message = schemaDetail.masterModel
      ? `<b>${entityToRestore?.properties?.name}</b> is a Master-Revision model. Restoring it will restore all the other deleted revisions. Do you really want to restore the entity?`
      : `Do you really want to restore the entity <b>${entityToRestore?.properties?.name}</b>?`;
    onOpenDialog(
      `Restore ${entityToRestore?.properties?.type} Entity`,
      message,
      () => {
        if (entityToRestore?.id) {
          fetch({
            ...entityUrls.restoreDeletedEntity,
            params: { id: entityToRestore.id },
            successMessage: `Successfully restored <a target="_blank" href="/detail/${entityToRestore?.properties?.type}/${entityToRestore.id}"><b>${entityToRestore?.properties?.name}</b></a>`,
            errorMessage: `Error restoring ${entityToRestore.name}`,
          }).then(() => {
            gridRef.current.api.refreshServerSide();
            window.dispatchEvent(
              new CustomEvent(CUSTOM_EVENTS.REFERSH_DELETED_STATS)
            );
          });
        }
      },
      "error"
    );
  }, [selectedRows, onOpenDialog, schemaDetail]);

  const createServerSideDataSource = useCallback(
    (api) => {
      const buildParams = (params) => {
        const filterModel = params.filterModel;
        let queryParams = {
          offset: params.startRow || 0,
          limit: api.paginationGetPageSize(),
          ...buildSortParams(params.sortModel),
        };
        if (filterModel && Object.keys(filterModel).length > 0) {
          const filterConditions = [];
          queryParams["query"] = JSON.stringify(
            buildQueryBasedOnFilter(filterConditions, filterModel)
          );
        }
        return queryParams;
      };
      return {
        getRows: (params) => {
          gridRef.current?.api.showLoadingOverlay();
          fetch({
            ...entityUrls.getListDeletedEntity,
            qs: buildParams(params?.request),
            params: {
              entityType: schemaName,
            },
          })
            .then((response) => {
              if (response.status !== 200) {
                params.failCallback();
                return;
              }
              const rowsThisPage = response.data.data;
              const rowCount = response.data.pageInfo.total;
              params.success({ rowData: rowsThisPage, rowCount });
              setTotalRows(rowCount);
            })
            .finally(() => {
              gridRef.current?.api.hideOverlay();
              if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                gridRef.current?.api.showNoRowsOverlay();
              }
            });
        },
      };
    },
    [schemaName]
  );

  const handleSetDataSource = useCallback(
    (schemaDetailRes) => {
      const dataSource = createServerSideDataSource(gridRef.current?.api);
      gridRef.current?.api.setServerSideDatasource(dataSource);
      gridRef.current?.api.setColumnDefs(buildColDefs(schemaDetailRes));
    },
    [createServerSideDataSource]
  );

  useEffect(() => {
    let abortController = new AbortController();
    if (schemaName) {
      getSchema(schemaName, abortController.signal).then((schemaDetailRes) => {
        handleSetDataSource(schemaDetailRes);
      });
    }
    return () => {
      gridRef?.current?.api?.deselectAll();
      abortController.abort();
    };
  }, [schemaName]);

  if (!schemaDetailLoaded) {
    return <Loading />;
  }

  return (
    <SchemaDetailViewContainer>
      <div style={{ padding: "0.5rem", paddingLeft: "1rem", display: "flex" }}>
        <Typography
          variant="label2-med"
          sx={{
            borderRight: (theme) =>
              `1px solid ${theme.palette.glide.stroke.normal.primary}`,
            paddingRight: "1rem",
            marginTop: "auto",
            marginBottom: "auto",
          }}
        >{`${totalRows} Result(s)`}</Typography>
        <div style={{ display: "flex", gap: "4px" }}>
          <MainTooltip title="Toggle Column Filters">
            <IconButton
              size="small"
              color="primary"
              sx={{ color: (theme) => theme.palette.glide.text.normal.main }}
              onClick={toggleColumnFilters}
            >
              <FilterIcon />
            </IconButton>
          </MainTooltip>
          <MainTooltip
            title={
              selectedRows?.length === 1
                ? "Restore Selected Entity"
                : "Select an Entity to Restore"
            }
          >
            <span>
              <IconButton
                disabled={selectedRows?.length === 0}
                size="small"
                color="primary"
                sx={{ color: (theme) => theme.palette.glide.text.normal.main }}
                onClick={handleRestore}
              >
                <RefreshIcon />
              </IconButton>
            </span>
          </MainTooltip>
        </div>
      </div>
      <div className="content ag-theme-alpine">
        <AgGridReact
          gridOptions={GRID_OPTIONS}
          ref={gridRef}
          onGridReady={() => handleSetDataSource(schemaDetail)}
          onSelectionChanged={() =>
            setSelectedRows(gridRef.current.api.getSelectedRows())
          }
        />
      </div>
      <AGGridTablePagination gridRef={gridRef} totalRows={totalRows} />
    </SchemaDetailViewContainer>
  );
};

export default RecycleBin;
