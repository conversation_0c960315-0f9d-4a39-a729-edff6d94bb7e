/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  CloseIcon,
  InputAdornment,
  SearchIcon,
  TextField,
  Typography,
  NoRowsOverlay,
  DocumentIcon,
} from "ui-style";
import { AgGridReact } from "@ag-grid-community/react";
import isEmpty from "lodash/isEmpty";
import get from "lodash/get";
import {
  TreeContainer,
  getContextMenuCallback,
  getIndentClass,
} from "@tripudiotech/admin-styleguide";
import { generatePath, Link, useMatch } from "react-router-dom";
import { RowNode, type GridOptions } from "@ag-grid-community/core";
import { ISchemaDetail } from "ui-common";

const PATH_PATTERN = "recycle-bin/:schemaName/*";

const EntityTypeRenderer = ({ value, data }) => {
  const entityType = get(data, "name");
  const matched = useMatch(PATH_PATTERN);
  const params: any = get(matched, "params", {});
  const path = generatePath(PATH_PATTERN, {
    ...params,
    schemaName: entityType,
  });
  return (
    <Link className="item" to={`/${path}`}>
      <DocumentIcon />
      <Typography variant="label2-sem">{value}</Typography>
      <Typography variant="label2-reg">({entityType})</Typography>
      <Typography variant="label2-sem">({data.count ?? ""})</Typography>
    </Link>
  );
};

const GRID_OPTIONS: GridOptions = {
  rowDragMultiRow: true,
  animateRows: true,
  defaultColDef: {
    sortable: true,
    resizable: true,
    filter: true,
    floatingFilter: false,
  },
  getRowId: (params) => {
    return params.data.id;
  },
  columnDefs: [
    {
      field: "displayName",
      headerName: "Display Name",
      filter: "agTextColumnFilter",
      hide: true,
    },
  ],
  rowModelType: "clientSide",
  rowSelection: "multiple",
  getRowStyle: () => ({
    background: " #FFFFFF",
    border: "none",
  }),
  treeData: true,
  enableCellChangeFlash: true,
  headerHeight: 0,
  rowHeight: 26,
};

const SchemaTree = ({ schemaTree }) => {
  const [searchText, setSearchText] = useState("");
  const gridRef = useRef<AgGridReact>();
  const matched = useMatch("recycle-bin/:schemaName/:view/*");
  const selectedType = get(matched, ["params", "schemaName"]);

  const onSearchChanged = useCallback((e) => {
    const { value } = e.target;
    setSearchText(value);
  }, []);

  const getDataPath = useCallback((data) => {
    return data.path || [];
  }, []);

  const isGroupOpenByDefault = useCallback((params) => {
    return params.level === 0;
  }, []);

  const onRowGroupOpened = () => {
    gridRef.current.columnApi.autoSizeAllColumns();
  };

  const autoGroupColumnDef = useMemo(
    () => ({
      field: "displayName",
      headerName: "Name",
      cellClass: getIndentClass,
      minWidth: 308,
      filter: "agTextColumnFilter",
      cellRendererParams: {
        innerRenderer: EntityTypeRenderer,
        suppressCount: true,
      },
      cellStyle: (params) => {
        const { level } = params.node;
        const groupCell = params.value === params.node.key;
        const indent = 24;
        return groupCell
          ? {
              border: "none",
              paddingLeft: "-" + indent * (level + 1) + "px",
            }
          : {
              border: "none",
            };
      },
    }),
    []
  );

  const onFirstDataRendered = useCallback(() => {
    // expand and select by default
    if (selectedType) {
      let selectedNode: RowNode<ISchemaDetail>;
      gridRef.current.api.forEachNode((node: RowNode) => {
        if (node.data.name === selectedType) {
          selectedNode = node;
          return;
        }
      });
      selectedNode.setSelected(true);
      if (selectedNode) {
        let parent = selectedNode;
        while (parent) {
          parent.setExpanded(true);
          parent = parent.parent;
        }
      }
    }
    gridRef.current.columnApi.applyColumnState({
      state: [{ colId: "displayName", sort: "asc" }],
    });
    onRowGroupOpened();
  }, [selectedType]);

  useEffect(() => {
    if (gridRef.current?.api) {
      gridRef.current.api.setFilterModel({
        displayName: {
          filterType: "text",
          type: "contains",
          filter: searchText,
        },
      });
    }
  }, [searchText]);

  useEffect(() => {
    if (schemaTree && selectedType && gridRef.current?.api) {
      gridRef.current.api.forEachNode((node) => {
        if (node.data.name === selectedType) {
          gridRef.current.api.deselectAll();
          node.setSelected(true);
          gridRef.current.api.ensureIndexVisible(node.rowIndex, "middle");
          return;
        }
      });
    }
  }, [schemaTree, selectedType]);

  return (
    <TreeContainer>
      <TextField
        className="searchBox"
        size="medium"
        value={searchText}
        onChange={onSearchChanged}
        fullWidth
        placeholder="Type to search"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment
              position="end"
              style={{
                visibility: isEmpty(searchText) ? "hidden" : "visible",
                cursor: "pointer",
              }}
              onClick={() => setSearchText("")}
            >
              <CloseIcon sx={{ width: "16px", height: "16px" }} />
            </InputAdornment>
          ),
        }}
      />
      <div className="agContainer ag-theme-alpine">
        <AgGridReact
          ref={gridRef}
          gridOptions={GRID_OPTIONS}
          rowData={schemaTree}
          isGroupOpenByDefault={isGroupOpenByDefault}
          onRowGroupOpened={onRowGroupOpened}
          onExpandOrCollapseAll={onRowGroupOpened}
          getContextMenuItems={getContextMenuCallback(gridRef)}
          getDataPath={getDataPath}
          onFirstDataRendered={onFirstDataRendered}
          autoGroupColumnDef={autoGroupColumnDef}
          noRowsOverlayComponent={NoRowsOverlay}
          noRowsOverlayComponentParams={{
            message: "Currently, there are no Deleted Entities in the system.",
          }}
        />
      </div>
    </TreeContainer>
  );
};

export default SchemaTree;
