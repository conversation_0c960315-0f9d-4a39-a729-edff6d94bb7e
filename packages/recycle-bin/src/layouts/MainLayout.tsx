/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo, useState } from "react";
import {
  Box,
  Button,
  ContentHeader,
  FixedHeightContainer,
  styled,
  AnimatedPage,
  LoadingOverlay,
  tableStyles,
} from "ui-style";
import { useSchemaTree } from "@tripudiotech/admin-caching-store";
import { fetch, entityUrls, SYSTEM_ENTITY_TYPE } from "@tripudiotech/admin-api";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { Outlet } from "react-router-dom";
import SchemaTree from "../views/SchemaTree";
import { CUSTOM_EVENTS } from "../constants";

const ContentWrapper = styled(Box)(({ theme }) => ({
  ...tableStyles,
  display: "flex",
  height: "100%",
  "& .handle": {
    position: "absolute",
    height: "100%",
    width: "8px",
    right: 0,
    top: 0,
  },
}));

const MainLayout = () => {
  const { isLoaded, schemaTreeMap, getSchemaTree } = useSchemaTree();
  const [deletedStats, setDeletedStats] = useState({});
  const [loadingDeletedTree, setLoadingDeletedTree] = useState(true);
  const deletedTree = useMemo(() => {
    if (schemaTreeMap) {
      const deletedEntities = Object.keys(deletedStats);
      const deletedTree = {};
      deletedEntities.forEach((deletedEntity) => {
        if (
          !schemaTreeMap[deletedEntity]?.path?.includes(SYSTEM_ENTITY_TYPE.BOM)
        ) {
          schemaTreeMap[deletedEntity]?.path.forEach((parent) => {
            if (!deletedTree[parent]) {
              deletedTree[parent] = schemaTreeMap[parent];
              deletedTree[parent].count = deletedStats[deletedEntity];
            } else {
              deletedTree[parent].count += deletedStats[deletedEntity];
            }
          });
        }
      });
      return Object.values(deletedTree);
    }
    return [];
  }, [schemaTreeMap, deletedStats]);

  useEffect(() => {
    const refreshDeletedStats = () => {
      setLoadingDeletedTree(true);
      fetch({ ...entityUrls.getDeletedStats }).then((res) => {
        setDeletedStats(res.data);
        setLoadingDeletedTree(false);
      });
    };
    getSchemaTree();
    refreshDeletedStats();

    window.addEventListener(
      CUSTOM_EVENTS.REFERSH_DELETED_STATS,
      refreshDeletedStats
    );
    return () => {
      window.removeEventListener(
        CUSTOM_EVENTS.REFERSH_DELETED_STATS,
        refreshDeletedStats
      );
    };
  }, []);

  return isLoaded ? (
    <AnimatedPage>
      {!isLoaded || loadingDeletedTree ? (
        <LoadingOverlay />
      ) : (
        <FixedHeightContainer>
          <ContentHeader title="Recycle Bin" />
          <ContentWrapper>
            <PanelGroup direction="horizontal" autoSaveId="schema-panels">
              <Panel
                id="schema-tree-panel"
                defaultSize={33}
                minSize={5}
                maxSize={80}
                style={{ height: "100%", position: "relative" }}
              >
                <SchemaTree schemaTree={deletedTree} />
                <PanelResizeHandle className="handle"></PanelResizeHandle>
              </Panel>
              <Panel
                id="recycle-bin-detail-panel"
                style={{ height: "100%", position: "relative" }}
              >
                <Outlet />
              </Panel>
            </PanelGroup>
          </ContentWrapper>
        </FixedHeightContainer>
      )}
    </AnimatedPage>
  ) : (
    <LoadingOverlay />
  );
};

export default MainLayout;
