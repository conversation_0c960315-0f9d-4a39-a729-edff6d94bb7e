/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { AttributeSchema, AttributeType } from "ui-common";
import get from "lodash/get";

export const getDynamicFilterType = (attribute: AttributeSchema) => {
  const { type } = attribute;
  const enumRange = get(attribute, ["constraint", "enumRange"], []);
  if (enumRange && enumRange?.length > 0) return "agSetColumnFilter";

  if (type === AttributeType.DATE || type === AttributeType.DATE_TIME) {
    return "agDateColumnFilter";
  }

  if (
    type === AttributeType.INTEGER ||
    type === AttributeType.LONG ||
    type === AttributeType.FLOAT
  ) {
    return "agNumberColumnFilter";
  }

  return "agTextColumnFilter";
};

export const StatusMapping = {
  start: {
    variant: "status-initial",
  },
  inProgress: {
    variant: "status-info",
  },
  released: {
    variant: "status-success",
  },
  obsoleted: {
    variant: "status-secondary",
  },
  superseded: {
    variant: "status-warning",
  },
};

export const getStatus = (state: any) => {
  if (state?.isObsoleted) {
    return {
      status: "obsoleted",
      label: "Obsoleted",
    };
  }

  if (state?.isOfficial) {
    return {
      status: "released",
      label: "Released",
    };
  }

  if (state?.isSuperseded) {
    return {
      status: "superseded",
      label: "Superseded",
    };
  }

  return {
    status: "start",
    label: "Start",
  };
};
