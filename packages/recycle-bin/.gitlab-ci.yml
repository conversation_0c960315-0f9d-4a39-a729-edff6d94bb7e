.recycle-bin-variables:
  variables:
    PACKAGE_DIR: packages/recycle-bin

aws-stag-recycle-bin-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .recycle-bin-variables

aws-stag-recycle-bin-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .recycle-bin-variables
  needs:
    - aws-stag-recycle-bin-package

gcp-stag-recycle-bin-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .recycle-bin-variables

gcp-stag-recycle-bin-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .recycle-bin-variables
  needs:
    - gcp-stag-recycle-bin-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-recycle-bin-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .recycle-bin-variables

gcp-uat-recycle-bin-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .recycle-bin-variables
  needs:
    - gcp-uat-recycle-bin-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json