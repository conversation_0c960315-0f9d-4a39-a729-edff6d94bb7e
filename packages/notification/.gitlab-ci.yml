.notification-variables:
  variables:
    PACKAGE_DIR: packages/notification

aws-stag-notification-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .notification-variables

aws-stag-notification-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .notification-variables
  needs:
    - aws-stag-notification-package

gcp-stag-notification-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .notification-variables

gcp-stag-notification-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .notification-variables
  needs:
    - gcp-stag-notification-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-notification-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .notification-variables

gcp-uat-notification-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .notification-variables
  needs:
    - gcp-uat-notification-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json