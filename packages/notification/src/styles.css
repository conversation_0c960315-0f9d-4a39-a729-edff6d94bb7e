.Toastify__toast {
  border-radius: 0;
  color: #161616;
  font-weight: 400;
  font-family: Work Sans, sans-serif;
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0.16px;
}

.Toastify__toast-container--top-right {
  width: auto;
  max-width: 450px;
}
.Toastify__toast-container--bottom-left {
  width: auto;
  max-width: 600px;
  left: 82px;
  bottom: 0px;
}

.Toastify__toast--success {
  background: #F6FFED;
}

.Toastify__progress-bar--success {
  background: #52C41A;
  height: 2px;
}

.Toastify__toast--error {
  background: #FFF1F0;
}

.Toastify__progress-bar--error {
  background: #F5222D;
  height: 2px;
}

.Toastify__toast--warning {
  background: #FEFFE6;
}

.Toastify__progress-bar--warning {
  background: #FADB14;
  height: 2px;
}

.Toastify__toast--info {
  background: #F0F5FF;
}

.Toastify__progress-bar--info {
  background: #2F54EB;
  height: 2px;
}

.Toastify__toast--info .Toastify__toast-icon svg {
  fill: #2F54EB;
}