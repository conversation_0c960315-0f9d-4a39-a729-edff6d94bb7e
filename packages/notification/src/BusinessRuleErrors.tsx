/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import {
    Dialog,
    DialogContent,
    DialogContentText,
    DialogTitle,
    IconButton,
    Slide,
    Typography,
    CloseIcon,
} from 'ui-style';
import { create } from 'zustand';
import RuleRenderer from './RuleRenderer';

const Transition = React.forwardRef(function Transition(
    props: any & {
        children: React.ReactElement;
    },
    ref: React.Ref<unknown>
) {
    return <Slide direction="up" ref={ref} {...props} />;
});

type Rule = {
    id: string;
    name: string;
    description: string;
    message: string;
};

export type BusinessRuleDialog = {
    open: boolean;
    rules: Rule[];
    setRuleErrors: (rules: Rule[]) => void;
    setOpenRuleErrors: (open: boolean) => void;
    onClose: () => void;
};

export const useBusinessRuleDialog = create<BusinessRuleDialog>((set, get) => ({
    open: false,
    rules: [],
    setRuleErrors: (rules: Rule[]) => {
        set({ rules });
    },
    setOpenRuleErrors: (open: boolean) => {
        set({ open });
    },
    onClose: () => {
        set({
            open: false,
            rules: [],
        });
    },
}));

const BusinessRuleErrors = () => {
    const { open, rules, onClose } = useBusinessRuleDialog();
    return (
        <Dialog
            open={open}
            TransitionComponent={Transition}
            keepMounted
            disableEnforceFocus
            onClose={onClose}
            aria-describedby="alert-dialog-slide-description"
            PaperProps={{
                sx: {
                    borderRadius: 0,
                },
            }}
            sx={{
                zIndex: 2001,
            }}
        >
            <DialogTitle
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    p: '24px',
                    backgroundColor: (theme) => theme.palette.glide.background.normal.quarternary,
                    color: (theme) => theme.palette.glide.text.normal.tertiary,
                    minWidth: '320px',
                }}
            >
                <Typography
                    sx={{
                        fontSize: '20px',
                        fontWeight: 600,
                    }}
                >
                    Business Rules
                </Typography>
                <IconButton
                    sx={{
                        alignSelf: 'flex-start',
                        color: 'inherit',
                    }}
                    onClick={onClose}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent
                sx={{
                    p: '0 24px',
                    my: '16px',
                    minWidth: {
                        xs: '320px',
                        md: '420px',
                    },
                }}
            >
                <DialogContentText
                    id="alert-dialog-slide-description"
                    sx={{
                        textAlign: 'left',
                        fontSize: '16px',
                        color: (theme) => theme.palette.glide.primary,
                        maxWidth: '400px',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '16px',
                    }}
                >
                    {rules.map((rule) => (
                        <RuleRenderer key={rule.id} rule={rule} invokedResult={rule} />
                    ))}
                </DialogContentText>
            </DialogContent>
        </Dialog>
    );
};

export default BusinessRuleErrors;
