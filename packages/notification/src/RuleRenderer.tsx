/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { Chip, Grow, styled, Typography } from 'ui-style';

export const RULE_STATUS = {
    VALID: 'Valid',
    INVALID: 'InValid',
};

const RuleWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    '& .title': {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    '& .name': {
        fontSize: '14px',
        fontWeight: 500,
        color: theme.palette.glide.text.tertiary,
    },
    '& .description': {
        fontSize: '12px',
        fontWeight: 400,
        color: theme.palette.glide.text.tertiary,
    },
    '& .status': {
        textTransform: 'uppercase',
    },
    '& .error': {
        fontSize: '12px',
        fontWeight: 400,
        color: theme.palette.error.main,
    },
    gap: '4px',
}));

const RuleRenderer = ({ rule, invokedResult }: { rule: any; invokedResult }) => {
    const status = invokedResult?.status;
    return (
        <RuleWrapper>
            <div className="title">
                <Typography className="name">{rule.name}</Typography>
                <Grow in={Boolean(invokedResult)}>
                    <Chip
                        className="status"
                        size="small"
                        variant={status === RULE_STATUS.VALID ? 'status-success-outlined' : 'status-error-outlined'}
                        label={status}
                    />
                </Grow>
            </div>
            <Typography className="description">{rule.description}</Typography>
            <Grow in={Boolean(invokedResult)}>
                <Typography className="error">{invokedResult?.message}</Typography>
            </Grow>
        </RuleWrapper>
    );
};

export default RuleRenderer;
