/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useRef } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import { NOTIFICATION_EVENT, RichTextRenderer } from '@tripudiotech/admin-styleguide';
import 'react-toastify/dist/ReactToastify.css';
import './styles.css';
import { Box, styled, Typography, Button, GlideThemeProvider } from 'ui-style';
import { useNavigate, BrowserRouter, Routes, Route, useLocation } from 'react-router-dom';
import { useDraftForm } from '@tripudiotech/admin-caching-store';
import BusinessRuleErrors, { useBusinessRuleDialog } from './BusinessRuleErrors';
import { DialogContainer } from './ConfirmDialog';

const ToastWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    width: '100%',
    fontSize: '14px',
    color: theme.palette.glide.text.tertiary,
    justifyContent: 'space-between',
    alignItems: 'center',
}));

const BaseToast = ({
    message,
    title,
    actionLabel = 'View',
    actionEnabled = false,
    onView = () => {},
    containerStyles = {},
}) => {
    return (
        <ToastWrapper sx={{ flexDirection: 'column' }}>
            <Box sx={{ textoverflow: 'ellipsis', overflow: 'hidden' }}>
                {title && <Typography sx={{ fontWeight: 600, fontSize: '14px' }}>{title}</Typography>}
                <Typography sx={{ fontWeight: 400, fontSize: '14px' }}>
                    {typeof message === 'string' ? (
                        <RichTextRenderer
                            style={{
                                fontSize: '14px',
                                color: '#161616',
                                fontWeight: 400,
                                lineHeight: '18px',
                            }}
                            html={message}
                        />
                    ) : (
                        message
                    )}
                </Typography>
            </Box>
            {actionEnabled && (
                <Button onClick={onView} size="small" variant="ghost">
                    {actionLabel}
                </Button>
            )}
        </ToastWrapper>
    );
};

const Container = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const pathname = useRef('');
    const initialized = useRef(false);
    const [forms, addForm, removeForm] = useDraftForm(({ forms, addForm, removeForm }) => [forms, addForm, removeForm]);
    const { setRuleErrors, setOpenRuleErrors } = useBusinessRuleDialog();
    useEffect(() => {
        pathname.current = location.pathname;
    }, [location]);

    const onView = (viewLocation) => {
        const { contextPath, fullPath, query } = viewLocation;
        if (pathname.current.startsWith(contextPath)) {
            navigate(pathname.current + query);
            return;
        }
        navigate(fullPath + query);
    };

    useEffect(() => {
        if (!initialized.current) {
            forms.forEach((form) => {
                const { id, options, title, content, viewLocation } = form;
                toast(
                    <BaseToast
                        title={title}
                        message={content}
                        actionEnabled={Boolean(viewLocation)}
                        onView={() => onView(viewLocation)}
                    />,
                    {
                        ...options,
                        toastId: id,
                        onClose: () => {
                            removeForm(id);
                        },
                    }
                );
            });
            initialized.current = true;
        }
    }, [forms]);

    const onOpenRuleErrors = useCallback(() => {
        setOpenRuleErrors(true);
    }, []);

    const handleNewNotification = useCallback((e) => {
        const { id, content, title, options, viewLocation, isForm, data } = e.detail;

        if (options?.type === NOTIFICATION_EVENT.DISMISS) {
            toast.dismiss(id);
            removeForm(id);
            return;
        }

        toast(
            <BaseToast
                title={title}
                message={content}
                actionEnabled={viewLocation}
                onView={() => onView(viewLocation)}
            />,
            {
                ...options,
                toastId: id,
                onClose: () => {
                    removeForm(id);
                },
            }
        );
        if (isForm) {
            addForm({
                ...e.detail,
                options,
            });
        }
    }, []);

    const handleBusinessRuleErrors = useCallback((e) => {
        const { id, content, options, rules, title } = e.detail;
        setRuleErrors(rules);

        if (options.skipDetails) {
            return toast(<BaseToast title={title} message={content} />, {
                ...options,
                toastId: id,
            });
        }
        return toast(
            <BaseToast
                title={content}
                message={
                    <ul style={{ margin: '4px', paddingTop: 0 }}>
                        {rules.map((rule) => (
                            <li style={{ fontSize: '12px' }} key={rule.id}>
                                {rule.message}
                            </li>
                        ))}
                    </ul>
                }
                onView={onOpenRuleErrors}
                actionEnabled={rules.length > 3}
                actionLabel="View More"
                containerStyles={{
                    flexDirection: 'column',
                }}
            />,
            {
                ...options,
                toastId: id,
                autoClose: false,
            }
        );
    }, []);

    useEffect(() => {
        window.addEventListener(NOTIFICATION_EVENT.NEW_NOTIFICATION, handleNewNotification);
        window.addEventListener(NOTIFICATION_EVENT.BUSINESS_RULE, handleBusinessRuleErrors);
        return () => {
            window.removeEventListener(NOTIFICATION_EVENT.NEW_NOTIFICATION, handleNewNotification);
            window.removeEventListener(NOTIFICATION_EVENT.BUSINESS_RULE, handleBusinessRuleErrors);
        };
    }, []);

    return (
        <>
            <ToastContainer />
        </>
    );
};

const Root = () => (
    <GlideThemeProvider>
        <BusinessRuleErrors />
        <DialogContainer />
        <BrowserRouter basename="admin">
            <Routes>
                <Route path="*" element={<Container />} />
            </Routes>
        </BrowserRouter>
    </GlideThemeProvider>
);

export default Root;
