import { Method } from "../services/fetch";
declare const ruleUrls: {
    getRules: {
        method: Method;
        url: string;
    };
    invokeRuleById: {
        method: Method;
        url: string;
    };
    invokeRuleByEntityType: {
        method: Method;
        url: string;
    };
    getRuleById: {
        method: Method;
        url: string;
    };
    createRule: {
        method: Method;
        url: string;
    };
    updateRule: {
        method: Method;
        url: string;
    };
    deleteRule: {
        method: Method;
        url: string;
    };
};
export default ruleUrls;
//# sourceMappingURL=index.d.ts.map