import { Classification } from '../../tripudiotech-api';
import { ISchemaDetail } from 'ui-common';
export default interface SysRoot {
    name: string;
    description: string;
    type: string;
    title: string;
    hasThumbnail: boolean;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    id: string;
    system: boolean;
    disabled: boolean;
}
export interface EntityDetail {
    id: string;
    alternates: any[];
    authoredIn?: {
        id: string;
        description: string;
        name: string;
        isDefault: boolean;
        isSystem: boolean;
        logo: string;
    };
    classifications: Classification[];
    createdAt: string;
    createdBy: string;
    updatedAt: string;
    updatedBy: string;
    disabled: boolean;
    entitySchema: ISchemaDetail;
    lifecycle: {
        name: string;
        id: string;
    };
    locked: boolean;
    lockedBy: string;
    owner: {
        id: string;
        name: string;
        type: string;
    };
    permissions: {
        canCheckIn: boolean;
        canCheckOut: boolean;
        canClassify: boolean;
        canConnectAsFromSide: boolean;
        canConnectAsToSide: boolean;
        canCopy: boolean;
        canCreate: boolean;
        canDelete: boolean;
        canDemote: boolean;
        canDisconnectAsFromSide: boolean;
        canDisconnectAsToSide: boolean;
        canDownload: boolean;
        canGrantAccess: boolean;
        canLock: boolean;
        canModify: boolean;
        canPromote: boolean;
        canPublish: boolean;
        canRead: boolean;
        canRevise: boolean;
        canRevokeAccess: boolean;
        canUnlock: boolean;
    };
    properties: Record<'type' | 'name' | 'title', any> & Record<string, any>;
    schemaType: string[];
    state: {
        name: string;
        id: string;
        isObsoleted: boolean;
        isOfficial: boolean;
        isSuperseded: boolean;
    };
}
//# sourceMappingURL=entity.model.d.ts.map