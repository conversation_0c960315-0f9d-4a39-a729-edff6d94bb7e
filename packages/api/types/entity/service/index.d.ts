import { Method } from '../../services/fetch';
declare const entityUrls: {
    getListEntity: {
        method: Method;
        url: string;
    };
    getListDeletedEntity: {
        method: Method;
        url: string;
    };
    restoreDeletedEntity: {
        method: Method;
        url: string;
    };
    getDeletedStats: {
        method: Method;
        url: string;
    };
    getEntityById: {
        method: Method;
        url: string;
    };
    getEntityAccesses: {
        method: Method;
        url: string;
    };
    createEntity: {
        method: Method;
        url: string;
    };
    updateEntity: {
        method: Method;
        url: string;
    };
    deleteEntity: {
        method: Method;
        url: string;
    };
    getEntityRelations: {
        method: Method;
        url: string;
    };
    createRelation: {
        method: Method;
        url: string;
    };
    deleteRelation: {
        method: Method;
        url: string;
    };
    getListEntitySysRoot: {
        method: Method;
        url: string;
    };
    getRelationsUnderEntity: {
        method: Method;
        url: string;
    };
    getAllRelationsUnderEntity: {
        method: Method;
        url: string;
    };
    getBOMList: {
        method: Method;
        url: string;
    };
    lockEntity: {
        method: Method;
        url: string;
    };
    unlockEntity: {
        method: Method;
        url: string;
    };
    grantPermission: {
        method: Method;
        url: string;
    };
    revokePermission: {
        method: Method;
        url: string;
    };
    whereUsed: {
        method: Method;
        url: string;
    };
    batchRequest: {
        method: Method;
        url: string;
    };
    swapBOM: {
        method: Method;
        url: string;
    };
    applyClassifications: {
        method: Method;
        url: string;
    };
    updateRelation: {
        method: Method;
        url: string;
    };
    promote: {
        method: Method;
        url: string;
    };
    demote: {
        method: Method;
        url: string;
    };
    revise: {
        method: Method;
        url: string;
    };
    generateEBom: {
        method: Method;
        url: string;
    };
    getAllEventType: {
        method: Method;
        url: string;
    };
};
export default entityUrls;
//# sourceMappingURL=index.d.ts.map