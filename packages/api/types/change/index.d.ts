import { Method } from "../services/fetch";
declare const changeUrls: {
    getIssueSummary: {
        method: Method;
        url: string;
    };
    getRevisionIssueSummary: {
        method: Method;
        url: string;
    };
    updateAttributePlan: {
        method: Method;
        url: string;
    };
    addComponentPlan: {
        method: Method;
        url: string;
    };
    deleteComponentPlan: {
        method: Method;
        url: string;
    };
    addContextualAlternatePlan: {
        method: Method;
        url: string;
    };
    addGlobalAlternatePlan: {
        method: Method;
        url: string;
    };
    removeContextualAlternatePlan: {
        method: Method;
        url: string;
    };
    removeGlobalAlternatePlan: {
        method: Method;
        url: string;
    };
    replaceComponentPlan: {
        method: Method;
        url: string;
    };
    swapComponentPlan: {
        method: Method;
        url: string;
    };
    getChangeRequestAffectedItems: {
        method: Method;
        url: string;
    };
    getChangeOrderAffectedItems: {
        method: Method;
        url: string;
    };
    getPlannedChangeBomSummary: {
        method: Method;
        url: string;
    };
    createChangeOrder: {
        method: Method;
        url: string;
    };
};
export default changeUrls;
//# sourceMappingURL=index.d.ts.map