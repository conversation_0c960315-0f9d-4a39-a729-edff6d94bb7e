import { Method } from "../services/fetch";
declare const assetServiceUrl: {
    checkinDocument: {
        method: Method;
        url: string;
    };
    documentCheckout: {
        method: Method;
        url: string;
    };
    getAllDocument: {
        method: Method;
        url: string;
    };
    deleteDocument: {
        method: Method;
        url: string;
    };
    previewFile: {
        method: Method;
        url: string;
    };
};
export default assetServiceUrl;
export declare const buildCheckInForm: ({ file, override, revise, lock, primary, }: {
    file: any;
    override?: string;
    revise?: string;
    lock?: string;
    primary?: string;
}) => FormData;
export declare const buildDownloadDocumentResponseError: (status: any) => "There's no document uploaded to this entity." | "An unexpected error has occurred while loading the document.";
//# sourceMappingURL=index.d.ts.map