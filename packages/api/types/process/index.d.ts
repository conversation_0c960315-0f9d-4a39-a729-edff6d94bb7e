import { Method } from "../services/fetch";
declare const processUrls: {
    getProcesses: {
        method: Method;
        url: string;
    };
    getTasks: {
        method: Method;
        url: string;
    };
    createTasks: {
        method: Method;
        url: string;
    };
    createProcess: {
        method: Method;
        url: string;
    };
    getProcessInstances: {
        method: Method;
        url: string;
    };
    getHistoryProcessInstances: {
        method: Method;
        url: string;
    };
    getHistoryProcessActivityInstances: {
        method: Method;
        url: string;
    };
    getProcessInstanceActivities: {
        method: Method;
        url: string;
    };
    getProcessDefinition: {
        method: Method;
        url: string;
    };
    getProcessInstanceIncidents: {
        method: Method;
        url: string;
    };
    getProcessTaskDetails: {
        method: Method;
        url: string;
    };
    getProcessTaskVariables: {
        method: Method;
        url: string;
    };
    completeTask: {
        method: Method;
        url: string;
    };
    queryTask: {
        method: Method;
        url: string;
    };
    updateTask: {
        method: Method;
        url: string;
    };
    getTaskComments: {
        method: Method;
        url: string;
    };
    addTaskComments: {
        method: Method;
        url: string;
    };
    getHistoryUserOperation: {
        method: Method;
        url: string;
    };
    getTaskAttachments: {
        method: Method;
        url: string;
    };
    createTaskAttachment: {
        method: Method;
        url: string;
    };
    downloadAttachment: {
        method: Method;
        url: string;
    };
    deleteAttachment: {
        method: Method;
        url: string;
    };
    claimTask: {
        method: Method;
        url: string;
    };
    setRetries: {
        method: Method;
        url: string;
    };
    applyProcess: {
        method: Method;
        url: string;
    };
    revokeProcess: {
        method: Method;
        url: string;
    };
    deployProcess: {
        method: Method;
        url: string;
    };
    updateProcess: {
        method: Method;
        url: string;
    };
    getProcessDetail: {
        method: Method;
        url: string;
    };
    deleteProcess: {
        method: Method;
        url: string;
    };
};
export default processUrls;
export declare const buildAttachmentForm: (file: any, description?: any) => FormData;
//# sourceMappingURL=index.d.ts.map