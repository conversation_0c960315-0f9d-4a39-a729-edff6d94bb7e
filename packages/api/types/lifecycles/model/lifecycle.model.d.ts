export default interface Lifecycle {
    id: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    description: string;
    disabled: boolean;
    default: boolean;
    createdBy: string;
    updatedBy: string;
}
export type LifecycleState = {
    id: string;
    name: string;
    description: string;
    isSuperseded: boolean;
    isObsoleted: boolean;
    isOfficial: boolean;
    nextStates?: string[];
};
//# sourceMappingURL=lifecycle.model.d.ts.map