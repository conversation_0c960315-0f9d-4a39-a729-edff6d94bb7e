import { Method } from "../services/fetch";
declare const integrationUrls: {
    importRouter: {
        method: Method;
        url: string;
    };
    reloadRoute: {
        method: Method;
        url: string;
    };
    getAvailableRoutes: {
        method: Method;
        url: string;
    };
    getConnectionTypes: {
        method: Method;
        url: string;
    };
    getConnectionTypeTemplates: {
        method: Method;
        url: string;
    };
    installConnectionType: {
        method: Method;
        url: string;
    };
    installConnectionTypeFromTemplate: {
        method: Method;
        url: string;
    };
    getConnectionTypeDetail: {
        method: Method;
        url: string;
    };
    getConnectionDetail: {
        method: Method;
        url: string;
    };
    getConnectionRouters: {
        method: Method;
        url: string;
    };
    importConnectionRouter: {
        method: Method;
        url: string;
    };
    updateRouter: {
        method: Method;
        url: string;
    };
    deleteRouter: {
        method: Method;
        url: string;
    };
    getRouter: {
        method: Method;
        url: string;
    };
    getConnectionsUnderConnectionType: {
        method: Method;
        url: string;
    };
    addConnection: {
        method: Method;
        url: string;
    };
    updateConnection: {
        method: Method;
        url: string;
    };
    deleteConnection: {
        method: Method;
        url: string;
    };
    uninstallConnectionType: {
        method: Method;
        url: string;
    };
};
export default integrationUrls;
//# sourceMappingURL=index.d.ts.map