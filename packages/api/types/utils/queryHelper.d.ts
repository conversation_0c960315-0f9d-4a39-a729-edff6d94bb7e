export declare const buildOrOperatorQuery: (queryList: any) => {
    $or: any;
};
export declare const buildAndOperatorQuery: (queryList: any) => {
    $and: any;
};
export declare const buildExactQuery: (field: any, value: any) => {
    $exact: {
        [x: number]: any;
    };
};
export declare const buildNotInQuery: (field: any, value: any) => {
    $not_in: {
        [x: number]: any;
    };
};
export declare const buildInQuery: (field: any, value: any) => {
    $in: {
        [x: number]: any;
    };
};
export declare const buildNotNullQuery: (field: any) => {
    $is_non_null: any;
};
export declare const buildNullQuery: (field: any) => {
    $is_null: any;
};
export declare const buildLessThanQuery: (field: any, value: any) => {
    $lt: {
        [x: number]: any;
    };
};
export declare const buildlessThanOrEqualQuery: (field: any, value: any) => {
    $lte: {
        [x: number]: any;
    };
};
export declare const buildGreaterThanQuery: (field: any, value: any) => {
    $gt: {
        [x: number]: any;
    };
};
export declare const buildGreaterThanOrEqualQuery: (field: any, value: any) => {
    $gte: {
        [x: number]: any;
    };
};
export declare const buildRegexQuery: (field: any, value: any) => {
    $regex: {
        [x: number]: any;
    };
};
export declare const buildContainsQuery: (field: string, value: string) => {
    $regex: {
        [x: number]: any;
    };
} | {
    $contains: {
        [x: string]: string;
    };
};
export declare const getCriteriasQuery: (key: any, item: any) => {
    $exact: {
        [x: number]: any;
    };
} | {
    $not_in: {
        [x: number]: any;
    };
} | {
    $in: {
        [x: number]: any;
    };
} | {
    $is_non_null: any;
} | {
    $is_null: any;
} | {
    $lt: {
        [x: number]: any;
    };
} | {
    $lte: {
        [x: number]: any;
    };
} | {
    $gt: {
        [x: number]: any;
    };
} | {
    $gte: {
        [x: number]: any;
    };
} | {
    $regex: {
        [x: number]: any;
    };
} | {
    $contains: {
        [x: string]: string;
    };
};
export declare const extractAndOrCriterias: (operation: any, condition: any) => {
    $or: any;
} | {
    $and: any;
};
export declare const QUERY: {
    readonly OR: "$or";
    readonly AND: "$and";
    readonly EXACT: "$exact";
    readonly NOT_IN: "$not_in";
    readonly IN: "$in";
    readonly IS_NULL: "$is_null";
    readonly IS_NON_NULL: "$is_non_null";
    readonly LT: "$lt";
    readonly LTE: "$lte";
    readonly GT: "$gt";
    readonly GTE: "$gte";
    readonly REGEX: "$regex";
    readonly CONTAINS: "$contains";
    readonly NOT_CONTAINS: "$not_contains";
    readonly EQUAL: "$eq";
    readonly NOT: "$not";
    readonly NOT_EQUAL: "$neq";
};
export interface QueryCondition {
    [QUERY.AND]: QueryCondition[];
    [QUERY.OR]: QueryCondition[];
    [QUERY.IN]: {
        [key: string]: string[];
    };
    [QUERY.NOT_IN]: {
        [key: string]: string[];
    };
    [QUERY.CONTAINS]: {
        [key: string]: string;
    };
    [QUERY.EXACT]: {
        [key: string]: string;
    };
    [QUERY.IS_NULL]: string;
    [QUERY.IS_NON_NULL]: string;
    [QUERY.LT]: {
        [key: string]: string;
    };
    [QUERY.LTE]: {
        [key: string]: string;
    };
    [QUERY.GT]: {
        [key: string]: string;
    };
    [QUERY.GTE]: {
        [key: string]: string;
    };
    [QUERY.REGEX]: {
        [key: string]: string;
    };
    [QUERY.EQUAL]: {
        [key: string]: string;
    };
    [QUERY.NOT_EQUAL]: {
        [key: string]: string;
    };
    [key: string]: any;
}
export declare const convertQueryObject: (query: Partial<QueryCondition>) => string;
export declare const convertQuery: (queryString: string) => string;
export declare const extractSchemaTypes: (criteria: any) => any;
//# sourceMappingURL=queryHelper.d.ts.map