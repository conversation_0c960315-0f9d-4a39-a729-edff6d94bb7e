export declare const CAD_3D_VIEW_ATTRIBUTE = "3dDocumentId";
export declare const SYSTEM_RELATION: {
    HAS_MASTER: string;
    HAS_ASSEMBLY: string;
    AFFECTED_ITEM: string;
    RESOLVED_BY: string;
    REPORTED_ON: string;
    LINKED_ISSUE: string;
    ALTERNATES_TO: string;
    CHANGING_ITEM: string;
    CHANGE_REQUEST: string;
    REFERENCE_TO: string;
    HAS_COMPONENT: string;
    SUBSTITUTES_TO: string;
    IMPLEMENTS: string;
    BELONGS_TO: string;
    TASK_OF: string;
    PROPOSAL_OF: string;
    ENGINEERING_REVISION_OF: string;
    IMPACT_ANALYSIS: string;
    WORKS_FOR: string;
    OWNED_BY: string;
};
export declare const SYSTEM_ENTITY_TYPE: {
    SYS_ROOT: string;
    DOCUMENT: string;
    DOCUMENT_MASTER: string;
    PART_MASTER: string;
    PART: string;
    CAD_PART: string;
    SPECIFICATION: string;
    ISSUE: string;
    CHANGE_REQUEST: string;
    PLANNED_CHANGE: string;
    CHANGE_ORDER: string;
    CHANGE_ITEM: string;
    TASK: string;
    PERSON: string;
    BOM: string;
    CAD_BOM: string;
    ENGINEERING_BOM: string;
    MANUFACTURING_BOM: string;
    IMPACT_ANALYSIS: string;
    COMPANY: string;
    INTERNAL_COMPANY: string;
    EXTERNAL_COMPANY: string;
    TEAM: string;
    DEPARTMENT: string;
    USER_GROUP: string;
};
export declare const CHANGE_ACTION: {
    UPDATE_ATTRIBUTE: string;
};
export declare enum RELATION_DIRECTION {
    OUTGOING = "Outgoing",
    INCOMING = "Incoming"
}
export declare const SYSTEM_ATTRIBUTE: {
    NAME: string;
    DISPLAY_NAME: string;
    DESCRIPTION: string;
};
export declare const DISTRIBUTION_LIST_RELATION: {
    CHANGE_MANAGEMENT_GROUP: string;
    FEEDBACK_PROVIDER: string;
    ANALYSIS_PERFORMER: string;
    DECISION_TEAM: string;
    AGENT: string;
};
export declare enum SORT_DIRECTION {
    DESCENDING = "DESCENDING",
    ASCENDING = "ASCENDING"
}
export declare const PERMISSION: {
    CAN_CHECK_IN: string;
    CAN_CHECK_OUT: string;
    CAN_CONNECT_AS_FROM_SIDE: string;
    CAN_CONNECT_AS_TO_SIDE: string;
    CAN_COPY: string;
    CAN_CREATE: string;
    CAN_DELETE: string;
    CAN_DEMOTE: string;
    CAN_DISCONNECT_AS_FROM_SIDE: string;
    CAN_DISCONNECT_AS_TO_SIDE: string;
    CAN_DOWNLOAD: string;
    CAN_GRANT_ACCESS: string;
    CAN_LOCK: string;
    CAN_MODIFY: string;
    CAN_PROMOTE: string;
    CAN_PUBLISH: string;
    CAN_READ: string;
    CAN_REVISE: string;
    CAN_REVOKE_ACCESS: string;
    CAN_UNLOCK: string;
    CAN_CLASSIFY: string;
};
export declare const PERMISSION_TYPES: {
    ANY: string;
    VIEWER: string;
    CONTRIBUTOR: string;
    OWNER: string;
    ADMIN: string;
};
//# sourceMappingURL=system-type.d.ts.map