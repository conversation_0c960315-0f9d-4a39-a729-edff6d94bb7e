import { Method } from '../services/fetch';
declare const userUrls: {
    getUsers: {
        method: Method;
        url: string;
    };
    createUser: {
        method: Method;
        url: string;
    };
    deleteUser: {
        method: Method;
        url: string;
    };
    getUser: {
        method: Method;
        url: string;
    };
    updateUser: {
        method: Method;
        url: string;
    };
    deactivateUser: {
        method: Method;
        url: string;
    };
    activateUser: {
        method: Method;
        url: string;
    };
    getRoles: {
        method: Method;
        url: string;
    };
    createRole: {
        method: Method;
        url: string;
    };
    deleteRole: {
        method: Method;
        url: string;
    };
    getAssignRole: {
        method: Method;
        url: string;
    };
    assignRole: {
        method: Method;
        url: string;
    };
    unassignRole: {
        method: Method;
        url: string;
    };
    getAppTools: {
        method: Method;
        url: string;
    };
    getUserGroups: {
        method: Method;
        url: string;
    };
    getUserUnderGroup: {
        method: Method;
        url: string;
    };
    createUserGroups: {
        method: Method;
        url: string;
    };
    updateUserGroups: {
        method: Method;
        url: string;
    };
    deleteUserGroups: {
        method: Method;
        url: string;
    };
    assignUserToGroups: {
        method: Method;
        url: string;
    };
    removeUserOutGroups: {
        method: Method;
        url: string;
    };
    deleteAppTool: {
        method: Method;
        url: string;
    };
    getAppTool: {
        method: Method;
        url: string;
    };
    createAppTool: {
        method: Method;
        url: string;
    };
};
export default userUrls;
//# sourceMappingURL=index.d.ts.map