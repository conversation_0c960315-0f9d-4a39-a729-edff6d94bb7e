import { Method } from '../../services/fetch';
declare const classificationUrls: {
    readonly getClassificationTree: {
        readonly method: Method.GET;
        readonly url: `${string}/classification`;
    };
    readonly createClassification: {
        readonly method: Method.POST;
        readonly url: `${string}/classification/:name/classification`;
    };
    readonly getClassificationDetail: {
        readonly method: Method.GET;
        readonly url: `${string}/classification/:name`;
    };
    readonly updateClassification: {
        readonly method: Method.PUT;
        readonly url: `${string}/classification/:name`;
    };
    readonly deleteClassification: {
        readonly method: Method.DELETE;
        readonly url: `${string}/classification/:name`;
    };
    readonly createClassificationAttribute: {
        readonly method: Method.POST;
        readonly url: `${string}/classification/:classificationName/attribute`;
    };
    readonly updateAttribute: {
        readonly method: Method.PUT;
        readonly url: `${string}/classification/:classificationName/attribute/:attributeName`;
    };
    readonly createUpdatableBy: {
        readonly method: Method.POST;
        readonly url: `${string}/classification/:classificationName/access`;
    };
    readonly deleteAttribute: {
        readonly method: Method.DELETE;
        readonly url: `${string}/classification/:classificationName/attribute/:attributeName`;
    };
    readonly getClassificationUpdatableBy: {
        readonly method: Method.GET;
        readonly url: `${string}/classification/:classificationName/access`;
    };
    readonly deleteClassificationUpdatableBy: {
        readonly method: Method.DELETE;
        readonly url: `${string}/classification/:classificationName/access`;
    };
};
export default classificationUrls;
//# sourceMappingURL=index.d.ts.map