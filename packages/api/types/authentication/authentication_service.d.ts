declare const authenticationService: {
    getToken(): string;
    getAuthUrl(): string;
    getTenant(): string;
    getUserInfo(): Promise<any>;
    getAvatar(email: any): string;
    getGlobalSettings(): Promise<any>;
    updateGlobalSetting(key: string, configId: string, value: string): Promise<import("axios").AxiosResponse<any, any>>;
    deleteGlobalSetting(configId: string): Promise<any>;
    createGlobalSetting(key: string, value: string): Promise<import("axios").AxiosResponse<any, any>>;
    getGlobalSettingById(id: string): Promise<import("axios").AxiosResponse<any, any>>;
    login(): void;
};
export default authenticationService;
//# sourceMappingURL=authentication_service.d.ts.map