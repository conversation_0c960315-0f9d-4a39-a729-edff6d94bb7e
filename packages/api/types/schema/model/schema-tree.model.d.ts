import Relation from "./relations.model";
export default interface SchemaTree {
    id: string;
    name: string;
    description: string;
    isSystem: boolean;
    isRevisable: boolean;
    isExtendable: boolean;
    isAbstract: boolean;
    isMaster: boolean;
    subTypes?: SchemaTree[];
    displayName?: string;
    uniqueKeys: string[];
    relations?: Relation[];
}
//# sourceMappingURL=schema-tree.model.d.ts.map