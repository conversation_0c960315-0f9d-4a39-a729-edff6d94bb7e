import { AttributeSchema } from 'ui-common';
export interface SchemaReference {
    name: string;
    description: string;
    id: string;
    type: 'Classification' | 'EntityType' | 'RelationType';
    displayName: string;
    fromEntityType?: string;
    toEntityType?: string;
}
export default interface AttributeDetail extends AttributeSchema {
    schemas: SchemaReference[];
}
//# sourceMappingURL=attribute-detail.model.d.ts.map