export declare enum AttributeType {
    STRING = "STRING",
    TEXT = "TEXT",
    BOOLEAN = "BOOLEAN",
    INTEGER = "INTEGER",
    LONG = "LONG",
    FLOAT = "FLOAT",
    DATE = "DATE",
    DATE_TIME = "DATE_TIME",
    ASYNC_SELECT = "ASYNC_SELECT",
    STRING_ARRAY = "STRING_ARRAY",
    FLOAT_ARRAY = "FLOAT_ARRAY",
    DATE_ARRAY = "DATE_ARRAY",
    DATE_TIME_ARRAY = "DATE_TIME_ARRAY",
    INTEGER_ARRAY = "INTEGER_ARRAY",
    BOOLEAN_ARRAY = "BOOLEAN_ARRAY",
    GROUP = "GROUP"
}
export default AttributeType;
//# sourceMappingURL=attribute-type.enum.d.ts.map