/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method } from "../services/fetch";
const BASE_URL = `${process.env.BASE_URL}/integration`;
const DATA_ROUTING_BASE_URL = `${process.env.BASE_URL}/data-routing`;

const integrationUrls = {
    importRouter: {
        method: Method.POST,
        url: `${BASE_URL}/connection/:connectionId/router/import`,
    },
    reloadRoute: {
        method: Method.POST,
        url: `${DATA_ROUTING_BASE_URL}/router/reload`,
    },
    getAvailableRoutes: {
        method: Method.GET,
        url: `${DATA_ROUTING_BASE_URL}/router`,
    },
    getConnectionTypes: {
        method: Method.GET,
        url: `${BASE_URL}/connection/type`,
    },
    getConnectionTypeTemplates: {
        method: Method.GET,
        url: `${BASE_URL}/connection/template`,
    },
    installConnectionType: {
        method: Method.POST,
        url: `${BASE_URL}/connection/register`,
    },
    installConnectionTypeFromTemplate: {
        method: Method.POST,
        url: `${BASE_URL}/connection/template/:templateId/type`,
    },
    getConnectionTypeDetail: {
        method: Method.GET,
        url: `${BASE_URL}/connection/type/:connectionTypeId`,
    },
    getConnectionDetail: {
        method: Method.GET,
        url: `${BASE_URL}/connection/:connectionId`,
    },
    getConnectionRouters: {
        method: Method.GET,
        url: `${BASE_URL}/connection/:connectionId/router`,
    },
    importConnectionRouter: {
        method: Method.POST,
        url: `${BASE_URL}/connection/:connectionId/router/import`,
    },
    updateRouter: {
        method: Method.PUT,
        url: `${BASE_URL}/router/:routerId`,
    },
    deleteRouter: {
        method: Method.DELETE,
        url: `${BASE_URL}/router/:routerId`,
    },
    getRouter: {
        method: Method.GET,
        url: `${BASE_URL}/router/:routerId`,
    },
    getConnectionsUnderConnectionType: {
        method: Method.GET,
        url: `${BASE_URL}/connection/type/:connectionTypeId/connection`,
    },
    addConnection: {
        method: Method.POST,
        url: `${BASE_URL}/connection/type/:connectionTypeId/connection`,
    },
    updateConnection: {
        method: Method.PUT,
        url: `${BASE_URL}/connection/type/:connectionTypeId/connection/:connectionId`,
    },
    deleteConnection: {
        method: Method.DELETE,
        url: `${BASE_URL}/connection/type/:connectionTypeId/connection/:connectionId`,
    },
    uninstallConnectionType: {
        method: Method.DELETE,
        url: `${BASE_URL}/connection/type/:connectionTypeId`,
    },
};

export default integrationUrls;
