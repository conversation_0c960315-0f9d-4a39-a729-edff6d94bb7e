/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import fetch, { generatePath, Method } from '../services/fetch';
import chunk from 'lodash/chunk';
import get from 'lodash/get';
import flatten from 'lodash/flatten';
import { commonMessages, notifySuc<PERSON>, notifyError } from '@tripudiotech/admin-styleguide';
import { DEFAULT_BATCH_CHUNK_SIZE } from '../tripudiotech-api';

const createBatchReqData = (
    method = Method.POST,
    url: string,
    data: Array<{
        subParams: any;
        body: any;
        subRequests: any[];
        url: string;
        method: any;
    }>
) => {
    const reqData = data.map((item) => {
        const path = generatePath(item.url ?? url, item.subParams);
        return {
            method: item.method ?? method,
            url: path,
            body: item.body || {},
            subRequests:
                item.subRequests &&
                item.subRequests.map((subRequest) => ({
                    url: generatePath(subRequest.url ?? url, subRequest.subParams),
                    method: subRequest.method ?? method,
                    body: subRequest.body,
                })),
        };
    });
    return reqData;
};

export const batchRequest = async ({ url, method, data, maxChunkRequest = DEFAULT_BATCH_CHUNK_SIZE }) => {
    try {
        let chunks = [data];

        if (data.length > maxChunkRequest) {
            chunks = chunk(data, maxChunkRequest);
        }

        const res = await Promise.all(
            chunks.map((chunk) =>
                fetch({
                    url,
                    params: {},
                    method,
                    qs: {},
                    data: chunk,
                    successMessage: '',
                    errorMessage: '',
                    shouldShow404: false,
                    signal: null,
                })
            )
        );

        return {
            success: true,
            data: res,
        };
    } catch (error) {
        return {
            success: false,
            error,
        };
    }
};

export const handleBatchRequestRes = ({
    response: { success, data },
    statusCodeOnSuccess = 201,
    onSuccess = () => {},
    onFailed = () => {},
    successMessage = commonMessages.successRequest,
    failedMessage = commonMessages.failedRequest,
}) => {
    if (success && data.every((r: any) => r.status === 200)) {
        // Flatten the response data
        const resDetails = flatten(data.map((r: any) => r.data));
        let successReq = [];
        let failedReq = [];

        resDetails.forEach((d: any) => {
            if (d.status === statusCodeOnSuccess) successReq.push(d);
            else failedReq.push(d);
        });

        const failedReqs = resDetails.filter((d: any) => d.status !== statusCodeOnSuccess);
        if (failedReqs.length === 0) {
            notifySuccess(successMessage);
            onSuccess();
        } else {
            notifyError(get(failedReqs[0], ['response', 'errorMessage'], failedReqs[0]));
            onFailed();
        }
    } else {
        notifyError(failedMessage);
        onFailed();
    }
};

export default createBatchReqData;
