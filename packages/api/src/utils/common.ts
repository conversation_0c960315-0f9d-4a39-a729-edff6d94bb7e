import { AttributeSchema } from 'ui-common';
import split from 'lodash/split';

export const filterAndSortAttributesForEdit = (attributes: AttributeSchema[], attributeOrder: string[]) => {
    let flattenOrder = [];
    attributeOrder?.forEach((order) => {
        if (order.includes(':')) {
            flattenOrder = flattenOrder.concat(split(split(order, ':')[1], ','));
        } else {
            flattenOrder.push(order);
        }
    });
    const filteredAttributes = Object.values(attributes)
        .filter((attr) => attr.visible && !attr.identifier)
        .sort((a, b) => {
            if (!attributeOrder) return a.name.localeCompare(b.name);
            return flattenOrder.indexOf(a.name) - flattenOrder.indexOf(b.name);
        });

    return filteredAttributes;
};

export const filterAndSortAttributesForRead = (attributes: AttributeSchema[], attributeOrder: string[]) => {
    let flattenOrder = [];
    attributeOrder?.forEach((order) => {
        if (order.includes(':')) {
            flattenOrder = flattenOrder.concat(split(split(order, ':')[1], ','));
        } else {
            flattenOrder.push(order);
        }
    });
    const filteredAttributes = Object.values(attributes)
        .filter((attr) => attr.visible)
        .sort((a, b) => {
            if (!attributeOrder) return a.name.localeCompare(b.name);
            return flattenOrder.indexOf(a.name) - flattenOrder.indexOf(b.name);
        });

    return filteredAttributes;
};
