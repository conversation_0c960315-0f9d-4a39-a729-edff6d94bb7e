/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method } from "../services/fetch";
const BASE_URL = `${process.env.BASE_URL}/asset`;

const assetServiceUrl = {
    checkinDocument: {
        method: Method.POST,
        url: `${BASE_URL}/v2/document/:id/check-in`,
    },
    documentCheckout: {
        method: Method.POST,
        url: `${BASE_URL}/v2/document/:id/check-out`,
    },
    getAllDocument: {
        method: Method.GET,
        url: `${BASE_URL}/v2/document/:id/file`,
    },
    deleteDocument: {
        method: Method.DELETE,
        url: `${BASE_URL}/v2/document/:id/file/:fileId`,
    },
    previewFile: {
        method: Method.GET,
        url: `${BASE_URL}/v2/file/:id/preview`,
    },
};

export default assetServiceUrl;

export const buildCheckInForm = ({
    file,
    override = "false",
    revise = "false",
    lock = "false",
    primary = "false",
}) => {
    if (!file) return;
    const formData = new FormData();
    formData.append("file", file, file.name);
    formData.append("override", override);
    formData.append("revise", revise);
    formData.append("lock", lock);
    formData.append("primary", primary);
    return formData;
};

export const buildDownloadDocumentResponseError = (status) => {
    return status === 404
        ? "There's no document uploaded to this entity."
        : "An unexpected error has occurred while loading the document.";
};
