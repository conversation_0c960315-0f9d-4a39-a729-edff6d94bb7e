/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import axiosInstance from "../../services/api_interceptor";
import ClassificationSchemaTree from "../model/classfication-tree.model";
import Classification from "../model/classfication.model";
const BASE_URL = `${process.env.BASE_URL}/schema-manager`;

const classificationService = {
    async getClassificationTree() {
        try {
            const response = await axiosInstance.get<ClassificationSchemaTree>(
                `${BASE_URL}/classification`
            );
            return response;
        } catch (err) {
            console.log(
                "Received an error on getting classification tree",
                err
            );
        }
    },
    async createClassification(name, classification) {
        try {
            const response = await axiosInstance.post(
                `${BASE_URL}/classification/${name}/classification`,
                classification
            );
            return response;
        } catch (err) {
            console.log("Received an error on creating classification", err);
        }
    },
    async getClassification(name: string) {
        try {
            const response = await axiosInstance.get<Classification>(
                `${BASE_URL}/classification/${name}`
            );
            return response;
        } catch (err) {
            console.log("Received an error on getting classification", err);
        }
    },
    async updateClassification(name: string, classification) {
        try {
            const response = await axiosInstance.put(
                `${BASE_URL}/classification/${name}`,
                classification
            );
            return response;
        } catch (err) {
            console.log("Received an error on updating classification", err);
        }
    },
    async deleteClassification(name: string) {
        try {
            const response = await axiosInstance.delete(
                `${BASE_URL}/classification/${name}`
            );
            return response;
        } catch (err) {
            console.log("Received an error on deleting classification", err);
        }
    },
    async createClassificationAttribute(classificationName: string, attribute) {
        try {
            const response = await axiosInstance.post(
                `${BASE_URL}/classification/${classificationName}/attribute`,
                attribute
            );
            return response;
        } catch (err) {
            console.log(
                "Received an error on creating classification attribute",
                err
            );
        }
    },
    async deleteAttribute(classificationName: string, attributeName: string) {
        try {
            const response = await axiosInstance.delete(
                `${BASE_URL}/classification/${classificationName}/attribute/${attributeName}`
            );
            return response;
        } catch (err) {
            console.log(
                "Received an error on deleting classification attribute",
                err
            );
        }
    },
};
export default classificationService;
