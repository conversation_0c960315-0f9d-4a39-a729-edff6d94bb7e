/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Method } from '../../services/fetch';

const BASE_URL = `${process.env.BASE_URL}/schema-manager`;

const classificationUrls = {
    getClassificationTree: {
        method: Method.GET,
        url: `${BASE_URL}/classification`,
    },
    createClassification: {
        method: Method.POST,
        url: `${BASE_URL}/classification/:name/classification`,
    },
    getClassificationDetail: {
        method: Method.GET,
        url: `${BASE_URL}/classification/:name`,
    },
    updateClassification: {
        method: Method.PUT,
        url: `${BASE_URL}/classification/:name`,
    },
    deleteClassification: {
        method: Method.DELETE,
        url: `${BASE_URL}/classification/:name`,
    },
    createClassificationAttribute: {
        method: Method.POST,
        url: `${BASE_URL}/classification/:classificationName/attribute`,
    },
    updateAttribute: {
        method: Method.PUT,
        url: `${BASE_URL}/classification/:classificationName/attribute/:attributeName`,
    },
    createUpdatableBy: {
        method: Method.POST,
        url: `${BASE_URL}/classification/:classificationName/access`,
    },
    deleteAttribute: {
        method: Method.DELETE,
        url: `${BASE_URL}/classification/:classificationName/attribute/:attributeName`,
    },
    getClassificationUpdatableBy: {
        method: Method.GET,
        url: `${BASE_URL}/classification/:classificationName/access`,
    },
    deleteClassificationUpdatableBy: {
        method: Method.DELETE,
        url: `${BASE_URL}/classification/:classificationName/access`,
    },
} as const;

export default classificationUrls;
