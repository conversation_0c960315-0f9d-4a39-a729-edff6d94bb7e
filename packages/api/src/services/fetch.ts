/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { stringify } from 'query-string';
import get from 'lodash/get';
import { navigateToUrl } from 'single-spa';
import axiosInstance from './api_interceptor';
import { notify<PERSON>uc<PERSON>, notifyError, notifyBusinessRuleErrors } from '@tripudiotech/admin-styleguide';
import axios, { type RawAxiosRequestHeaders, type ResponseType } from 'axios';
import authenticationService from '../authentication/authentication_service';
import { RESPONSE_ERROR_TYPE } from '../tripudiotech-api';
export enum Method {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    DELETE = 'DELETE',
    PATCH = 'PATCH',
}

export const invariant = (cond, message: string) => {
    if (!cond) throw new Error(message);
};

export const generatePath = (path, params): string => {
    return path
        .replace(/:(\w+)/g, (_, key) => {
            invariant(params[key] != null, `Missing ":${key}" param`);
            return params[key]!;
        })
        .replace(/\/*\*$/, (_) => (params['*'] == null ? '' : params['*'].replace(/^\/*/, '/')));
};

interface FetchProps {
    url: string;
    params?: Record<string, any>;
    method: Method;
    qs?: Record<string, any>;
    data?: Record<string, any> | string;
    successMessage?: string | JSX.Element;
    errorMessage?: string | JSX.Element;
    shouldShow404?: boolean;
    shouldShow403?: boolean;
    skipToast?: boolean;
    skipDetails?: boolean;
    headers?: RawAxiosRequestHeaders;
    signal?: AbortSignal;
    responseType?: ResponseType;
    onUploadProgress?: (progressEvent: any) => void;
    ignoreErrorCodes?: number[];
}

const fetch = async ({
    url,
    params,
    method,
    qs,
    data,
    successMessage,
    errorMessage,
    shouldShow404 = false,
    shouldShow403 = false,
    skipToast = false,
    skipDetails = false,
    headers,
    signal,
    responseType = 'json',
    ignoreErrorCodes = [],
}: FetchProps) => {
    try {
        const path = generatePath(url, params);
        const response = await axiosInstance({
            data,
            method: method,
            url: qs ? `${path}?${stringify(qs)}` : path,
            headers,
            signal,
            responseType: responseType as any,
        });

        const needConfirmationOnBusinessRule = response.data?.businessRuleResults?.length > 0;

        if (successMessage && !needConfirmationOnBusinessRule) {
            notifySuccess(successMessage);
        }

        return response;
    } catch (err) {
        const businessRuleErrors = get(err?.response, ['data', 'metadata', 'rule'], []);
        const isInvalidAttributeError = err.response?.data?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE;
        if (err.response?.status === 401) {
            authenticationService.login();
            return;
        }
        if (err.response?.status === 400 && businessRuleErrors.length > 0) {
            notifyBusinessRuleErrors(businessRuleErrors, { skipDetails });
            throw err;
        }
        if (err.response?.status === 400 && isInvalidAttributeError){
            throw err;
        }
        if (err.response?.status === 403 && shouldShow403) {
            navigateToUrl(`${window.location.origin}/access-denied`);
            return;
        }
        if (err.response?.status === 404 && shouldShow404) {
            navigateToUrl(`${window.location.origin}/not-found`);
            return;
        }
        if (axios.isCancel(err)) {
            console.log('Request cancelled');
            return;
        }
        if (errorMessage && !skipToast) {
            notifyError(errorMessage);
        } else if (!skipToast && !ignoreErrorCodes.includes(err.response?.status)) {
            const error = get(err, ['response', 'data', 'errorMessage'], err.message);
            const formattedErr = typeof error === 'string' ? error : JSON.stringify(error);
            notifyError(`Error: ${formattedErr}`);
        }

        throw err;
    }
};

export default fetch;
