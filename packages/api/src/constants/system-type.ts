/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export const CAD_3D_VIEW_ATTRIBUTE = '3dDocumentId';

export const SYSTEM_RELATION = {
    HAS_MASTER: 'HAS_MASTER',
    HAS_ASSEMBLY: 'HAS_ASSEMBLY',
    AFFECTED_ITEM: 'AFFECTED_ITEM',
    RESOLVED_BY: 'RESOLVED_BY',
    REPORTED_ON: 'REPORTED_ON',
    LINKED_ISSUE: 'LINKED_ISSUE',
    ALTERNATES_TO: 'ALTERNATES_TO',
    CHANGING_ITEM: 'CHANGING_ITEM',
    CHANGE_REQUEST: 'CHANGE_REQUEST',
    REFERENCE_TO: 'REFERENCE_TO',
    HAS_COMPONENT: 'HAS_COMPONENT',
    SUBSTITUTES_TO: 'SUBSTITUTES_TO',
    IMPLEMENTS: 'IMPLEMENTS',
    BELONGS_TO: 'BELONGS_TO',
    TASK_OF: 'TASK_OF',
    PROPOSAL_OF: 'PROPOSAL_OF',
    ENGINEERING_REVISION_OF: 'ENGINEERING_REVISION_OF',
    IMPACT_ANALYSIS: 'IMPACT_ANALYSIS',
    WORKS_FOR: 'WORKS_FOR',
    OWNED_BY: 'OWNED_BY',
};

export const SYSTEM_ENTITY_TYPE = {
    SYS_ROOT: 'SysRoot',
    DOCUMENT: 'Document',
    DOCUMENT_MASTER: 'DocumentMaster',
    PART_MASTER: 'PartMaster',
    PART: 'Part',
    CAD_PART: 'CADPart',
    SPECIFICATION: 'Specification',
    ISSUE: 'Issue',
    CHANGE_REQUEST: 'ChangeRequest',
    PLANNED_CHANGE: 'PlannedChange',
    CHANGE_ORDER: 'ChangeOrder',
    CHANGE_ITEM: 'ChangeItem',
    TASK: 'Task',
    PERSON: 'Person',
    BOM: 'BillOfMaterial',
    CAD_BOM: 'CadBOM',
    ENGINEERING_BOM: 'eBOM',
    MANUFACTURING_BOM: 'mBOM',
    IMPACT_ANALYSIS: 'ImpactAnalysis',
    COMPANY: 'Company',
    INTERNAL_COMPANY: 'InternalCompany',
    EXTERNAL_COMPANY: 'ExternalCompany',
    TEAM: 'Team',
    DEPARTMENT: 'Department',
    USER_GROUP: 'UserGroup',
};

export const CHANGE_ACTION = {
    UPDATE_ATTRIBUTE: 'UPDATE_ATTRIBUTE',
};

export enum RELATION_DIRECTION {
    OUTGOING = 'Outgoing',
    INCOMING = 'Incoming',
}

export const SYSTEM_ATTRIBUTE = {
    NAME: 'name',
    DISPLAY_NAME: 'displayName',
    DESCRIPTION: 'description',
};

export const DISTRIBUTION_LIST_RELATION = {
    CHANGE_MANAGEMENT_GROUP: 'CHANGE_MANAGEMENT_GROUP',
    FEEDBACK_PROVIDER: 'FEEDBACK_PROVIDER',
    ANALYSIS_PERFORMER: 'ANALYSIS_PERFORMER',
    DECISION_TEAM: 'DECISION_TEAM',
    AGENT: 'AGENT',
};

export enum SORT_DIRECTION {
    DESCENDING = 'DESCENDING',
    ASCENDING = 'ASCENDING',
}

export const PERMISSION = {
    CAN_CHECK_IN: 'canCheckIn',
    CAN_CHECK_OUT: 'canCheckOut',
    CAN_CONNECT_AS_FROM_SIDE: 'canConnectAsFromSide',
    CAN_CONNECT_AS_TO_SIDE: 'canConnectAsToSide',
    CAN_COPY: 'canCopy',
    CAN_CREATE: 'canCreate',
    CAN_DELETE: 'canDelete',
    CAN_DEMOTE: 'canDemote',
    CAN_DISCONNECT_AS_FROM_SIDE: 'canDisconnectAsFromSide',
    CAN_DISCONNECT_AS_TO_SIDE: 'canDisconnectAsToSide',
    CAN_DOWNLOAD: 'canDownload',
    CAN_GRANT_ACCESS: 'canGrantAccess',
    CAN_LOCK: 'canLock',
    CAN_MODIFY: 'canModify',
    CAN_PROMOTE: 'canPromote',
    CAN_PUBLISH: 'canPublish',
    CAN_READ: 'canRead',
    CAN_REVISE: 'canRevise',
    CAN_REVOKE_ACCESS: 'canRevokeAccess',
    CAN_UNLOCK: 'canUnlock',
    CAN_CLASSIFY: 'canClassify',
};

export const PERMISSION_TYPES = {
    ANY: 'Any',
    VIEWER: 'Viewer',
    CONTRIBUTOR: 'Contributor',
    OWNER: 'Owner',
    ADMIN: 'Admin',
};
