/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import SchemaTree from './model/schema-tree.model';
import EntityType from './model/entity-type.model';
import axiosInstance from '../services/api_interceptor';
import AttributePageInfo from './model/attributes-info.model';
import { Lifecycle } from '../tripudiotech-api';
import { ISchemaDetail } from 'ui-common';

const BASE_URL = `${process.env.BASE_URL}/schema-manager`;

const schemaService = {
    async getSchemaTree() {
        try {
            const response = await axiosInstance.get<SchemaTree>(`${BASE_URL}/schema`);
            return response;
        } catch (err) {
            console.log('Received an Error on getting schema tree', err);
        }
    },
    async getSchema(entityTypeName: string) {
        try {
            const response = await axiosInstance.get<ISchemaDetail>(`${BASE_URL}/schema/${entityTypeName}`);
            return response;
        } catch (err) {
            console.log('Received an Error on getting schema', err);
        }
    },
    async updateEntityType(entityTypeName: string, entityType: EntityType) {
        try {
            const response = await axiosInstance.put<EntityType>(`${BASE_URL}/schema/${entityTypeName}`, entityType);
            return response;
        } catch (err) {
            console.log('Received an Error on update entity type', err);
        }
    },
    async createEntityAttribute(entityTypeName: string, attributes) {
        try {
            const response = await axiosInstance.post<EntityType>(
                `${BASE_URL}/schema/${entityTypeName}/attribute`,
                attributes
            );
            return response;
        } catch (err) {
            console.log('Received an Error on create attribute', err);
        }
    },
    async updateEntityAttribute(entityTypeName: string, attributes) {
        try {
            const response = await axiosInstance.put<EntityType>(
                `${BASE_URL}/attribute/${attributes.name}`,
                attributes
            );
            return response;
        } catch (err) {
            console.log('Received an Error on update attribute', err);
        }
    },
    async deleteEntityAttribute(entityTypeName: string, attributeName: string) {
        try {
            const response = await axiosInstance.delete<EntityType>(
                `${BASE_URL}/schema/${entityTypeName}/attribute/${attributeName}`
            );
            return response;
        } catch (err) {
            console.log('Received an Error on delete attribute', err);
        }
    },
    async createEntitySubType(entityTypeName: string, subType) {
        try {
            const response = await axiosInstance.post<EntityType>(
                `${BASE_URL}/schema/${entityTypeName}/schema`,
                subType
            );
            return response;
        } catch (err) {
            console.log('Received an Error on create sub type', err);
            throw err;
        }
    },
    async getAttributes() {
        try {
            const response = await axiosInstance.get<AttributePageInfo>(
                `${process.env.BASE_URL}/schema-manager/attribute`
            );
            return response;
        } catch (error) {
            console.log('Received an Error on get attributes', error);
        }
    },
    async deleteEntityType(entityTypeName: string) {
        try {
            const response = await axiosInstance.delete(`${BASE_URL}/schema/${entityTypeName}`);
            return response;
        } catch (err) {
            console.log('Received an Error on deleting the Entity Type', err);
        }
    },
    async fetchEntityLifecycle(entityTypeName: string) {
        try {
            const response = await axiosInstance.get<Lifecycle>(`${BASE_URL}/schema/${entityTypeName}/life-cycle`);
            return response;
        } catch (err) {
            console.log('Error on fetch lifecycle for entity ', entityTypeName);
        }
    },
    async updateDefaultLifecycle(entityTypeName: string, lifecycleId: string) {
        try {
            const response = await axiosInstance.post(
                `${BASE_URL}/schema/${entityTypeName}/life-cycle/${lifecycleId}/set-default`
            );
            return response;
        } catch (err) {
            console.log('Error when update attribute', err);
        }
    },
    async getAllLifeCycles(searchTerm?: string, offset?: number, limit?: number) {
        try {
            const parameter = {
                ...(offset ? { offset: offset } : {}),
                ...(limit ? { limit: limit } : {}),
                ...(searchTerm ? { query: searchTerm } : {}),
            };
            const response = await axiosInstance.get<EntityType>(`${BASE_URL}/life-cycle`, {
                params: parameter,
            });
            return response;
        } catch (err) {
            console.log('Received an Error on getting Life-Cycles', err);
        }
    },
    async getLifeCycle(lifecycleId: string) {
        try {
            const response = await axiosInstance.get<EntityType>(`${BASE_URL}/life-cycle/${lifecycleId}`);
            return response;
        } catch (err) {
            console.log('Received an Error on getting Life-Cycle', err);
        }
    },
    async assignLifeCycles(data, entityTypeName) {
        try {
            const response = await axiosInstance.post<EntityType>(
                `${BASE_URL}/schema/${entityTypeName}/life-cycle`,
                data
            );
            return response;
        } catch (err) {
            console.log('Received an Error on assigning Life-Cycle', err);
        }
    },
    async unassignLifeCycles(data, entityTypeName) {
        try {
            const response = await axiosInstance.delete<EntityType>(`${BASE_URL}/schema/${entityTypeName}/life-cycle`, {
                data,
            });
            return response;
        } catch (err) {
            console.log('Receive an Error on unassigning Life-Cycle', err);
        }
    },
    async deleteLifecycle(id: string) {
        try {
            const response = await axiosInstance.delete(`${BASE_URL}/life-cycle/${id}`);
            return response;
        } catch (err) {
            console.log('Receive an Error on delete lifecycle', err);
        }
    },
    async createLifecycle(lifecycle) {
        try {
            const response = await axiosInstance.post(`${BASE_URL}/life-cycle`, lifecycle);
            return response;
        } catch (err) {
            throw err;
        }
    },
    async createRelation(relation) {
        try {
            const response = await axiosInstance.post(`${BASE_URL}/relation`, relation);
            return response;
        } catch (err) {
            console.log('Received an error on create Relation', err);
            throw err;
        }
    },
    async updateLifecycle({ id, ...data }) {
        try {
            const response = await axiosInstance.put(`${BASE_URL}/life-cycle/${id}`, data);
            return response;
        } catch (err) {
            throw err;
        }
    },
    async deleteRelation(fromEntityTypeName, relationName, toEntityTypeName) {
        try {
            const response = await axiosInstance.delete(
                `${BASE_URL}/relation/${fromEntityTypeName}/${relationName}/${toEntityTypeName}`
            );
            return response;
        } catch (err) {
            console.log('Received an error on deleting Relation', err);
            throw err;
        }
    },
};

export default schemaService;
