/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import type SysRoot from '../../entity/model/entity.model';
import type { RELATION_DIRECTION } from '../../tripudiotech-api';
import { AttributeSchema } from 'ui-common';

type RelationSideType = 'FLOAT' | 'REPLICATED' | 'NONE';
type RelationFloatType = 'IMMEDIATE' | 'STATE_BASE';

export interface RelationSide {
    type: RelationSideType;
    floatType: RelationFloatType;
    floatStateCondition: string;
}

export interface RelationTypePermission {
    onCreate: string[];
    onDelete: string[];
}

export interface RelationType extends SysRoot {
    fromEntityType: string;
    toEntityType: string;
    required: boolean;
    visible: boolean;
    onFromSideRevise: RelationSide;
    onFromSideClone: RelationSide;
    onToSideRevise: RelationSide;
    onToSideClone: RelationSide;
    fromEntityPermissions: RelationTypePermission;
    toEntityPermission: RelationTypePermission;
    direction: RELATION_DIRECTION.OUTGOING | RELATION_DIRECTION.INCOMING;
    displayName: string;
    constraint?: {
        searchCriteria?: string;
    };
}
export interface RelationSchema {
    relationType: RelationType;
    attributes?: Record<string, AttributeSchema>;
}
