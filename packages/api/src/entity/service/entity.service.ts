/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import axiosInstance from "../../services/api_interceptor";

const BASE_URL = `${process.env.BASE_URL}/entity`;

const entityTypeService = {
    async getListEntity(entityType: string, offset = 0, limit = 20) {
        try {
            const response = await axiosInstance.get(
                `${BASE_URL}/entity/${entityType}?limit=${limit}&offset=${offset}`
            );
            return response;
        } catch (err) {
            console.log(
                `Received an error on getting entity ${entityType}`,
                err
            );
        }
    },
    async getEntityById(entityType: string, entityId: string) {
        try {
            const response = await axiosInstance.get(
                `${BASE_URL}/entity/${entityType}/${entityId}`
            );
            return response;
        } catch (err) {
            console.log(`Received an error on get entity ${entityType}`, err);
        }
    },
    async createEntity(entityType: string, data) {
        try {
            const response = await axiosInstance.post(
                `${BASE_URL}/entity/${entityType}`,
                data
            );
            return response;
        } catch (err) {
            console.log(
                `Received an error on create entity ${entityType}`,
                err
            );
        }
    },
    async updateEntity(entityId, data) {
        try {
            const response = await axiosInstance.put(
                `${BASE_URL}/entity/${entityId}`,
                data
            );
            return response;
        } catch (err) {
            console.log(
                `Received an error on update entity ${entityId}`,
                err
            );
        }
    },
    async deleteEntity(entityId) {
        try {
            const response = await axiosInstance.delete(
                `${BASE_URL}/entity/${entityId}`
            );
            return response;
        } catch (err) {
            console.log(
                `Received an error on delete entity ${entityId}`,
                err
            );
        }
    },
};

export default entityTypeService;
