/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { MarkerType, Node, Edge, Position, getConnectedEdges } from 'reactflow';
import { v4 as uuidv4 } from 'uuid';
import dagre from 'dagre';
import { LIFECYCLE_VALIDATION_MSG } from '../constants';
import { State } from '../store/lifecycleStateStore';
import isEmpty from 'lodash/isEmpty'

export const EDGE_OPTIONS = {
    animated: false,
    type: 'simpleFloatingEdge',
    style: { strokeWidth: '1px', stroke: '#434343' },
    markerEnd: {
        type: MarkerType.ArrowClosed,
        color: '#434343',
    },
};

export const createNewEdge = (source: string, target: string) => {
    const edgeId = uuidv4();
    return {
        id: edgeId,
        source: source,
        target: target,
        ...EDGE_OPTIONS,
    };
};
export const createNewNode = (id: string) => ({
    id,
    type: 'lifecycleStateNode',
    position: {
        x: 100,
        y: 100,
    },
});

const dagreGraph = new dagre.graphlib.Graph();

dagreGraph.setDefaultEdgeLabel(() => ({}));

export const getLayoutedElements = (nodes, edges, direction = 'LR', nodeWidth = 220, nodeHeight = 100) => {
    const isHorizontal = direction === 'LR';
    dagreGraph.setGraph({
        rankdir: direction,
        nodesep: 100,
        edgesep: 50,
        ranksep: 50,
    });

    nodes.forEach((node) => {
        dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight });
    });

    edges.forEach((edge) => {
        dagreGraph.setEdge(edge.source, edge.target);
    });

    dagre.layout(dagreGraph);

    nodes.forEach((node) => {
        const nodeWithPosition = dagreGraph.node(node.id);
        node.targetPosition = isHorizontal ? 'left' : 'top';
        node.sourcePosition = isHorizontal ? 'right' : 'bottom';

        // We are shifting the dagre node position (anchor=center center) to the top left
        // so it matches the React Flow node anchor point (top left).
        node.position = {
            x: nodeWithPosition.x - nodeWidth / 2,
            y: nodeWithPosition.y - nodeHeight / 2,
        };

        return node;
    });

    return { nodes, edges };
};

const calculateDistance = (x1, y1, x2, y2) => {
    return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
};

const getCenterEdgePosition = (center, intersectionPoint, r) => {
    // top right bottom left
    const { x, y } = center;
    const { x: interX, y: interY } = intersectionPoint;
    const ds = [
        {
            position: Position.Top,
            value: calculateDistance(x, y + r, interX, interY),
        },
        {
            position: Position.Right,
            value: calculateDistance(x + r, y, interX, interY),
        },
        {
            position: Position.Bottom,
            value: calculateDistance(x, y - r, interX, interY),
        },
        {
            position: Position.Left,
            value: calculateDistance(x - r, y, interX, interY),
        },
    ];
    return ds.reduce((res, obj) => (obj.value < res.value ? obj : res)).position;
};

const sgn = (x) => {
    return x < 0 ? -1 : 1;
};

const getCircleNodeIntersection = (intersectionNode, targetNode, offset = 2) => {
    const {
        width: intersectionNodeWidth,
        height: intersectionNodeHeight,
        position: intersectionNodePosition,
    } = intersectionNode;
    const targetPosition = targetNode.position;
    const w = intersectionNodeWidth / 2;
    const h = intersectionNodeHeight / 2;
    const r = Math.min(w, h);

    const x1 = intersectionNodePosition.x + w;
    const y1 = intersectionNodePosition.y + h;

    // We shift the node so that the intersection node will have position (0, 0) => Delta always = 0
    const x2 = targetPosition.x + w - x1;
    const y2 = targetPosition.y + h - y1;
    const dr = Math.sqrt(x2 * x2 + y2 * y2);

    // The equation always returns two results
    const xr1 = (sgn(y2) * x2 * Math.sqrt(r * r * dr * dr)) / (dr * dr) + x1;
    const xr2 = -(sgn(y2) * x2 * Math.sqrt(r * r * dr * dr)) / (dr * dr) + x1;
    const yr1 = (Math.abs(y2) * Math.sqrt(r * r * dr * dr)) / (dr * dr) + y1;
    const yr2 = -(Math.abs(y2) * Math.sqrt(r * r * dr * dr)) / (dr * dr) + y1;

    // Calculate distances between two results and the target node
    const d1 = Math.sqrt(Math.pow(xr1 - x2 - x1, 2) + Math.pow(yr1 - y2 - y1, 2));
    const d2 = Math.sqrt(Math.pow(xr2 - x2 - x1, 2) + Math.pow(yr2 - y2 - y1, 2));

    const intersectionPoint =
        d1 > d2
            ? {
                  x: xr2,
                  y: yr2,
              }
            : {
                  x: xr1,
                  y: yr1,
              };
    const x = intersectionNodePosition.x + w;
    const y = intersectionNodePosition.y + h;
    const position = getCenterEdgePosition({ x: x1, y: y1 }, intersectionPoint, r);
    switch (position) {
        case Position.Bottom:
            return {
                x,
                y: y - h - offset,
            };
        case Position.Top:
            return {
                x,
                y: y + h + offset,
            };
        case Position.Left:
            return {
                x: x - w - offset,
                y,
            };
        case Position.Right:
            return {
                x: x + w + offset,
                y,
            };
        default:
            return { x, y };
    }
};
// returns the position (top,right,bottom or right) passed node compared to the intersection point
const getEdgePosition = (node, intersectionPoint) => {
    const n = { ...node.position, ...node };
    const nx = Math.round(n.x);
    const ny = Math.round(n.y);
    const px = Math.round(intersectionPoint.x);
    const py = Math.round(intersectionPoint.y);

    if (px <= nx + 1) {
        return Position.Left;
    }
    if (px >= nx + n.width - 1) {
        return Position.Right;
    }
    if (py <= ny + 1) {
        return Position.Top;
    }
    if (py >= n.y + n.height - 1) {
        return Position.Bottom;
    }

    return Position.Top;
};

export const getEdgeParams = (source, target) => {
    const sourceIntersectionPoint = getCircleNodeIntersection(source, target);
    const targetIntersectionPoint = getCircleNodeIntersection(target, source);

    const sourcePos = getEdgePosition(source, sourceIntersectionPoint);
    const targetPos = getEdgePosition(target, targetIntersectionPoint);

    return {
        sx: sourceIntersectionPoint.x,
        sy: sourceIntersectionPoint.y,
        tx: targetIntersectionPoint.x,
        ty: targetIntersectionPoint.y,
        sourcePos,
        targetPos,
    };
};

const noCreatePermissison = (nodes: Node[], states: Record<string, State>) => {
    return !nodes.some(
        (node) =>
            states[node.id] && states[node.id].canCreate.length > 0 && states[node.id].canConnectAsFromSide.length > 0
    );
};

const lifeCycleStatesHaveDuplicatedName = (nodes: Node[], states: Record<string, State>) => {
    return nodes.some((item, index) =>
        nodes.some((node, idx) => states[node.id].name === states[item.id].name && index !== idx)
    );
};

const isMissingReviseType = (nodes: Node[], states: Record<string, State>): boolean => {
    return nodes.some((node) => states[node.id].reviseType === null);
};

export const validateLifecycleStates = (nodes: Node[], edges: Edge[], states: Record<string, State>, isRevisable) => {
    let errors = [];

    if (noCreatePermissison(nodes, states)) {
        errors.push(LIFECYCLE_VALIDATION_MSG.NO_CREATE_PERMISSION);
    }

    if (isRevisable && isMissingReviseType(nodes, states)) {
        errors.push(LIFECYCLE_VALIDATION_MSG.MISSING_REVISE_TYPE);
    }

    if (lifeCycleStatesHaveDuplicatedName(nodes, states)) {
        errors.push(LIFECYCLE_VALIDATION_MSG.DUPLICATED_NAME);
    }

    let hasOrphanedNode = false;

    nodes.map((node) => {
        const connectedEdges = getConnectedEdges([node], edges);
        if (connectedEdges.length === 0 && nodes.length !== 1) {
            hasOrphanedNode = true;
        }
    });
    if (hasOrphanedNode) {
        errors.push(LIFECYCLE_VALIDATION_MSG.ORPHAN_STATE);
    }
    return errors;
};

export const formatRevisionPattern = (values) => {
    return ['major', 'minor', 'patch', 'separator']
        .filter((key) => !isEmpty(values[key]))
        .reduce((obj, key) => {
            return {
                ...obj,
                [key]: values[key],
            };
        }, {});
};



export const parseStateToNode = ({ states }) => {
    let edges = [];
    const nodes = Object.values(states).map((state: any) => {
        if (state.nextStates) {
            state.nextStates.map((stateName) => {
                if (states[stateName]) {
                    const target = states[stateName].id;
                    edges.push({
                        id: `${state.id}|${target}`,
                        source: state.id,
                        target,
                        ...EDGE_OPTIONS,
                    });
                }
            });
        }

        return createNewNode(state.id);
    });
    return { edges, nodes };
};
