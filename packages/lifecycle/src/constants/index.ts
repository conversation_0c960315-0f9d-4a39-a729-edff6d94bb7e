/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { PERMISSION } from 'ui-common';

export const COMPONENT_NAME = {
    CREATE_LIFECYCLE: 'createLifecycle',
    DETAIL_LIFECYCLE: 'detailLifecycle',
    CONNECT_HIERARCHY_LIFECYCLE: 'connectHierarchyLifecycle',
};
export const SEPARATORS = ['DOT', 'HYPHEN', 'UNDER_SCORE'];
export const SEPARATOR_LABEL = {
    DOT: 'Dot',
    HYPHEN: 'Hyphen',
    UNDER_SCORE: 'Underscore',
};

export const ZOOM_OPTIONS = {
    duration: 500,
    padding: 0.2,
};

export enum REVISE_TYPES {
    MAJOR = 'MAJOR',
    MINOR = 'MINOR',
    PATCH = 'PATCH',
}

export const REVISE_TYPES_OPTIONS = [
    {
        id: REVISE_TYPES.MAJOR,
        value: REVISE_TYPES.MAJOR,
        label: 'Major',
    },
    {
        id: REVISE_TYPES.MINOR,
        value: REVISE_TYPES.MINOR,
        label: 'Minor',
    },
    {
        id: REVISE_TYPES.PATCH,
        value: REVISE_TYPES.PATCH,
        label: 'Patch',
    },
];

export enum STATE_TYPES {
    OFFICIAL = 'Official',
    OBSOLETED = 'Obsoleted',
    SUPERSEDED = 'Superseded',
}

export const STATE_TYPES_OPTIONS = [
    {
        id: STATE_TYPES.OFFICIAL,
        value: STATE_TYPES.OFFICIAL,
        label: STATE_TYPES.OFFICIAL,
    },
    {
        id: STATE_TYPES.OBSOLETED,
        value: STATE_TYPES.OBSOLETED,
        label: STATE_TYPES.OBSOLETED,
    },
    {
        id: STATE_TYPES.SUPERSEDED,
        value: STATE_TYPES.SUPERSEDED,
        label: STATE_TYPES.SUPERSEDED,
    },
];

export const ACCESS_OPTIONS = [
    { id: 'Any', label: 'Any' },
    { id: 'Owner', label: 'Owner' },
    { id: 'Viewer', label: 'Viewer' },
    { id: 'Contributor', label: 'Contributor' },
    { id: 'Admin', label: 'Admin' },
    { id: 'Change Management Group', label: 'Change Management Group' },
    { id: 'Feedback Provider', label: 'Feedback Provider' },
    { id: 'Analysis Performer', label: 'Analysis Performer' },
    { id: 'Decision Team', label: 'Decision Team' },
    { id: 'Agent', label: 'Agent' },
];

export const ACCESS_OPTIONS_MAP = new Map<string, any>([
    ['Any', { id: 'Any', label: 'Any' }],
    ['Owner', { id: 'Owner', label: 'Owner' }],
    ['Viewer', { id: 'Viewer', label: 'Viewer' }],
    ['Contributor', { id: 'Contributor', label: 'Contributor' }],
    ['Admin', { id: 'Admin', label: 'Admin' }],
    ['Change Management Group', { id: 'Change Management Group', label: 'Change Management Group' }],
    ['Feedback Provider', { id: 'Feedback Provider', label: 'Feedback Provider' }],
    ['Analysis Performer', { id: 'Analysis Performer', label: 'Analysis Performer' }],
    ['Decision Team', { id: 'Decision Team', label: 'Decision Team' }],
    ['Agent', { id: 'Agent', label: 'Agent' }],
]);

export const PERMISSION_FIELDS = [
    { key: PERMISSION.CAN_CHECK_IN, label: 'Check In' },
    { key: PERMISSION.CAN_CHECK_OUT, label: 'Check Out' },
    { key: PERMISSION.CAN_CONNECT_AS_FROM_SIDE, label: 'Connect As From Side' },
    { key: PERMISSION.CAN_CONNECT_AS_TO_SIDE, label: 'Connect As To Side' },
    { key: PERMISSION.CAN_COPY, label: 'Copy' },
    { key: PERMISSION.CAN_CREATE, label: 'Create', hide: true },
    { key: PERMISSION.CAN_DELETE, label: 'Delete' },
    { key: PERMISSION.CAN_DEMOTE, label: 'Demote' },
    { key: PERMISSION.CAN_DISCONNECT_AS_FROM_SIDE, label: 'Disconnect As From Side' },
    { key: PERMISSION.CAN_DISCONNECT_AS_TO_SIDE, label: 'Disconnect As To Side' },
    { key: PERMISSION.CAN_DOWNLOAD, label: 'Download' },
    { key: PERMISSION.CAN_GRANT_ACCESS, label: 'Grant Access' },
    { key: PERMISSION.CAN_LOCK, label: 'Lock' },
    { key: PERMISSION.CAN_MODIFY, label: 'Modify' },
    { key: PERMISSION.CAN_PROMOTE, label: 'Promote' },
    { key: PERMISSION.CAN_PUBLISH, label: 'Publish' },
    { key: PERMISSION.CAN_READ, label: 'Read' },
    { key: PERMISSION.CAN_REVISE, label: 'Revise' },
    { key: PERMISSION.CAN_REVOKE_ACCESS, label: 'Revoke Access' },
    { key: PERMISSION.CAN_UNLOCK, label: 'Unlock' },
    { key: PERMISSION.CAN_CLASSIFY, label: 'Classify' },
];

export const PERMISSION_KEYS = PERMISSION_FIELDS.map((field) => field.key);

export const LIFECYCLE_VALIDATION_MSG = {
    NO_CREATE_PERMISSION:
        'LifeCycle must be defined with at least one state allow Create and Connect As From Side permission.',
    DUPLICATED_NAME: 'Lifecycle state name must be unique',
    ORPHAN_STATE: 'All Lifecycle States should be connected.',
    MISSING_REVISE_TYPE: 'Revise type is required on each state if Revision Pattern is used',
};

export const CONTEXT_RULE_FIELDS = [
    { key: 'VIEWER', label: 'Viewer' },
    { key: 'OWNER', label: 'Owner' },
    {
        key: 'CONTRIBUTOR',
        label: 'Contributor',
    },
];
