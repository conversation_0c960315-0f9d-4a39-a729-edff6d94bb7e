/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { EDGE_OPTIONS, getLayoutedElements } from '../utils/graphUtils';
import {
    addEdge,
    applyEdgeChanges,
    applyNodeChanges,
    Connection,
    EdgeChange,
    NodeChange,
    OnSelectionChangeParams,
    useReactFlow,
} from 'reactflow';
import { fetch, schemaUrls } from '@tripudiotech/admin-api';
import keyBy from 'lodash/keyBy';
import { ZOOM_OPTIONS } from '../constants';

const createNewNode = (id: string) => ({
    id,
    type: 'LifecycleHierarchyNode',
    position: {
        x: 100,
        y: 100,
    },
});
const getInitialStoreState = () => {
    return {
        lifecycles: {},
        selectedLifecycleId: null,
        nodes: [],
        edges: [],
        isReadOnly: false,
        lifecycleId: '',
    };
};

type IHierarchyLifecycleStateStore = {
    isReadOnly: boolean;
    lifecycleId: string;
    lifecycles: Record<string, any>;
    selectedLifecycleId: string;
    nodes: any[];
    edges: any[];
    setIsReadOnly: (value: boolean) => void;
    getLifecycleHierarchy: (id: string, isReadOnly?: boolean) => Promise<any>;
    refresh: (fitView?: (option?: any) => void) => Promise<any>;
    addNode: (ids: string[], fitView?: (option?: any) => void) => Promise<any>;
    removeEdge: (edge: string, fitView?: (option?: any) => void) => Promise<any>;
    onConnect: (connection: Connection) => void;
    onSelectionChange: (params: OnSelectionChangeParams) => void;
    onNodesChange: (changes: NodeChange[]) => void;
    onEdgesChange: (changes: EdgeChange[]) => void;
};

export const useHierarchyLifecycleStateStore = create<IHierarchyLifecycleStateStore>((set, get) => ({
    ...getInitialStoreState(),
    setIsReadOnly: (isReadOnly) => {
        set({ isReadOnly });
    },
    getLifecycleHierarchy: async (id: string, isReadOnly = false) => {
        try {
            const { data } = await fetch({
                ...schemaUrls.getLifeCycleHierarchy,
                params: { lifecycleId: id },
            });

            if (data) {
                const lifecycles = keyBy(data, 'id');
                const { nodes, edges } = parseLifecyclestoNode(data);

                set({
                    isReadOnly,
                    lifecycles,
                    ...getLayoutedElements(nodes, edges),
                    lifecycleId: id,
                });
            }
        } catch (err) {}
    },
    refresh: async (fitView = null) => {
        await get().getLifecycleHierarchy(get().lifecycleId, get().isReadOnly);
        if (fitView) {
            setTimeout(() => fitView(ZOOM_OPTIONS), 120);
        }
    },
    addNode: async (ids: string[], fitView?: (option?: any) => void) => {
        try {
            const { selectedLifecycleId, lifecycles } = get();
            await Promise.all(
                ids.map((id) => {
                    return fetch({
                        ...schemaUrls.setNextLifeCycleHierarchy,
                        params: {
                            lifecycleId: selectedLifecycleId,
                            nextLifecycleId: id,
                        },
                        successMessage: `Successfully connected from lifecycle ${lifecycles[selectedLifecycleId]?.name} to ${lifecycles[id]?.name}`,
                    });
                })
            );
            await get().refresh(fitView);
        } catch (error) {
            console.error(error);
        }
    },
    onNodesChange: (changes: NodeChange[]) => {
        set({
            nodes: applyNodeChanges(changes, get().nodes),
        });
    },
    onEdgesChange: (changes: EdgeChange[]) => {
        set({
            edges: applyEdgeChanges(changes, get().edges),
        });
    },
    onConnect: async (connection: Connection) => {
        if (get().isReadOnly) return;
        const edgeId = [connection.source, connection.target].join('|');
        if (
            get()
                .edges.map((edge) => edge.id)
                .includes(edgeId)
        ) {
            return;
        }
        try {
            const fromLc = get().lifecycles[connection.source];
            const toLC = get().lifecycles[connection.target];

            await fetch({
                ...schemaUrls.setNextLifeCycleHierarchy,
                params: {
                    lifecycleId: connection.source,
                    nextLifecycleId: connection.target,
                },
                successMessage: `Successfully connected from lifecycle ${fromLc.name} to ${toLC.name}`,
            });
            await get().refresh();

            return true;
        } catch {
            return false;
        }
    },
    onSelectionChange: (params: OnSelectionChangeParams) => {
        set({
            selectedLifecycleId: params.nodes.length > 0 ? params.nodes[0].id : null,
        });
    },

    removeEdge: async (edgeId: string, fitView?: (option?: any) => void) => {
        const edgeIds = edgeId.split('|');
        try {
            await fetch({
                ...schemaUrls.deleteNextLifeCycleHierarchy,
                params: { lifecycleId: edgeIds[0], nextLifecycleId: edgeIds[1] },
            });
            await get().refresh(fitView);
        } catch {}
    },
}));

const parseLifecyclestoNode = (lifecycles) => {
    let edges = [];
    const nodes = Object.values(lifecycles).map((lifecycle: any) => {
        if (lifecycle.nextLifeCycles) {
            lifecycle.nextLifeCycles.map((targetLcId) => {
                edges.push({
                    id: `${lifecycle.id}|${targetLcId}`,
                    source: lifecycle.id,
                    target: targetLcId,
                    ...EDGE_OPTIONS,
                    type: 'HierarchyFloatingEdge',
                });
            });
        }

        return createNewNode(lifecycle.id);
    });
    return { edges, nodes };
};
