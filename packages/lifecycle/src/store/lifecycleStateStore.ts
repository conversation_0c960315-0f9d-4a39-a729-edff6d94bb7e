/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { IState, ILifeCycleDetail } from 'ui-common';
import { v4 as uuidv4 } from 'uuid';
import {
    createNewEdge,
    createNewNode,
    EDGE_OPTIONS,
    getLayoutedElements,
    parseStateToNode,
    validateLifecycleStates,
} from '../utils/graphUtils';
import {
    addEdge,
    applyEdgeChanges,
    applyNodeChanges,
    Connection,
    EdgeChange,
    getOutgoers,
    NodeChange,
    OnSelectionChangeParams,
} from 'reactflow';
import { ACCESS_OPTIONS_MAP, PERMISSION_FIELDS, PERMISSION_KEYS, STATE_TYPES, ZOOM_OPTIONS } from '../constants';
import pick from 'lodash/pick';
import { autoContextUrls, fetch, entityUrls, schemaUrls } from '@tripudiotech/admin-api';
import pickBy from 'lodash/pickBy';
import keyBy from 'lodash/keyBy';
import keys from 'lodash/keys';
import { notifySuccess, notifyError } from '@tripudiotech/admin-styleguide';
import { useAgent, usePermissionRoles } from '@tripudiotech/admin-caching-store';
export interface State extends IState {
    shouldCreate: boolean;
    contextRule?: any;
}

type LifeCycleStateStore = {
    loadingContextRules: boolean;
    isReadOnly: boolean;
    nodes: any[];
    edges: any[];
    states: Record<string, any>;
    selectedStateId: string;
    revisable: boolean;
    lifecycleDetail: ILifeCycleDetail;
    setIsReadOnly: (value: boolean) => void;
    getLifecycle: (id: string, isReadOnly?: boolean) => Promise<any>;
    resetStore: () => void;
    setRevisable: (enabled: boolean) => void;
    addNode: (fromNodeId: string, fitView?: (option?: any) => void) => void;
    removeNode: (stateId: string, fitView?: (option?: any) => void) => void;
    onNodesChange: (changes: NodeChange[]) => void;
    onEdgesChange: (changes: EdgeChange[]) => void;
    onConnect: (connection: Connection) => void;
    onSelectionChange: (params: OnSelectionChangeParams) => void;
    updateStateInfo: (stateId: string, fieldKey: string, value: any) => void;
    updateStateType: (stateId: string, value: string) => void;
    validate: () => string[];
    formatLifecycleStates: () => any;
    getContextRules: () => Promise<any>;
    updateStateContextRule: (permissionKey, value: any) => void;
    clearStore: () => void;
};

const getDefaultNewState = () => ({
    id: uuidv4() as string,
    name: 'Start State',
    description: '',
    autoContextRule: null,
    isObsoleted: false,
    isOfficial: false,
    isSuperseded: false,
    reviseType: null,
    nextStates: [],
    canCheckIn: [],
    canCheckOut: [],
    canConnectAsFromSide: [],
    canConnectAsToSide: [],
    canCopy: [],
    canCreate: [],
    canDelete: [],
    canDemote: [],
    canDisconnectAsFromSide: [],
    canDisconnectAsToSide: [],
    canDownload: [],
    canGrantAccess: [],
    canLock: [],
    canModify: [],
    canPromote: [],
    canPublish: [],
    canRead: [],
    canRevise: [],
    canRevokeAccess: [],
    canUnlock: [],
    canClassify: [],
    shouldCreate: true,
});

const getInitialStoreState = () => {
    const defaultNode = getDefaultNewState();
    return {
        lifecycleDetail: null,
        states: { [defaultNode.id]: defaultNode },
        selectedStateId: null,
        nodes: [createNewNode(defaultNode.id)],
        edges: [],
        revisable: false,
        isReadOnly: false,
        loadingContextRules: false,
    };
};

const defaultContextPermissions: any = {
    Owner: {
        includes: [],
    },
    Viewer: {
        includes: [],
    },
    Contributor: {
        includes: [],
    },
    isModified: false,
    shouldCreate: true,
};

const parseLifecycleToStore = (lifecycledetail: ILifeCycleDetail, set) => {
    const { nodes, edges } = parseStateToNode(lifecycledetail);
    const revisionPattern = lifecycledetail?.lifeCycle?.revisionPattern;
    let states = {};
    Object.values(lifecycledetail.states).forEach((state: any) => {
        PERMISSION_FIELDS.forEach(({ key }) => {
            if (state[key] && state[key].length > 0) {
                state[key] = state[key].map((value) => ACCESS_OPTIONS_MAP.get(value));
                return;
            }
            state[key] = [];
        });
        states[state.id] = { ...state };
    });
    set({
        ...getLayoutedElements(nodes, edges),
        lifecycleDetail: lifecycledetail,
        states: states,
        revisable: Boolean(revisionPattern),
    });
};

export const useLifecycleStateStore = create<LifeCycleStateStore>((set, get) => ({
    ...getInitialStoreState(),
    setIsReadOnly: (isReadOnly) => {
        set({ isReadOnly });
    },
    resetStore: () => {
        if (!get().lifecycleDetail) get().clearStore();
        const lifecycleDetail = get().lifecycleDetail;
        parseLifecycleToStore(lifecycleDetail, set);
    },
    getLifecycle: async (id: string, isReadOnly = false) => {
        try {
            const { data } = await fetch({
                ...schemaUrls.getLifeCycle,
                params: { lifecycleId: id },
            });

            if (data) {
                parseLifecycleToStore(data, set);
                set({ isReadOnly });
            }
        } catch (error) {
            console.error(error);
        }
    },
    addNode: (fromNodeId: string, fitView?: (option?: any) => void) => {
        const newState = getDefaultNewState();
        const newStateId = newState.id;
        const newNode = createNewNode(newStateId);
        const newEdge = createNewEdge(fromNodeId, newStateId);

        const nodes = [...get().nodes, newNode];
        const edges = [...get().edges, newEdge];
        set({
            states: {
                ...get().states,
                [newStateId]: newState,
            },
            ...getLayoutedElements(nodes, edges),
        });
        setTimeout(() => fitView(ZOOM_OPTIONS), 120);
    },
    removeNode: (stateId: string, fitView?: (option?: any) => void) => {
        const nodes = [...get().nodes].filter((node) => node.id !== stateId);
        const edges = [...get().edges].filter((edge) => edge.source !== stateId && edge.target !== stateId);
        if (nodes.length === 0) {
            return;
        }
        const newStates = {
            ...get().states,
        };
        delete newStates[stateId];
        set({
            states: newStates,
            ...getLayoutedElements(nodes, edges),
        });
        setTimeout(() => fitView(ZOOM_OPTIONS), 120);
    },
    onNodesChange: (changes: NodeChange[]) => {
        set({
            nodes: applyNodeChanges(changes, get().nodes),
        });
    },
    onEdgesChange: (changes: EdgeChange[]) => {
        set({
            edges: applyEdgeChanges(changes, get().edges),
        });
    },
    onConnect: (connection: Connection) => {
        if (get().isReadOnly) return;
        set({
            edges: addEdge(
                {
                    ...connection,
                    type: 'simpleFloatingEdge',
                    style: { strokeWidth: '3px' },
                    ...EDGE_OPTIONS,
                },
                get().edges
            ),
        });
    },
    onSelectionChange: (params: OnSelectionChangeParams) => {
        set({
            selectedStateId: params.nodes.length > 0 ? params.nodes[0].id : get().selectedStateId,
        });
    },
    updateStateInfo: (stateId, fieldKey, value) => {
        set({
            states: {
                ...get().states,
                [stateId]: { ...get().states[stateId], [fieldKey]: value },
            },
        });
    },
    setRevisable: (revisable) => {
        set({ revisable });
    },
    updateStateType: (stateId: string, value: string) => {
        set({
            states: {
                ...get().states,
                [stateId]: {
                    ...get().states[stateId],
                    isObsoleted: value === STATE_TYPES.OBSOLETED,
                    isOfficial: value === STATE_TYPES.OFFICIAL,
                    isSuperseded: value === STATE_TYPES.SUPERSEDED,
                },
            },
        });
    },
    validate: () => {
        const { nodes, edges, revisable, states } = get();
        return validateLifecycleStates(nodes, edges, states, revisable);
    },
    formatLifecycleStates: () => {
        const { nodes, edges, states, revisable } = get();
        let contextRules = [];
        const data = nodes.map((node) => {
            const state = states[node.id];
            const outgoers = getOutgoers(node, nodes, edges);
            const stateData = pick(state, ['name', 'description', 'isOfficial', 'isObsoleted', 'isSuperseded']);
            let permissions = {};
            PERMISSION_KEYS.forEach(
                (key) =>
                    (permissions[key] = state[key].map((option) => {
                        return option.id;
                    }))
            );
            let rulePermission = pickBy(state.contextRule, (val, key) => !['shouldCreate', 'isModified'].includes(key));
            keys(rulePermission).forEach((key) => {
                if (rulePermission[key].includes?.length > 0) {
                    rulePermission[key] = {
                        includes: rulePermission[key].includes?.map((obj) => obj.value),
                    };
                } else {
                    delete rulePermission[key];
                }
            });
            if (state?.contextRule?.isModified) {
                contextRules.push({
                    permission: rulePermission,
                    shouldCreate: state.contextRule.shouldCreate,
                    stateName: state.name,
                });
            }
            return {
                ...stateData,
                ...permissions,
                reviseType: revisable ? state.reviseType : null,
                nextStates: outgoers.map((outgoer) => states[outgoer.id].name),
            };
        });

        return [
            data.reduce(
                (prev, curr) => ({
                    ...prev,
                    [curr.name]: {
                        ...curr,
                    },
                }),
                {}
            ),
            contextRules,
        ];
    },

    getContextRules: async () => {
        const stateId = get().selectedStateId;
        if (!stateId) {
            return;
        }
        if (get().states[stateId]?.contextRule) {
            return;
        }
        let data = defaultContextPermissions;
        let shouldCreate = true;
        set({ loadingContextRules: true });
        const [agents, [permissionRoles]] = await Promise.all([
            useAgent.getState().getAgents(),
            usePermissionRoles.getState().getPermissionRoles(),
        ]);
        // If the state is already created, we check to see if it has context rules, otherwise there no need to check
        if (!get().states[stateId]?.shouldCreate) {
            try {
                const { data: res } = await fetch({
                    ...autoContextUrls.getContext,
                    params: { id: stateId },
                    ignoreErrorCodes: [404],
                });
                data = res.permission;
                shouldCreate = false;
            } catch (err) {}
        }

        let rules = {};

        let agentMap = keyBy(agents, 'id');
        permissionRoles.forEach((role) => {
            if (data[role.name]) {
                rules[role.name] = {
                    includes: data[role.name]?.includes
                        ?.map((id) =>
                            agentMap[id] ? { label: agentMap[id].properties.name, value: agentMap[id].id } : undefined
                        )
                        .filter((item) => item !== undefined),
                };
            }
        });

        set({
            states: {
                ...get().states,
                [stateId]: {
                    ...get().states[stateId],
                    contextRule: {
                        ...rules,
                        shouldCreate,
                        isModified: false,
                    },
                },
            },
            loadingContextRules: false,
        });
    },

    updateStateContextRule: (permissionKey, value: any) => {
        const { selectedStateId } = get();
        set({
            states: {
                ...get().states,
                [selectedStateId]: {
                    ...get().states[selectedStateId],
                    contextRule: {
                        ...get().states[selectedStateId]?.contextRule,
                        [permissionKey]: {
                            includes: value,
                        },
                        isModified: true,
                    },
                },
            },
        });
    },

    clearStore: () => {
        set({ ...getInitialStoreState() });
    },
}));

export const fetchAgent = async (query) => {
    try {
        const { data } = await fetch({
            ...entityUrls.getListEntity,
            params: { entityType: 'agent' },
            qs: {
                query,
            },
            skipToast: true,
        });
        return data.data.map((item) => ({
            label: item.properties.name,
            value: item.id,
        }));
    } catch (err) {
        console.error(err);
        return [];
    }
};

export const createContextRule = async (state, permission) => {
    const { id } = state;
    await fetch({
        ...autoContextUrls.createContext,
        params: { id },
        data: { permission },
        skipToast: true,
    });
};

export const updateContextRule = async (state, permission) => {
    const { id } = state;
    await fetch({
        ...autoContextUrls.updateContext,
        params: { id },
        data: { permission },
        skipToast: true,
    });
};

export const createLifecycle = async (request, contextRules) => {
    try {
        const lifecycleResult = await fetch({
            ...schemaUrls.createLifecycle,
            data: request,
        });

        const { states } = lifecycleResult.data;

        await Promise.all(
            contextRules.map(async (contextRule) => {
                const state = states[contextRule.stateName];
                const shouldCreate = Boolean(contextRule.shouldCreate);
                if (shouldCreate) {
                    createContextRule(state, contextRule.permission);
                } else {
                    updateContextRule(state, contextRule.permission);
                }
            })
        );
        notifySuccess(`Successfully created lifecycle <b>${request.name}</b>`);
        return true;
    } catch (err) {
        console.error(err);
        notifyError(`An error occurred while creating the lifecycle. Please try again later`);
        return false;
    }
};

export const updateLifecycle = async ({ id, ...data }, contextRules) => {
    try {
        const lifeCycleResult = await fetch({
            ...schemaUrls.updateLifecycle,
            params: { lifeCycleId: id },
            data: data,
        });

        const { states } = lifeCycleResult.data;

        await Promise.all(
            contextRules.map(async (contextRule) => {
                const state = states[contextRule.stateName];

                const shouldCreate = Boolean(contextRule.shouldCreate);
                if (shouldCreate) {
                    createContextRule(state, contextRule.permission);
                } else {
                    updateContextRule(state, contextRule.permission);
                }
            })
        );
        notifySuccess(`Successfully updated lifecycle <b>${data.name}</b>`);
        return true;
    } catch (err) {
        console.error(err);
        notifyError(`An error occurred while updating the lifecycle. Please try again later`);
        return false;
    }
};
