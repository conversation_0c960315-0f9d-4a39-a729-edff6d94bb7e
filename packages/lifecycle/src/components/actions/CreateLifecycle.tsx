/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useRef, useState } from 'react';
import { styled, Button, RightTray, Snackbar, Alert, Typography, LoadingOverlay } from 'ui-style';
import { useToggle } from 'usehooks-ts';
import { COMPONENT_NAME } from '../../constants';
import CollapsablePanel from '../CollapsablePanel';
import LifecycleForm from '../LifecycleForm';
import LifecycleStateForm from '../LifecycleStateForm';
import 'reactflow/dist/style.css';
import LifecycleStateDiagram from '../LifecycleStateDiagram';
import { FormikProps } from 'formik';
import { formatRevisionPattern } from '../../utils/graphUtils';
import { createLifecycle, useLifecycleStateStore } from '../../store/lifecycleStateStore';
import pick from 'lodash/pick';

const Container = styled('div')(({ theme }) => ({
    width: '100%',
    flexGrow: 1,
    display: 'flex',
    overflow: 'hidden',
    backgroundColor: theme.palette.glide.background.normal.inversePrimary,
    '& .diagramContainer': {
        position: 'relative',
        flexGrow: 1,
    },
    '& .alertContainer': {
        width: '100%',
        maxWidth: '420px',
    },
    '& .errorTitle': {
        fontSize: '0.875rem',
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 600,
    },
    '& .errorDescription': {
        fontSize: '0.875rem',
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 400,
    },
    '& .errorList': {
        paddingInlineStart: '24px',
    },
}));

const CreateLifecycleAction = ({ onRefresh }) => {
    const [open, toggle, setValue] = useToggle();
    const formRef = useRef<FormikProps<any>>(null);
    const [revisable, validateStates, formatLifecycleStates, clearStore] = useLifecycleStateStore((state) => [
        state.revisable,
        state.validate,
        state.formatLifecycleStates,
        state.clearStore,
    ]);
    const [errors, setErrors] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const onSubmit = useCallback(() => {
        formRef.current?.submitForm();
    }, [formRef]);

    const handleClose = useCallback(() => {
        setValue(false);
        clearStore();
    }, [setValue]);

    const handleSubmit = useCallback(
        async (values) => {
            const errors = validateStates();
            setErrors(errors);

            if (errors.length === 0) {
                const [states, contextRules] = formatLifecycleStates();
                const request = {
                    ...pick(values, ['name', 'description']),
                    states,
                    revisionPattern: revisable ? formatRevisionPattern(values) : null,
                };
                setIsLoading(true);
                const res = await createLifecycle(request, contextRules);
                onRefresh();
                setIsLoading(false);
                if (res) {
                    handleClose();
                }
            }
        },
        [validateStates, formatLifecycleStates, revisable, onRefresh, handleClose]
    );

    const handleCloseValidation = useCallback(() => setErrors([]), [setErrors]);

    return (
        <>
            {isLoading && <LoadingOverlay />}
            <Button onClick={toggle} variant="contained" color="secondary" size="small">
                Create Lifecycle
            </Button>
            <RightTray
                title={`Create New Lifecycle`}
                componentName={COMPONENT_NAME.CREATE_LIFECYCLE}
                open={open}
                onClose={handleClose}
                onConfirm={onSubmit}
                confirmText="Save Changes"
                disableCloseOnClickOutside
                fullWidth
            >
                <Container>
                    <CollapsablePanel>
                        <LifecycleForm formRef={formRef} handleSubmit={handleSubmit} />
                        <LifecycleStateForm />
                    </CollapsablePanel>
                    <div className="diagramContainer">
                        <LifecycleStateDiagram />
                        <Snackbar
                            open={errors.length > 0}
                            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                            autoHideDuration={6000}
                            onClose={handleCloseValidation}
                        >
                            <Alert
                                onClose={handleCloseValidation}
                                severity="error"
                                sx={{ width: '100%', maxWidth: '420px' }}
                            >
                                <Typography className="errorTitle">Some states are not valid</Typography>
                                <ul className="errorList">
                                    {errors.map((error) => (
                                        <li className="errorItem" key={error}>
                                            <Typography className="errorDescription">{error}</Typography>
                                        </li>
                                    ))}
                                </ul>
                            </Alert>
                        </Snackbar>
                    </div>
                </Container>
            </RightTray>
        </>
    );
};

export default CreateLifecycleAction;
