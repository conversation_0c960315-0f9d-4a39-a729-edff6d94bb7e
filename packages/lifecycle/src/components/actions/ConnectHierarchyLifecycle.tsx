/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useRef, useState, useMemo } from 'react';
import { useToggle, formatDateTime, queryBuilder, buildSortParams, buildQueryBasedOnFilter } from 'ui-common';
import { AgGridReact } from '@ag-grid-community/react';
import {
    Box,
    Loading,
    RichtextCellRenderer,
    RightTray,
    SelectedRows,
    tableIcons,
    tableStyles,
    LoadingOverlay,
} from 'ui-style';
import get from 'lodash/get';
import { COMPONENT_NAME, ZOOM_OPTIONS } from '../../constants';
import { schemaUrls, fetch } from '@tripudiotech/admin-api';
import { useHierarchyLifecycleStateStore } from '../../store/hierarchyLifecycleStateStore';
import { useReactFlow } from 'reactflow';

const ConnectHierarchyLifecycleAction = ({ open, onClose }) => {
    const gridRef = useRef<AgGridReact>();
    const searchQuery = useRef(null);
    const [selectedRows, setSelectedRows] = useState([]);
    const handleSelectionChanged = useCallback(() => setSelectedRows(gridRef.current.api.getSelectedRows()), []);
    const [isLoading, setIsLoading] = useState(false);
    const [addNode, selectedLifecycle] = useHierarchyLifecycleStateStore((state) => [
        state.addNode,
        state.lifecycles[state.selectedLifecycleId],
    ]);
    const { fitView } = useReactFlow();

    const gridOptions: any = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 34,
            onSelectionChanged: handleSelectionChanged,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    checkboxSelection: true,
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    minWidth: 140,
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'updatedAt',
                    headerName: 'Updated At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                },
            ],
            components: {},
            cacheBlockSize: 50,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            rowSelection: 'multiple',
            maxConcurrentDatasourceRequests: 2,
            suppressRowClickSelection: true,
        }),
        [gridRef]
    );

    const buildSearchQuery = useCallback(
        (search) => {
            const searchText = search || '';
            return queryBuilder.buildAndOperatorQuery([
                queryBuilder.buildOrOperatorQuery([
                    queryBuilder.buildContainsQuery('name', searchText),
                    queryBuilder.buildContainsQuery('description', searchText),
                ]),
                queryBuilder.buildNotInQuery('id', [...selectedLifecycle.nextLifeCycles, selectedLifecycle.id]),
            ]);
        },
        [selectedLifecycle]
    );

    const createServerSideDataSource = useCallback(() => {
        const buildParams = (params) => {
            let queryParams = {
                offset: params.startRow,
                limit: 50,
                ...buildSortParams(params.sortModel),
            };
            const filterModel = params.filterModel;
            if (Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                if (searchQuery.current) {
                    filterConditions.push(buildSearchQuery(searchQuery.current));
                }
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else {
                queryParams['query'] = JSON.stringify(buildSearchQuery(searchQuery.current));
            }
            return queryParams;
        };
        return {
            getRows: (params: any) => {
                fetch({
                    ...schemaUrls.getListLifeCycle,
                    qs: buildParams(params?.request),
                })
                    .then((response) => {
                        if (response.status !== 200) {
                            params.failCallback();
                            return;
                        }
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);

    const handleClose = () => {
        setSelectedRows([]);
        searchQuery.current = null;
        onClose();
    };

    const handleSubmit = useCallback(async () => {
        const rows = gridRef.current.api.getSelectedRows();
        if (rows.length > 0) {
            try {
                setIsLoading(true);
                await addNode(
                    rows.map((row) => row.id),
                    fitView
                );
                handleClose();
            } catch (err) {
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        }
        handleClose();
    }, [handleClose, gridRef, setIsLoading]);

    const handleSetDataSource = useCallback((event) => {
        const dataSource = createServerSideDataSource();
        event.api.setServerSideDatasource(dataSource);
    }, []);

    const handleSearchChanged = (text) => {
        searchQuery.current = text;
        if (gridRef.current) {
            handleSetDataSource(gridRef.current);
        }
    };

    const clearSelect = useCallback(() => {
        gridRef.current.api.deselectAll();
    }, [gridRef]);

    return (
        <>
            <RightTray
                title={`Connect hierarchy lifecycles from ${selectedLifecycle?.name}`}
                componentName={COMPONENT_NAME.CONNECT_HIERARCHY_LIFECYCLE}
                open={open}
                onClose={handleClose}
                onConfirm={handleSubmit}
                confirmText="Connect"
                enableSearch
                disableCloseOnClickOutside
                onSearchChange={handleSearchChanged}
            >
                {isLoading && <LoadingOverlay />}
                <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', ...tableStyles }}>
                    <SelectedRows
                        selectedRows={selectedRows}
                        clearSelect={clearSelect}
                        tooltipItemGetter={(data) => data.name}
                    />
                    <div style={{ height: '100%', overflow: 'auto' }}>
                        <AgGridReact
                            className="ag-theme-alpine"
                            ref={gridRef}
                            {...gridOptions}
                            onGridReady={handleSetDataSource}
                            icons={tableIcons}
                        />
                    </div>
                </Box>
            </RightTray>
        </>
    );
};

export default ConnectHierarchyLifecycleAction;
