/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { useToggle } from 'usehooks-ts';
import { Box, IconButton, ArrowLeft } from 'ui-style';

const CollapsablePanel = ({ children }) => {
    const [open, toggle] = useToggle(true);

    return (
        <Box
            sx={{
                width: '366px',
                maxWidth: open ? '100%' : '32px',
                transition: 'all 0.25s',
                backgroundColor: (theme) => theme.palette.glide.background.normal.inversePrimary,
                overflow: 'hidden',
                position: 'relative',
                zIndex: 100,
                height: '100%',
                display:'flex',
                flexDirection: 'column',
            }}
        >
            <Box
                sx={{
                    width: '350px',
                    maxWidth: '100%',
                    backgroundColor: (theme) => theme.palette.glide.background.normal.white,
                    opacity: open ? 1 : 0,
                    transition: 'all 0.25s',
                    height: '100%',
                    display:'flex',
                    flexDirection: 'column',
                }}
            >
                {children}
            </Box>
            <IconButton
                onClick={toggle}
                sx={{
                    position: 'absolute',
                    right: '4px',
                    top: '16px',
                    boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.25)',
                    backgroundColor: (theme) => theme.palette.glide.background.normal.white,
                    width: '24px',
                    height: '24px',
                    borderRadius: '100px',
                    '&:hover': {
                        backgroundColor: (theme) => theme.palette.glide.background.hover.inversePrimary,
                    },
                }}
                size="small"
            >
                <ArrowLeft
                    sx={{
                        transform: open ? 'none' : 'rotate(180deg)',
                        transition: 'ease-in-out 0.3s',
                    }}
                />
            </IconButton>
        </Box>
    );
};

export default CollapsablePanel;
