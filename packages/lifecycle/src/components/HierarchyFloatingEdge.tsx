/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback } from 'react';
import { getSmoothStepPath, useReactFlow, useStore } from 'reactflow';
import { getEdgeParams } from '../utils/graphUtils';
import { IconButton, DeleteIcon } from 'ui-style';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { useHierarchyLifecycleStateStore } from '../store/hierarchyLifecycleStateStore';

function HierarchyFloatingEdge({
    id,
    source,
    target,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    style = {},
    data,
    label,
    markerEnd,
    labelStyle,
    labelShowBg,
    labelBgStyle,
    labelBgPadding,
    labelBgBorderRadius,
    selected,
}) {
    const [removeEdge, isReadOnly, lifecycles, selectedLifecycleId] = useHierarchyLifecycleStateStore((state) => [
        state.removeEdge,
        state.isReadOnly,
        state.lifecycles,
        state.selectedLifecycleId,
    ]);

    const sourceNode = useStore(useCallback((store) => store.nodeInternals.get(source), [source]));
    const targetNode = useStore(useCallback((store) => store.nodeInternals.get(target), [target]));
    const { fitView } = useReactFlow();

    if (!sourceNode || !targetNode) {
        return null;
    }
    const { sx, sy, tx, ty, sourcePos, targetPos } = getEdgeParams(sourceNode, targetNode);

    const [edgePath, labelX, labelY] = getSmoothStepPath({
        sourceX: sx,
        sourceY: sy,
        sourcePosition: sourcePos,
        targetPosition: targetPos,
        targetX: tx,
        targetY: ty,
    });

    const foreignObjectSize = 24;
    const onOpenDialog = useDialog((state) => state.onOpenDialog);

    const onRemove = useCallback(() => {
        const [fromId, toId] = id.split('|');
        const fromLc = lifecycles[fromId];
        const toLc = lifecycles[toId];
        onOpenDialog(
            'Delete connection',
            `Do you really want to remove the connection from <b>${fromLc.name}</b> to <b>${toLc.name}</b>`,
            () => removeEdge(id, fitView),
            'error'
        );
    }, [id]);

    const isRelateSlectedNode = id.includes(selectedLifecycleId);

    return (
        <>
            <path
                id={id}
                className="react-flow__edge-path"
                d={edgePath}
                strokeWidth={2}
                markerEnd={markerEnd}
                style={style}
            />
            {!isReadOnly && isRelateSlectedNode && (
                <foreignObject
                    width={foreignObjectSize}
                    height={foreignObjectSize}
                    x={labelX - foreignObjectSize / 2}
                    y={labelY - foreignObjectSize / 2}
                >
                    <div>
                        <IconButton
                            sx={{
                                backgroundColor: (theme) => theme.palette.error.main,
                                color: (theme) => theme.palette.glide.text.normal.tertiary,
                                borderRadius: '100px',
                                height: '24px',
                                width: '24px',
                                '&:hover': {
                                    backgroundColor: (theme) => theme.palette.error.main,
                                },
                                '&:active': {
                                    backgroundColor: (theme) => theme.palette.error.main,
                                },
                            }}
                            onClick={onRemove}
                            size="small"
                        >
                            <DeleteIcon />
                        </IconButton>
                    </div>
                </foreignObject>
            )}
        </>
    );
}

export default HierarchyFloatingEdge;
