/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { memo, useCallback } from 'react';
import { Box, IconButton, Typography, EditIcon, PlusIcon, DeleteIcon } from 'ui-style';
import { useDebounce } from 'usehooks-ts';
import { Handle, NodeProps, NodeToolbar, Position, useReactFlow, useStore } from 'reactflow';
import { useLifecycleStateStore } from '../store/lifecycleStateStore';

const connectionNodeIdSelector = (state) => state.connectionNodeId;

const LifecycleStateNode = (props: NodeProps) => {
    const { selected, id } = props;
    const [state, addNode, removeNode, isReadOnly] = useLifecycleStateStore((state) => [
        state.states[id],
        state.addNode,
        state.removeNode,
        state.isReadOnly,
    ]);
    const debouncedState = useDebounce(state, 200);
    const { fitView } = useReactFlow();

    const connectionNodeId = useStore(connectionNodeIdSelector);
    const isTarget = connectionNodeId && connectionNodeId !== id;

    const targetHandleStyle = { zIndex: isTarget ? 3 : 1 };

    return (
        <Box
            sx={{
                '& .nodeBody': {
                    width: '150px',
                    height: '78px',
                    border: (theme) =>
                        selected
                            ? `2px solid ${theme.palette.info.main}`
                            : `1px solid ${theme.palette.glide.stroke.normal.inversePrimary}`,
                    position: 'relative',
                    overflow: 'hidden',
                    borderRadius: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    boxShadow: selected ? '0px 4px 20px rgba(0, 0, 0, 0.12)' : 'none',
                    background: (theme) => theme.palette.glide.background.normal.white,
                },
                '& .targetHandle': {
                    display: isReadOnly? 'none': 'block',
                    width: '32px',
                    left: '50%',
                    height: '12px',
                    transform: 'translate(-50%, 0)',
                    background: (theme) =>
                        isTarget
                            ? theme.palette.glide.background.hover.inverseSecondary
                            : theme.palette.glide.background.normal.inverseSecondary,
                    position: 'absolute',
                    top: -2,
                    opacity: 1,
                    border: (theme) =>
                        selected
                            ? `2px solid ${theme.palette.info.main}`
                            : `1px solid ${theme.palette.glide.stroke.normal.inversePrimary}`,
                    borderRadius: 0,
                    borderBottomLeftRadius: '2px',
                    borderBottomRightRadius: '2px',
                },
            }}
        >
            <Box className="nodeBody">
                <Typography variant="label2-med" sx={{textAlign: 'center'}}>{debouncedState?.name}</Typography>
                <Handle className="targetHandle" type="target" style={targetHandleStyle} position={Position.Left} />
                <Handle className="targetHandle" style={{ zIndex: 2 }} type="source" position={Position.Right} id="b" />
            </Box>
            {!isReadOnly && (
                <NodeToolbar position={Position.Bottom} style={{ display: 'flex', gap: '12px' }}>
                    <IconButton
                        sx={{
                            backgroundColor: (theme) => theme.palette.error.main,
                            color: (theme) => theme.palette.glide.text.normal.tertiary,
                            borderRadius: '100px',
                            height: '24px',
                            width: '24px',
                            '&:hover': {
                                backgroundColor: (theme) => theme.palette.error.main,
                            },
                            '&:active': {
                                backgroundColor: (theme) => theme.palette.error.main,
                            },
                        }}
                        onClick={() => removeNode(id, fitView)}
                        size="small"
                    >
                        <DeleteIcon />
                    </IconButton>
                    <IconButton
                        sx={{
                            backgroundColor: (theme) => theme.palette.glide.background.normal.quarternary,
                            color: (theme) => theme.palette.glide.text.normal.tertiary,
                            borderRadius: '100px',
                            height: '24px',
                            width: '24px',
                            '&:hover': {
                                backgroundColor: (theme) => theme.palette.glide.background.hover.quarternary,
                            },
                            '&:active': {
                                backgroundColor: (theme) => theme.palette.glide.background.active.quarternary,
                            },
                        }}
                        onClick={() => addNode(id, fitView)}
                    >
                        <PlusIcon />
                    </IconButton>
                </NodeToolbar>
            )}
        </Box>
    );
};

export default memo(LifecycleStateNode);
