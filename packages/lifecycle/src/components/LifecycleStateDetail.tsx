/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useMemo } from 'react';
import { Typography, SchemaField, Loading, BooleanField, Grid, SchemaFieldSection, Fade, Box } from 'ui-style';
import { PERMISSION_FIELDS, STATE_TYPES } from '../constants';
import { useLifecycleStateStore } from '../store/lifecycleStateStore';
import get from 'lodash/get';
import { isCreatingState } from '../utils/lifecycleUtils';
import keys from 'lodash/keys';

const LifecycleStateDetail = () => {
    const [selectedState, getContextRules, loadingContextRules] = useLifecycleStateStore((state) => [
        state.states[state.selectedStateId],
        state.getContextRules,
        state.loadingContextRules,
    ]);
    const stateType = useMemo(() => {
        if (selectedState?.isObsoleted) {
            return STATE_TYPES.OBSOLETED;
        }
        if (selectedState?.isSuperseded) {
            return STATE_TYPES.SUPERSEDED;
        }
        if (selectedState?.isOfficial) {
            return STATE_TYPES.OFFICIAL;
        }
        return '-';
    }, [selectedState]);

    useEffect(() => {
        getContextRules();
    }, [selectedState]);

    const getPermissionValueRender = useCallback(
        (selectedState, permission) => {
            const value = get(selectedState, [permission.key], []);
            if (!value) return '-';
            return value.map((v) => v?.label).join(', ') || '-';
        },
        [selectedState]
    );

    const canCreate = isCreatingState(selectedState);
    return (
        <Fade style={{ flexGrow: 1 }} in={Boolean(selectedState)} unmountOnExit>
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'auto',
                    flexGrow: 1,
                }}
            >
                <Box
                    sx={{
                        backgroundColor: (theme) => theme.palette.glide.background.normal.inversePrimary,
                        padding: '8px 24px',
                    }}
                >
                    <Typography variant="title3">{selectedState?.name || 'State Information'}</Typography>
                </Box>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        p: '16px',
                        gap: '16px',
                        overflow: 'auto',
                    }}
                >
                    <SchemaFieldSection>General Information</SchemaFieldSection>
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <SchemaField label="State Name" value={selectedState?.name || '-'} />
                            <SchemaField label="State Type" value={stateType} />
                            <SchemaField label="Desciption" value={selectedState?.description} />
                            <BooleanField
                                labelTitle="Can be used as start state if enabled"
                                label="Creating State"
                                value={canCreate}
                            />
                        </Grid>
                    </Grid>
                    <SchemaFieldSection>Permission</SchemaFieldSection>
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            {PERMISSION_FIELDS.map((permission) => {
                                return (
                                    !permission.hide && (
                                        <SchemaField
                                            key={permission.key}
                                            label={permission.label}
                                            value={getPermissionValueRender(selectedState, permission)}
                                        />
                                    )
                                );
                            })}
                        </Grid>
                    </Grid>
                    <SchemaFieldSection>Context Rules</SchemaFieldSection>
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            {loadingContextRules && <Loading />}
                            {keys(selectedState?.contextRule).map((key) => {
                                return (
                                    !['isModified', 'shouldCreate'].includes(key) && (
                                        <SchemaField
                                            key={key}
                                            label={key}
                                            value={
                                                get(selectedState, ['contextRule', key, 'includes'], [])
                                                    .map((r) => r.label)
                                                    .join(', ') || '-'
                                            }
                                        />
                                    )
                                );
                            })}
                        </Grid>
                    </Grid>
                </Box>
            </Box>
        </Fade>
    );
};

export default LifecycleStateDetail;
