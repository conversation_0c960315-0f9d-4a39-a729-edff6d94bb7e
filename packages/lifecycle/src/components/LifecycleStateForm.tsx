/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo } from 'react';
import {
    styled,
    Typography,
    AccordionDetails,
    TextField,
    BaseAccordion,
    AccordionSummary,
    ExpandMoreIcon,
    Checkbox,
    MenuItem,
    Fade,
    Autocomplete,
    FormControlLabel,
} from 'ui-style';
import { useLifecycleStateStore } from '../store/lifecycleStateStore';
import {
    ACCESS_OPTIONS_MAP,
    PERMISSION_FIELDS,
    REVISE_TYPES_OPTIONS,
    STATE_TYPES,
    STATE_TYPES_OPTIONS,
} from '../constants';
import ContextRuleForm from './ContextRuleForm';
import { usePermissionRoles } from '@tripudiotech/admin-caching-store';
import { isCreatingState } from '../utils/lifecycleUtils';
import orderBy from 'lodash/orderBy';

const FormContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    overflow: 'auto',
    flexGrow: 1,
    '& .title': {
        backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
        padding: '8px 24px',
    },
    '& .form': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '16px',
    },
    '& .sectionLabel': {
        color: theme.palette.info.main,
    },
    '& .summary': {
        position: 'sticky',
        top: 0,
        zIndex: 102,
    },
    '& .sectionSummary': {
        height: '40px',
        width: '40px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    },
    '& .formDetails': {
        display: 'flex',
        flexDirection: 'column',
        padding: '16px 0',
        gap: '12px',
        paddingBottom: 0,
    },
    '& .emptyContainer': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        margin: '24px',
        textAlign: 'center',
    },
    '& .MuiAutocomplete-root': {
        '& .MuiFormLabel-root': {
            top: '-3%',
        },
        '& button': {
            height: '24px',
            width: '24px',
        },
        '& .MuiAutocomplete-endAdornment': {
            top: 'calc(50% -  11px)',
        },
    },
}));

const LifecycleStateForm = () => {
    const [revisable, selectedState, updateStateInfo, updateStateType] = useLifecycleStateStore((state) => [
        state.revisable,
        state.states[state.selectedStateId],
        state.updateStateInfo,
        state.updateStateType,
    ]);

    const [permissionRoles, getPermissionRoles] = usePermissionRoles((state) => [
        state.permissionRoles,
        state.getPermissionRoles,
    ]);

    useEffect(() => {
        const loaddata = async () => {
            await getPermissionRoles();
        };
        loaddata();
    }, []);
    const permissionRoleOptions = useMemo(() => {
        return orderBy(permissionRoles?.map((p) => ({ id: p.name, label: p.name })) || [], item => item.label?.toLowerCase());
    }, [permissionRoles]);

    console.log('permissionRoleOptions', permissionRoles, permissionRoleOptions);
    const stateType = useMemo(() => {
        if (selectedState?.isObsoleted) {
            return STATE_TYPES.OBSOLETED;
        }
        if (selectedState?.isSuperseded) {
            return STATE_TYPES.SUPERSEDED;
        }
        if (selectedState?.isOfficial) {
            return STATE_TYPES.OFFICIAL;
        }
        return 'None';
    }, [selectedState]);

    const handleFieldChange = useCallback(
        (key) => {
            return (e) => {
                updateStateInfo(selectedState?.id, key, e.target.value);
            };
        },
        [selectedState]
    );

    const handleStateTypeChange = useCallback(() => {
        return (e) => {
            updateStateType(selectedState?.id, e.target.value);
        };
    }, [selectedState]);

    const canCreate = isCreatingState(selectedState);

    const toggleCanCreate = (e) => {
        if (e.target.checked) {
            updateStateInfo(selectedState.id, 'canCreate', [ACCESS_OPTIONS_MAP.get('Any')]);
            if (
                selectedState?.canConnectAsFromSide?.every(
                    (permission: any) => !['Any', 'Owner'].includes(permission.id)
                )
            ) {
                updateStateInfo(selectedState.id, 'canConnectAsFromSide', [
                    ...selectedState?.canConnectAsFromSide,
                    ACCESS_OPTIONS_MAP.get('Owner'),
                ]);
            }
        } else {
            updateStateInfo(selectedState.id, 'canCreate', []);
        }
    };

    return (
        <Fade style={{ flexGrow: 1 }} in={Boolean(selectedState)} unmountOnExit>
            <FormContainer>
                <div className="title">
                    <Typography variant="title3">{selectedState?.name || 'State Information'}</Typography>
                </div>
                <div className="form">
                    <BaseAccordion
                        defaultExpanded
                        TransitionProps={{ unmountOnExit: true }}
                        sx={{ '&.Mui-expanded': { margin: 0 } }}
                    >
                        <AccordionSummary
                            className="summary"
                            expandIcon={
                                <div className="allowPointer sectionSummary">
                                    <ExpandMoreIcon sx={{ width: '16px', height: '16px' }} />
                                </div>
                            }
                        >
                            <Typography variant="label2-med">Basic Information</Typography>
                        </AccordionSummary>
                        <AccordionDetails className="accordionDetails">
                            <div className="formDetails">
                                <TextField
                                    variant="outlined"
                                    label="Name"
                                    required
                                    name="name"
                                    value={selectedState?.name}
                                    onChange={handleFieldChange('name')}
                                />
                                <TextField
                                    variant="outlined"
                                    label="Description"
                                    required
                                    value={selectedState?.description}
                                    onChange={handleFieldChange('description')}
                                />
                                <Fade in={revisable} unmountOnExit>
                                    <TextField
                                        variant="outlined"
                                        label="Revise Type"
                                        required
                                        type="text"
                                        select
                                        value={selectedState?.reviseType}
                                        onChange={handleFieldChange('reviseType')}
                                    >
                                        {REVISE_TYPES_OPTIONS.map((option) => (
                                            <MenuItem key={option.id} value={option.value}>
                                                {option.label}
                                            </MenuItem>
                                        ))}
                                    </TextField>
                                </Fade>
                                <TextField
                                    variant="outlined"
                                    label="State Type"
                                    type="text"
                                    select
                                    value={stateType}
                                    onChange={handleStateTypeChange()}
                                >
                                    <MenuItem value={'None'}>
                                        <em>None</em>
                                    </MenuItem>
                                    {STATE_TYPES_OPTIONS.map((option) => (
                                        <MenuItem key={option.id} value={option.value}>
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </TextField>
                                <FormControlLabel
                                    label="Creating State"
                                    control={<Checkbox checked={canCreate} onChange={toggleCanCreate} />}
                                />
                            </div>
                        </AccordionDetails>
                    </BaseAccordion>
                    <BaseAccordion TransitionProps={{ unmountOnExit: true }} sx={{ '&.Mui-expanded': { margin: 0 } }}>
                        <AccordionSummary
                            className="summary"
                            expandIcon={
                                <div className="allowPointer sectionSummary">
                                    <ExpandMoreIcon sx={{ width: '16px', height: '16px' }} />
                                </div>
                            }
                        >
                            <Typography variant="label2-med">Permission</Typography>
                        </AccordionSummary>
                        <AccordionDetails className="accordionDetails">
                            <div className="formDetails">
                                {PERMISSION_FIELDS.map(
                                    ({ key, label, hide }) =>
                                        !hide && (
                                            <Autocomplete
                                                key={key}
                                                multiple
                                                id={`permission-${key}`}
                                                options={permissionRoleOptions}
                                                size="small"
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        variant="outlined"
                                                        label={label}
                                                        size="small"
                                                    />
                                                )}
                                                getOptionLabel={(option) => option?.label || option}
                                                onChange={(e, newValue) =>
                                                    updateStateInfo(selectedState?.id, key, newValue)
                                                }
                                                value={selectedState?.[key] || []}
                                            />
                                        )
                                )}
                            </div>
                        </AccordionDetails>
                    </BaseAccordion>
                    <ContextRuleForm permissionRoles={permissionRoleOptions} />
                </div>
            </FormContainer>
        </Fade>
    );
};

export default LifecycleStateForm;
