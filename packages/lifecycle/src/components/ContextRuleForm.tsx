/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo } from 'react';
import {
    styled,
    BaseAccordion,
    AccordionSummary,
    Typography,
    AccordionDetails,
    ExpandMoreIcon,
    Autocomplete,
    Loading,
    TextField,
    Box,
    PlusIcon,
    Button,
    Menu,
    MenuItem,
} from 'ui-style';
import { queryBuilder } from 'ui-common';
import { CONTEXT_RULE_FIELDS } from '../constants';
import { fetchAgent, useLifecycleStateStore } from '../store/lifecycleStateStore';
import get from 'lodash/get';
import keys from 'lodash/keys';
import { useAgent } from '@tripudiotech/admin-caching-store';
import orderBy from 'lodash/orderBy';

const Container = styled('div')(({ theme }) => ({}));

const AddButton = ({ contextRule, permissionRoles, onAdd }) => {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };
    const onSelect = (role) => {
        return () => {
            onAdd(role);
            handleClose();
        };
    };
    return (
        <Box>
            <Button onClick={handleClick} size="small" variant="contained" color="secondary" endIcon={<PlusIcon />}>
                Add Rule
            </Button>
            <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
            >
                {permissionRoles
                    ?.filter((role) => !contextRule[role.id])
                    ?.map((role) => (
                        <MenuItem key={role.id} onClick={onSelect(role.id)}>
                            {role.id}
                        </MenuItem>
                    ))}
            </Menu>
        </Box>
    );
};

const ContextRuleForm = ({ permissionRoles }) => {
    const [getContextRules, selectedState, updateStateContextRule] = useLifecycleStateStore((state) => [
        state.getContextRules,
        state.states[state.selectedStateId],
        state.updateStateContextRule,
    ]);
    const agents = useAgent((state) => state.agents);
    
    const agentOptions = useMemo(() => orderBy( agents?.map((agent) => ({
        label: agent.properties.name,
        value: agent.id,
    })) || [], item => item.label?.toLowerCase()), [agents])

    useEffect(() => {
        getContextRules();
    }, [selectedState]);
    return (
        <Container>
            <BaseAccordion TransitionProps={{ unmountOnExit: true }} sx={{ '&.Mui-expanded': { margin: 0 } }}>
                <AccordionSummary
                    className="summary"
                    expandIcon={
                        <div className="allowPointer sectionSummary">
                            <ExpandMoreIcon sx={{ width: '16px', height: '16px' }} />
                        </div>
                    }
                >
                    <Typography variant="label2-med">Context Rule</Typography>
                </AccordionSummary>
                <AccordionDetails className="accordionDetails">
                    <div className="formDetails">
                        {selectedState?.contextRule ? (
                            <>
                                {keys(selectedState.contextRule).map((key) => {
                                    if (!['isModified', 'shouldCreate'].includes(key)) {
                                        return (
                                            <Autocomplete
                                                key={key}
                                                multiple
                                                id={`permission-${key}`}
                                                options={agentOptions}
                                                size="small"
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        variant="outlined"
                                                        label={key}
                                                        size="small"
                                                    />
                                                )}
                                                getOptionLabel={(option) => option?.label || option}
                                                onChange={(e, newValue) => updateStateContextRule(key, newValue)}
                                                value={selectedState?.contextRule?.[key]?.includes || []}
                                            />
                                        );
                                    }
                                })}
                                <AddButton
                                    onAdd={(role) => {
                                        updateStateContextRule(role, []);
                                    }}
                                    contextRule={selectedState?.contextRule}
                                    permissionRoles={permissionRoles}
                                />
                            </>
                        ) : (
                            <Loading />
                        )}
                    </div>
                </AccordionDetails>
            </BaseAccordion>
        </Container>
    );
};

export default ContextRuleForm;
