/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo } from 'react';
import { ReactFlow } from 'reactflow';
import { useHierarchyLifecycleStateStore } from '../store/hierarchyLifecycleStateStore';
import ConnectionLine from './ConnectionLine';
import HierarchyFloatingEdge from './HierarchyFloatingEdge';
import LifecycleHierarchyNode from './LifecycleHierarchyNode';
import LifecycleStateNode from './LifecycleStateNode';
import SimpleFloatingEdge from './SimpleFloatingEdge';

const connectionLineStyle = {
    strokeWidth: 3,
    stroke: '#2F54EB',
};

const HierarchyLifecycleStateDiagram = () => {
    const [nodes, edges, onNodesChange, onEdgesChange, onConnect, onSelectionChange, isReadOnly] = useHierarchyLifecycleStateStore(
        (state) => [
            state.nodes,
            state.edges,
            state.onNodesChange,
            state.onEdgesChange,
            state.onConnect,
            state.onSelectionChange,
            state.isReadOnly
        ]
    );

    const config: any = useMemo(
        () => ({
            nodesDraggable: true,
            nodesConnectable: !isReadOnly,
            connectOnClick: !isReadOnly,
            elementsSelectable: true,
            onNodesChange,
            onEdgesChange,
            deleteKeyCode: ['Delete'],
            nodeTypes: { LifecycleHierarchyNode: LifecycleHierarchyNode },
            fitView: true,
            edgeTypes: {
                HierarchyFloatingEdge: HierarchyFloatingEdge,
            },
            connectionLineComponent: ConnectionLine,
            onSelectionChange: onSelectionChange,
            onConnect,
        }),
        [isReadOnly]
    );
    return <ReactFlow {...config} nodes={nodes} edges={edges} connectionLineStyle={connectionLineStyle}></ReactFlow>;
};

export default HierarchyLifecycleStateDiagram;
