/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo } from 'react';
import { styled, Typography, MenuItem, Fade, FormikTextField, FormControlLabel, Checkbox } from 'ui-style';
import { Formik, Form, Field } from 'formik';
import * as yup from 'yup';
import { SEPARATORS, SEPARATOR_LABEL } from '../constants';
import { useLifecycleStateStore } from '../store/lifecycleStateStore';
import get from 'lodash/get';

const FormContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 'auto',
    '& .generalInfo': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '12px',
    },
    '& .sectionLabel': {
        color: theme.palette.info.main,
    },
    '& .inlineContainer': {
        display: 'flex',
        gap: '12px',
        flex: 2,
    },
    '& .revisionPattern': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
    },
}));

const LifecycleForm = ({ formRef, handleSubmit }) => {
    const [lifecycleDetail, revisable, setRevisable] = useLifecycleStateStore((state) => [
        state.lifecycleDetail,
        state.revisable,
        state.setRevisable,
    ]);
    const initialValues = useMemo(() => {
        if (lifecycleDetail) {
            return {
                name: get(lifecycleDetail, 'lifeCycle.name', ''),
                description: get(lifecycleDetail, 'lifeCycle.description', ''),
                major: get(lifecycleDetail, 'lifeCycle.revisionPattern.major', ''),
                minor: get(lifecycleDetail, 'lifeCycle.revisionPattern.minor', ''),
                patch: get(lifecycleDetail, 'lifeCycle.revisionPattern.patch', ''),
                separator: get(lifecycleDetail, 'lifeCycle.revisionPattern.separator', ''),
            };
        }
        return {
            name: '',
            description: '',
            major: '',
            minor: '',
            patch: '',
            separator: '',
        };
    }, []);
    const validationSchema = useMemo(
        () =>
            yup.object().shape({
                name: yup.string().required('Name is required'),
                description: yup.string().required('Description is required'),
                major: revisable
                    ? yup
                          .string()
                          .matches(RegExp('^[0-9a-zA-Z]$'), 'Major should be one character of alphabet or number')
                          .required('Major is required')
                    : yup
                          .string()
                          .matches(RegExp('^[0-9a-zA-Z]$'), 'Major should be one character of alphabet or number')
                          .nullable(),
                minor: yup
                    .string()
                    .matches(RegExp('^[0-9a-zA-Z]$'), 'Minor should be one character of alphabet or number')
                    .nullable(),
                patch: yup
                    .string()
                    .matches(RegExp('^[0-9a-zA-Z]$'), 'Patch should be one character of alphabet or number')
                    .nullable(),
                separator: yup
                    .mixed()
                    .when(['minor', 'patch'], {
                        is: (minor, patch) => Boolean(minor) || Boolean(patch),
                        then: (schema) => schema.required('Separator is required'),
                        otherwise: (schema) => schema.nullable(),
                    })
                    .oneOf(SEPARATORS)
                    .nullable(),
            }),
        [revisable]
    );
    const handleRevisableChange = useCallback(
        (setFieldValue) => (e) => {
            const value = e.target.checked;
            if (!value) {
                setFieldValue('major', '');
                setFieldValue('minor', '');
                setFieldValue('patch', '');
                setFieldValue('separator', '');
            }
            setRevisable(value);
        },
        [setRevisable]
    );

    return (
        <FormContainer>
            <Formik
                enableReinitialize
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting }) => {
                    handleSubmit(values);
                    setSubmitting(false);
                }}
                innerRef={formRef}
            >
                {({ values, setFieldValue }) => (
                    <Form>
                        <div className="generalInfo">
                            <Typography variant="label2-med" className="sectionLabel">
                                General Information
                            </Typography>
                            <Field
                                fullWidth
                                component={FormikTextField}
                                label="Name"
                                name="name"
                                variant="outlined"
                                required
                            />
                            <Field
                                fullWidth
                                component={FormikTextField}
                                label="Description"
                                name="description"
                                variant="outlined"
                                required
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox checked={revisable} onChange={handleRevisableChange(setFieldValue)} />
                                }
                                label="Revisable"
                            />
                            <Fade in={revisable} unmountOnExit>
                                <div className="revisionPattern">
                                    <Typography variant="label2-med" className="sectionLabel">
                                        Revision Pattern
                                    </Typography>
                                    <div className="inlineContainer">
                                        <Field
                                            fullWidth
                                            component={FormikTextField}
                                            label="Major"
                                            name="major"
                                            variant="outlined"
                                        />
                                        <Field
                                            fullWidth
                                            component={FormikTextField}
                                            label="Minor"
                                            name="minor"
                                            variant="outlined"
                                        />
                                    </div>
                                    <div className="inlineContainer">
                                        <Field
                                            fullWidth
                                            component={FormikTextField}
                                            label="Patch"
                                            name="patch"
                                            variant="outlined"
                                        />
                                        <Field
                                            fullWidth
                                            select
                                            type="text"
                                            name="separator"
                                            label="Separator"
                                            component={FormikTextField}
                                        >
                                            <MenuItem value="">
                                                <em>None</em>
                                            </MenuItem>
                                            {Object.values(SEPARATORS).map((sep) => (
                                                <MenuItem value={sep} key={sep}>
                                                    {SEPARATOR_LABEL[sep]}
                                                </MenuItem>
                                            ))}
                                        </Field>
                                    </div>
                                </div>
                            </Fade>
                        </div>
                    </Form>
                )}
            </Formik>
        </FormContainer>
    );
};

export default LifecycleForm;
