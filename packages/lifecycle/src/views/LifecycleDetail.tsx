/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useNavigate, useParams, Link, useMatch, Outlet } from 'react-router-dom';
import { COMPONENT_NAME, SEPARATORS } from '../constants';
import {
    styled,
    <PERSON><PERSON>,
    <PERSON>T<PERSON>,
    <PERSON>nackbar,
    Alert,
    Typography,
    Box,
    SchemaFieldSection,
    Grid,
    SchemaField,
    TabGroup,
    MainTooltip,
    LoadingOverlay,
} from 'ui-style';

import { useToggle } from 'ui-common';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import get from 'lodash/get';
import CollapsablePanel from '../components/CollapsablePanel';
import LifecycleForm from '../components/LifecycleForm';
import { FormikProps } from 'formik';
import { deleteLifecycle } from '../actions';
import { updateLifecycle, useLifecycleStateStore } from '../store/lifecycleStateStore';
import LifecycleStateForm from '../components/LifecycleStateForm';
import LifecycleStateDetail from '../components/LifecycleStateDetail';
import { formatRevisionPattern } from '../utils/graphUtils';
import { useOnRefresh } from './LifecycleList';
import { useHierarchyLifecycleStateStore } from '../store/hierarchyLifecycleStateStore';

const Container = styled('div')(({ theme }) => ({
    width: '100%',
    flexGrow: 1,
    display: 'flex',
    overflow: 'hidden',
    backgroundColor: theme.palette.glide.background.normal.inversePrimary,
    '& .diagramContainer': {
        position: 'relative',
        flexGrow: 1,
    },
    '& .alertContainer': {
        width: '100%',
        maxWidth: '420px',
    },
    '& .errorTitle': {
        fontSize: '0.875rem',
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 600,
    },
    '& .errorDescription': {
        fontSize: '0.875rem',
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 400,
    },
    '& .errorList': {
        paddingInlineStart: '24px',
    },
}));

const LifecycleDetail = () => {
    const { onRefresh } = useOnRefresh();
    const { lifecycleId } = useParams();
    const [errors, setErrors] = useState<string[]>([]);
    const [isEditing, isEditingToggle] = useToggle(false);
    const [isFirstLoading, setIsFirstLoading] = useState(false);
    const [isActionLoading, setIsActionLoading] = useState(false);
    const formRef = useRef<FormikProps<any>>(null);
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const navigate = useNavigate();
    const matched = useMatch('lifecycle/:lifecycleId/:view');
    const view = get(matched, ['params', 'view']);
    const isHierarchy = view === 'hierarchy';

    const [
        lifeCycleDetail,
        getLifecycle,
        setIsReadOnly,
        resetLifecycleStore,
        formatLifecycleStates,
        revisable,
        clearStore,
        validateStates,
    ] = useLifecycleStateStore((state) => [
        state.lifecycleDetail,
        state.getLifecycle,
        state.setIsReadOnly,
        state.resetStore,
        state.formatLifecycleStates,
        state.revisable,
        state.clearStore,
        state.validate,
    ]);

    const [getLifecycleHierarchy, setHierarchyReadOnly] = useHierarchyLifecycleStateStore((state) => [
        state.getLifecycleHierarchy,
        state.setIsReadOnly,
    ]);

    const name = get(lifeCycleDetail, 'lifeCycle.name', '-');
    const revisionPattern = useMemo(() => {
        const pattern = get(lifeCycleDetail, 'lifeCycle.revisionPattern', null);
        if (Boolean(pattern)) {
            const { major, minor, patch, separator } = pattern;
            return [major, minor, patch].filter(Boolean).join(SEPARATORS[separator] || '');
        }
        return '-';
    }, [lifeCycleDetail]);

    const loaddata = async () => {
        try {
            setIsFirstLoading(true);
            await Promise.all([getLifecycle(lifecycleId, true), getLifecycleHierarchy(lifecycleId, true)]);
        } catch (err) {
            console.error(err);
        } finally {
            setIsFirstLoading(false);
        }
    };

    useEffect(() => {
        loaddata();
    }, []);

    const handleCancel = () => {
        if (isEditing) {
            isEditingToggle.close();
            setIsReadOnly(true);
            setHierarchyReadOnly(true);
            resetLifecycleStore();
            return;
        }
        handleClose();
    };

    const handleClose = () => {
        clearStore();
        navigate(`/lifecycle`);
    };

    const changeToEditing = () => {
        isEditingToggle.open();
        setIsReadOnly(false);
        setHierarchyReadOnly(false);
    };

    const handleCloseValidation = useCallback(() => setErrors([]), [setErrors]);

    const handleDelete = useCallback(async () => {
        try {
            setIsActionLoading(true);
            const res = await deleteLifecycle(lifecycleId, name);
            if (res) {
                onRefresh();
                handleClose();
            }
        } catch (error) {
        } finally {
            setIsActionLoading(false);
        }
    }, [lifecycleId, name]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete lifecycle',
            `Do you really want to delete the lifecycle <b>${name}</b>`,
            handleDelete,
            'error'
        );
    }, [onOpenDialog, handleDelete, name]);

    const handleSubmit = useCallback(
        async (values) => {
            try {
                setIsActionLoading(true);
                const errors = validateStates();
                setErrors(errors);
                if (errors.length > 0) {
                    return;
                }
                const [states, contextRules] = formatLifecycleStates();

                const request = {
                    states: states,
                    revisionPattern: revisable ? formatRevisionPattern(values) : null,
                    id: get(lifeCycleDetail, 'lifeCycle.id'),
                    name: values.name,
                    description: values.description,
                };

                const updateResult = await updateLifecycle(request, contextRules);

                if (updateResult) {
                    onRefresh();
                    handleClose();
                }
            } catch (error) {
                console.error(error);
            } finally {
                setIsActionLoading(false);
            }
        },
        [onRefresh, lifeCycleDetail, revisable]
    );

    const onSubmit = () => {
        formRef.current?.submitForm();
    };

    return (
        <RightTray
            title={name}
            componentName={COMPONENT_NAME.DETAIL_LIFECYCLE}
            open={true}
            onClose={handleClose}
            fullWidth
            hideConfirm
            anchor="bottom"
        >
            {isFirstLoading ? (
                <LoadingOverlay />
            ) : (
                <>
                    {isActionLoading && <LoadingOverlay />}
                    <Container>
                        <CollapsablePanel>
                            <Box
                                sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    height: '100%',
                                    justifyContent: 'flex-start',
                                }}
                            >
                                {!isEditing || isHierarchy ? (
                                    <>
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                p: '16px',
                                                gap: '16px',
                                            }}
                                        >
                                            <SchemaFieldSection>General Information</SchemaFieldSection>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12}>
                                                    <SchemaField label="Name" value={name || '-'} />
                                                    <SchemaField label="Revision Pattern" value={revisionPattern} />
                                                    <SchemaField
                                                        label="Description"
                                                        value={get(lifeCycleDetail, 'lifeCycle.description')}
                                                        breakline
                                                    />
                                                </Grid>
                                            </Grid>
                                        </Box>
                                        {!isHierarchy && <LifecycleStateDetail />}
                                    </>
                                ) : (
                                    <>
                                        <LifecycleForm formRef={formRef} handleSubmit={handleSubmit} />
                                        <LifecycleStateForm />
                                    </>
                                )}
                            </Box>
                        </CollapsablePanel>
                        <div className="diagramContainer">
                            {!isEditing && (
                                <TabGroup
                                    value={view}
                                    variant="scrollable"
                                    scrollButtons="auto"
                                    allowScrollButtonsMobile
                                    aria-label="classification navigation tabs"
                                    items={LIFECYCLE_DETAIL_TABS}
                                    size="small"
                                    style={{
                                        marginTop: '8px',
                                    }}
                                />
                            )}
                            <Outlet />
                            <Snackbar
                                open={errors.length > 0}
                                anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                                autoHideDuration={6000}
                                onClose={handleCloseValidation}
                            >
                                <Alert
                                    onClose={handleCloseValidation}
                                    severity="error"
                                    sx={{ width: '100%', maxWidth: '420px' }}
                                >
                                    <Typography className="errorTitle">Some states are not valid</Typography>
                                    <ul className="errorList">
                                        {errors.map((error) => (
                                            <li className="errorItem" key={error}>
                                                <Typography className="errorDescription">{error}</Typography>
                                            </li>
                                        ))}
                                    </ul>
                                </Alert>
                            </Snackbar>
                        </div>
                    </Container>
                    <Box
                        sx={{
                            mt: 'auto',
                            display: 'flex',
                            gap: '8px',
                            justifyContent: 'flex-end',
                            margin: '16px 24px',
                        }}
                    >
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                                mr: '8px',
                                maxWidth: '160px',
                            }}
                            variant="contained"
                            color="secondary"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                        {!isEditing && (
                            <MainTooltip
                                title={
                                    get(lifeCycleDetail, 'lifeCycle.system')
                                        ? 'You are not allowed to delete system lifecycle'
                                        : ''
                                }
                            >
                                <span>
                                    <Button
                                        sx={{
                                            width: '136px',
                                            justifyContent: 'flex-start',
                                        }}
                                        variant="outlined"
                                        onClick={onDelete}
                                        color="error"
                                        disabled={get(lifeCycleDetail, 'lifeCycle.system')}
                                    >
                                        Delete
                                    </Button>
                                </span>
                            </MainTooltip>
                        )}
                        {!(isEditing && isHierarchy) && (
                            <span>
                                <Button
                                    sx={{
                                        width: '136px',
                                        justifyContent: 'flex-start',
                                        maxWidth: '260px',
                                    }}
                                    variant="contained"
                                    onClick={() => (isEditing ? onSubmit() : changeToEditing())}
                                    color="primary"
                                >
                                    {isEditing ? 'Save' : 'Edit'}
                                </Button>
                            </span>
                        )}
                    </Box>
                </>
            )}
        </RightTray>
    );
};

export default LifecycleDetail;

export const LIFECYCLE_DETAIL_TABS = [
    {
        id: 'lifecycle-tab',
        label: 'Lifecycle',
        value: 'lifecycle',
        to: 'lifecycle',
        component: Link,
    },
    {
        id: 'hierarchy-tab',
        label: 'Hierarchy',
        value: 'hierarchy',
        to: 'hierarchy',
        component: Link,
    },
];
