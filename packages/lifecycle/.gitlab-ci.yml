.lifecycle-variables:
  variables:
    PACKAGE_DIR: packages/lifecycle

aws-stag-lifecycle-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .lifecycle-variables

aws-stag-lifecycle-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .lifecycle-variables
  needs:
    - aws-stag-lifecycle-package

gcp-stag-lifecycle-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .lifecycle-variables

gcp-stag-lifecycle-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .lifecycle-variables
  needs:
    - gcp-stag-lifecycle-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-lifecycle-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .lifecycle-variables

gcp-uat-lifecycle-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .lifecycle-variables
  needs:
    - gcp-uat-lifecycle-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json