.integration-variables:
  variables:
    PACKAGE_DIR: packages/integration

aws-stag-integration-package:
  extends:
    - .npm-package
    - .aws-stag-variables
    - .integration-variables

aws-stag-integration-publish:
  extends:
    - .aws-bucket-publish
    - .aws-stag-variables
    - .integration-variables
  needs:
    - aws-stag-integration-package

gcp-stag-integration-package:
  extends:
    - .npm-package
    - .gcp-stag-variables
    - .integration-variables

gcp-stag-integration-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-stag-variables
    - .integration-variables
  needs:
    - gcp-stag-integration-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json

gcp-uat-integration-package:
  extends:
    - .npm-package
    - .gcp-uat-variables
    - .integration-variables

gcp-uat-integration-publish:
  extends:
    - .gcp-bucket-publish
    - .gcp-uat-variables
    - .integration-variables
  needs:
    - gcp-uat-integration-package
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json