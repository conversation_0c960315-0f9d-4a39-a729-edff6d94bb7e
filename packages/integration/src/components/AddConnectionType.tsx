/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Button,
    RightTray,
    Typography,
    Box,
    PlusIcon,
    TextField,
    IconButton,
    DeleteIcon,
    EmptyDocument,
} from 'ui-style';
import { useToggle } from 'ui-common';
import { useCallback, useEffect, useState } from 'react';
import Dropzone from 'react-dropzone';
import ComponentIcon from './ComponentIcon';
import useIntegrationStore from '../store';

const AddConnectionType = () => {
    const [open, openToggle] = useToggle();
    const [name, setName] = useState('');
    const [selectedFile, setSelectedFile] = useState(null);
    const installConnectionType = useIntegrationStore((state) => state.installConnectionType);

    const handleSubmit = useCallback(async () => {
        await installConnectionType(name, selectedFile);
        openToggle.close();
    }, [installConnectionType, name, selectedFile]);

    useEffect(() => {
        if (!open) {
            setName(null);
            setSelectedFile(null);
        }
    }, [open]);
    return (
        <>
            <Button variant="contained" color="secondary" onClick={openToggle.open} size="small" endIcon={<PlusIcon />}>
                Install
            </Button>
            <RightTray
                title={'Install Connection'}
                componentName={'install-connection-type'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Apply"
                disabled={!name || !selectedFile}
                onConfirm={handleSubmit}
            >
                <Box sx={{ p: '24px', display: 'flex', flexDirection: 'column', gap: '8px', overflow: 'auto' }}>
                    <Typography
                        sx={{ color: (theme) => theme.palette.glide.info }}
                        variant="label2-med"
                        className="sectionLabel"
                    >
                        Basic Information
                    </Typography>
                    <TextField
                        label="Name"
                        name="name"
                        required
                        helperText="Input name for new connection type"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                    />
                    <Typography
                        sx={{ color: (theme) => theme.palette.glide.info }}
                        variant="label2-med"
                        className="sectionLabel"
                    >
                        Upload file
                    </Typography>
                    <Dropzone
                        accept={{ ['.jar,.zip']: ['.jar', '.zip'] }}
                        multiple={false}
                        onDropAccepted={(files) => setSelectedFile(files[0])}
                    >
                        {({ getRootProps, getInputProps, isDragActive }) => (
                            <Box
                                {...getRootProps()}
                                sx={{
                                    width: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '8px',
                                    padding: '32px',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    border: (theme) =>
                                        `2px dashed ${
                                            isDragActive
                                                ? theme.palette.glide.stroke.hover.primary
                                                : theme.palette.glide.stroke.normal.primary
                                        }`,
                                    cursor: selectedFile ? 'unset' : 'pointer',
                                    borderRadius: '4px',
                                    position: 'relative',
                                }}
                            >
                                {selectedFile ? (
                                    <>
                                        <ComponentIcon style={{ height: '160px', width: '160px' }} />
                                        <Typography variant="bo2" sx={{ display: 'block' }}>
                                            {selectedFile.name}
                                        </Typography>
                                        <IconButton
                                            color="error"
                                            onClick={() => setSelectedFile(null)}
                                            sx={{
                                                position: 'absolute',
                                                top: '8px',
                                                right: '8px',
                                            }}
                                            size="small"
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                    </>
                                ) : (
                                    <>
                                        <input {...getInputProps()} />
                                        <EmptyDocument style={{ width: '140px', height: '112px' }} />
                                        <Typography variant="title2">Drop or Upload a file</Typography>
                                        <Typography variant="bo2" sx={{ display: 'block' }}>
                                            Supported format: .zip, .jar. You can drop a file or click here.
                                        </Typography>
                                    </>
                                )}
                            </Box>
                        )}
                    </Dropzone>
                </Box>
            </RightTray>
        </>
    );
};

export default AddConnectionType;
