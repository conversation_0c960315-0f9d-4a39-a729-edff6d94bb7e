/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import useIntegrationStore, { IConnectionType } from '../store';
import { RightTray, Typography, Box, PlusIcon, IconButton, LoadingOverlay } from 'ui-style';
import { useToggle } from 'ui-common';
import { AttributeType } from '@tripudiotech/admin-styleguide';
import { buildFormItem, buildInitialValue, buildValidationSchema } from '../utils/formBuilder';
import { Formik, Form } from 'formik';
import { useMemo, useRef, useState } from 'react';

const transformJsonSchemaToGlideSchema = (jsonSchema: any) => {
    const attributes = Object.keys(jsonSchema?.properties || {});
    const typeMapping = {
        number: AttributeType.LONG,
        integer: AttributeType.INTEGER,
        string: AttributeType.STRING,
        boolean: AttributeType.BOOLEAN,
        object: 'object',
    };
    return attributes.map((attribute, idx) => ({
        id: `${attribute}_${idx}`,
        name: attribute,
        type: typeMapping[jsonSchema?.properties?.[attribute]?.type] || AttributeType.STRING,
        displayName: attribute,
        nullable: !jsonSchema?.required?.some((field) => field === attribute),
        description: jsonSchema?.properties?.[attribute]?.description,
    }));
};

const formatValues = (attributes, values: any) => {
    let properties = {};
    attributes.forEach((attribute, idx) => {
        properties[attribute.name] = [AttributeType.LONG, AttributeType.INTEGER].includes(attribute.type)
            ? Number(values[attribute.name])
            : attribute.type === AttributeType.BOOLEAN
            ? Boolean(values[attribute.name])
            : values[attribute.name];
    });
    return properties;
};

const DEFAULT_ATTRIBUTES = [
    {
        id: 'name-att',
        name: 'name',
        displayName: 'Connection Name',
        nullable: false,
        type: AttributeType.STRING,
    },
    {
        id: 'desc-att',
        name: 'description',
        displayName: 'Connection Description',
        nullable: false,
        type: AttributeType.STRING,
    },
];

const AddConnection = ({ connectionType }: { connectionType: IConnectionType }) => {
    const [open, openToggle] = useToggle();
    const addConnection = useIntegrationStore((state) => state.addConnection);
    const formRef = useRef(null);
    const [loading, setLoading] = useState(false);
    const handleSubmit = async (values) => {
        setLoading(true);
        const success = await addConnection(connectionType.id, {
            name: values.name,
            description: values.name,
            properties: formatValues(attributes, values),
        });
        setLoading(false);
        success && openToggle.close();
    };
    const attributes = useMemo(
        () => (connectionType ? transformJsonSchemaToGlideSchema(connectionType.schema) : []),
        [connectionType]
    );
    const [initialValues, validationSchema] = useMemo(() => {
        return [buildInitialValue(attributes), buildValidationSchema(attributes)];
    }, [attributes]);

    return (
        <>
            {loading && <LoadingOverlay />}
            <IconButton size="small" color="primary" onClick={openToggle.open}>
                <PlusIcon />
            </IconButton>
            <RightTray
                title={'Add Connection'}
                componentName={'add-connection'}
                open={open}
                onClose={openToggle.close}
                disableCloseOnClickOutside
                confirmText="Apply"
                onConfirm={() => formRef.current.submitForm()}
            >
                <Box
                    sx={{
                        p: '24px',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '8px',
                        overflow: 'auto',
                        '& .generalInfo': {
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '12px',
                            padding: '12px 0',
                        },
                    }}
                >
                    <Typography
                        sx={{ color: (theme) => theme.palette.glide.info }}
                        variant="label2-med"
                        className="sectionLabel"
                    >
                        Basic Information
                    </Typography>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleSubmit(values);
                            setSubmitting(false);
                        }}
                        innerRef={formRef}
                    >
                        {({ values, errors, setFieldValue }) => (
                            <Form className="generalInfo">
                                {DEFAULT_ATTRIBUTES.concat(attributes).map((attribute) =>
                                    buildFormItem(attribute, setFieldValue, undefined, values)
                                )}
                            </Form>
                        )}
                    </Formik>
                </Box>
            </RightTray>
        </>
    );
};

export default AddConnection;
