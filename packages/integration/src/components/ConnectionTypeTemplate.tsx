/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Button, Typography, PlusIcon, MainTooltip } from 'ui-style';
import useIntegrationStore, { IConnectionType } from '../store';
const ConnectionTypeTemplate = ({ connectionTypeTemplate }: { connectionTypeTemplate: IConnectionType }) => {
    const [isInstalled, installConnectionTypeFromTemplate] = useIntegrationStore((state) => [
        !!state.connectionTypeMap[connectionTypeTemplate.name],
        state.installConnectionTypeFromTemplate,
    ]);
    return (
        <Box
            sx={{
                width: '160px',
                background: (theme) => theme.palette.glide.background.normal.blue2,
                borderRadius: '4px',
                boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px',
                '& .connection-icon': {
                    width: '80px',
                    height: '80px',
                    objectFit: 'center',
                },
                p: '16px',
                height: '100%',
                textAlign: 'center',
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <img className="connection-icon" src={`data:image/jpeg;base64,${connectionTypeTemplate.icon}`} />
            </Box>
            <Box
                sx={{
                    flexGrow: 1,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    flexDirection: 'column',
                    gap: '2px',
                }}
            >
                <Typography variant="bo1">{connectionTypeTemplate.name}</Typography>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '4px',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <Box
                        sx={{
                            height: '1px',
                            background: (theme) => theme.palette.glide.stroke.normal.primary,
                            opacity: 0.9,
                            width: '50%',
                        }}
                    />
                    <MainTooltip title={isInstalled ? 'This connection type already installed' : ''}>
                        <span>
                            <Button
                                disabled={isInstalled}
                                size="small"
                                onClick={() => installConnectionTypeFromTemplate(connectionTypeTemplate.id)}
                                endIcon={<PlusIcon />}
                            >
                                Add
                            </Button>
                        </span>
                    </MainTooltip>
                </Box>
            </Box>
        </Box>
    );
};

export default ConnectionTypeTemplate;
