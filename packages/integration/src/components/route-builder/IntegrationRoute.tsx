/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { Children, PropsWithChildren, useMemo } from 'react';
import { NodeProps } from 'reactflow';
import {
    Box,
    ContentHeader,
    FixedHeightContainer,
    AnimatedPage,
    LoadingOverlay,
    tableStyles,
    Typography,
    SchemaField,
    Loading,
    tableIcons,
    IconButton,
    DeleteIcon,
    MinusIcon,
    Button,
    EditIcon,
    PlusIcon,
    Menu,
    MenuItem,
    TextField,
    styled,
    Select,
    Divider,
    FormControl,
    InputLabel,
} from 'ui-style';
import { IXmlElement } from '../../types/integration';
import { CodeBlock, CopyBlock } from 'react-code-blocks';
import { Controller, useController, useFieldArray, useFormContext } from 'react-hook-form';
import useIntegrationStore from '../../store';
import get from 'lodash/get';
const ExpressionTypeLabel = {
    constant: 'Constant',
    simple: 'Simple Expression',
};

const SelectExpressionType = ({ field }) => {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (e) => {
        setAnchorEl(e.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };
    const onSelect = (value) => {
        return () => {
            field.onChange({
                target: {
                    name: field.name,
                    value,
                },
            });
            field.onBlur();
            handleClose();
        };
    };
    return (
        <Box sx={{ width: '100%' }}>
            <Box sx={{ '& input': { cursor: 'pointer' }, width: '100%' }}>
                <Typography variant="label2-med">Expression Type</Typography>
                <TextField
                    fullWidth
                    focused={open}
                    onClick={handleClick}
                    InputProps={{ readOnly: true }}
                    value={ExpressionTypeLabel[field.value]}
                />
            </Box>
            <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
            >
                <MenuItem onClick={onSelect('constant')}>Constant</MenuItem>
                <MenuItem onClick={onSelect('simple')}>Simple Expression</MenuItem>
            </Menu>
        </Box>
    );
};

const AddEIP = ({ append }) => {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    const onNewEndpoint = () => {
        append({
            attributes: {
                uri: '<Your New Endpoint>',
            },
            children: [],
            name: 'from',
            value: '',
        });
        handleClose();
    };

    const onToEndpoint = () => {
        append({
            name: 'to',
            attributes: {
                uri: '<To Endpoint>',
            },
            value: '',
            children: [],
        });
        handleClose();
    };

    const onToDynamicEndpoint = () => {
        append({
            name: 'toD',
            attributes: {
                uri: '<To Endpoint>',
            },
            value: '',
            children: [],
        });
        handleClose();
    };

    const onSetHeader = () => {
        append({
            name: 'setHeader',
            value: '',
            children: [
                {
                    name: 'constant',
                    value: '<Your Header Value>',
                    children: [],
                    attributes: {},
                },
            ],
            attributes: {
                name: '<Your Header name>',
            },
        });
        handleClose();
    };

    const onSetBody = () => {
        append({
            name: 'setBody',
            value: '',
            attributes: {},
            children: [
                {
                    attributes: {},
                    children: [],
                    name: 'simple',
                    value: '',
                },
            ],
        });
        handleClose();
    };

    const onSetProperty = () => {
        append({
            name: 'setProperty',
            value: '',
            children: [
                {
                    name: 'constant',
                    value: '<Your Header Value>',
                    children: [],
                    attributes: {},
                },
            ],
            attributes: {
                name: '<Your Header name>',
            },
        });
        handleClose();
    };
    return (
        <div>
            <Button
                id="basic-button"
                size="small"
                variant="contained"
                color="secondary"
                aria-controls={open ? 'basic-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={handleClick}
                endIcon={<PlusIcon />}
            >
                Add
            </Button>
            <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
            >
                <MenuItem onClick={onNewEndpoint}>New Endpoint</MenuItem>
                <MenuItem onClick={onToEndpoint}>To Endpoint</MenuItem>
                <MenuItem onClick={onToDynamicEndpoint}>To Dynamic Endpoint</MenuItem>
                <Divider />
                <Typography sx={{ ml: '16px' }} variant="bo2">
                    Data Transformation
                </Typography>
                <MenuItem onClick={onSetBody}>Set Body</MenuItem>
                <MenuItem onClick={onSetHeader}>Set Header</MenuItem>
                <MenuItem onClick={onSetProperty}>Set Exchange Property</MenuItem>
            </Menu>
        </div>
    );
};

const ElementContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    alignItems: 'center',
    justifyContent: 'center',
    wordBreak: 'break-word',
    padding: '8px',
    border: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    width: '100%',
    borderRadius: '4px',
}));

const LogElement = (props) => {
    const { register } = useFormContext();
    return (
        <ElementContainer>
            <Box
                sx={{
                    width: '100%',
                    position: 'relative',
                    justifyContent: 'center',
                    alignItems: 'center',
                    textAlign: 'center',
                    display: 'flex',
                }}
            >
                <Typography variant="label1-med">Log</Typography>
                {props.isEditing && (
                    <IconButton
                        sx={{ position: 'absolute', right: '8px', top: '50%', transform: 'translateY(-50%)' }}
                        onClick={() => props.remove(props.index)}
                        color="error"
                        size="small"
                    >
                        <DeleteIcon />
                    </IconButton>
                )}
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%' }}>
                <TextField
                    InputProps={{ readOnly: !props.isEditing }}
                    label="Message"
                    {...register(`${props.field.name}.attributes.message`)}
                />
            </Box>
        </ElementContainer>
    );
};
const SetPropertyElement = ({ label = 'Set Exchange Property', remove, index, ...props }) => {
    const data = props.field.value;
    const { register, control, formState } = useFormContext();
    return (
        <ElementContainer>
            <Box
                sx={{
                    width: '100%',
                    position: 'relative',
                    justifyContent: 'center',
                    alignItems: 'center',
                    textAlign: 'center',
                    display: 'flex',
                }}
            >
                <Typography variant="label1-med">{label}</Typography>
                {props.isEditing && (
                    <IconButton
                        sx={{ position: 'absolute', right: '8px', top: '50%', transform: 'translateY(-50%)' }}
                        onClick={() => remove(index)}
                        color="error"
                        size="small"
                    >
                        <DeleteIcon />
                    </IconButton>
                )}
            </Box>
            <Controller
                name={`${props.field.name}.children.0.name`}
                control={control}
                render={({ field }) => <SelectExpressionType field={field} />}
            />
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%' }}>
                <Typography variant="label2-med">Name</Typography>
                <TextField
                    InputProps={{ readOnly: !props.isEditing }}
                    {...register(`${props.field.name}.attributes.name`, { required: REQUIRED_MSG })}
                    required
                    error={Boolean(get(formState.errors, `${props.field.name}.attributes.name.message`))}
                    helperText={get(formState.errors, `${props.field.name}.attributes.name.message`)}
                />
                <Typography variant="label2-med">Value</Typography>
                {data.children.map((child, idx) => (
                    <TextField
                        multiline
                        InputProps={{ readOnly: !props.isEditing }}
                        minRows={3}
                        maxRows={5}
                        required
                        label="Value"
                        key={`expression-${child.name}-${idx}`}
                        {...register(`${props.field.name}.children.${idx}.value`, { required: REQUIRED_MSG })}
                        error={Boolean(get(formState.errors, `${props.field.name}.children.${idx}.value.message`))}
                        helperText={get(formState.errors, `${props.field.name}.children.${idx}.value.message`)}
                    />
                ))}
            </Box>
        </ElementContainer>
    );
};
const SetBodyElement = ({ label = 'Set Body', remove, index, ...props }) => {
    const data = props.field.value;
    const { register, control, formState } = useFormContext();
    return (
        <ElementContainer>
            <Box
                sx={{
                    width: '100%',
                    position: 'relative',
                    justifyContent: 'center',
                    alignItems: 'center',
                    textAlign: 'center',
                    display: 'flex',
                }}
            >
                <Typography variant="label1-med">{label}</Typography>
                {props.isEditing && (
                    <IconButton
                        sx={{ position: 'absolute', right: '8px', top: '50%', transform: 'translateY(-50%)' }}
                        onClick={() => remove(index)}
                        color="error"
                        size="small"
                    >
                        <DeleteIcon />
                    </IconButton>
                )}
            </Box>
            <Controller
                name={`${props.field.name}.children.0.name`}
                control={control}
                render={({ field }) => <SelectExpressionType field={field} />}
            />
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%' }}>
                <Typography variant="label2-med">Value</Typography>
                {data.children.map((child, idx) => (
                    <TextField
                        InputProps={{ readOnly: !props.isEditing }}
                        multiline
                        required
                        minRows={3}
                        maxRows={5}
                        label="Value"
                        key={`expression-${child.name}-${idx}`}
                        {...register(`${props.field.name}.children.0.value`, { required: REQUIRED_MSG })}
                        error={Boolean(get(formState.errors, `${props.field.name}.children.0.value.message`))}
                        helperText={get(formState.errors, `${props.field.name}.children.0.value.message`)}
                    />
                ))}
            </Box>
        </ElementContainer>
    );
};
const SetHeader = (props) => {
    return <SetPropertyElement {...props} label="Set Header" />;
};
const EndpointElement = (props) => {
    const data = props.field.value;
    const { register, formState } = useFormContext();
    // TODO: Support query parameters builder
    // const [uri, queries] = parseFromElement(data);

    return (
        <ElementContainer>
            <Box
                sx={{
                    width: '100%',
                    position: 'relative',
                    justifyContent: 'center',
                    alignItems: 'center',
                    textAlign: 'center',
                    display: 'flex',
                }}
            >
                <Typography variant="label1-med">
                    {data.name === 'from' ? 'From' : data.name === 'toD' ? 'To Dynamic' : 'To'} Endpoint
                </Typography>
                {props.isEditing && (
                    <IconButton
                        sx={{ position: 'absolute', right: '8px', top: '50%', transform: 'translateY(-50%)' }}
                        onClick={() => props.remove(props.index)}
                        color="error"
                        size="small"
                    >
                        <DeleteIcon />
                    </IconButton>
                )}
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%' }}>
                <Typography variant="label2-med">Endpoint URI</Typography>
                <TextField
                    InputProps={{ readOnly: !props.isEditing }}
                    label="URI"
                    required
                    {...register(`${props.field.name}.attributes.uri`, { required: REQUIRED_MSG })}
                    error={Boolean(get(formState.errors, `${props.field.name}.attributes.uri.message`))}
                    helperText={get(formState.errors, `${props.field.name}.attributes.uri.message`)}
                />
                {/* {queries.length > 0 && (
                    <>
                        <Typography variant="label2-med">Query Parameters</Typography>
                        {queries.map((query) => (
                            <TextField label={query.key} value={query.value} key={query.key} fullWidth />
                        ))}
                    </>
                )} */}
            </Box>
        </ElementContainer>
    );
};

const EIPComponentMap = {
    from: EndpointElement,
    to: EndpointElement,
    toD: EndpointElement,
    log: LogElement,
    setProperty: SetPropertyElement,
    setHeader: SetHeader,
    setBody: SetBodyElement,
};

const EIPBuilder = (props: any) => {
    const data = props.field.value;
    const Component = EIPComponentMap[data.name];
    return Component ? (
        <Component {...props} />
    ) : (
        <div>
            <Typography variant="bo2">Unsupported Operation</Typography>
        </div>
    );
};

const REQUIRED_MSG = 'This attribute is required';
const IntegrationRoute = (props: any) => {
    const { data } = props;
    const { control, setValue, getValues } = useFormContext();
    const isEditing = useIntegrationStore((state) => state.isEditingRoute);
    const readOnly = !isEditing;
    const formControl = useController({
        control,
        name: `data.${data.index}.data.attributes.id`,
        rules: { required: REQUIRED_MSG },
    });
    const { fields, append, remove } = useFieldArray({ control, name: `data.${data.index}.data.children` });
    const routeFormName = useMemo(() => `data.${data.index}.data`, [data]);

    return (
        <Box
            sx={{
                width: '320px',
                minWidth: '320px',
                height: 'max-content',
                border: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                borderRadius: '8px',
                '& .header': {
                    padding: '16px',
                    textAlign: 'center',
                    borderBottom: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                    wordBreak: 'break-word',
                    width: '100%',
                    display: 'flex',
                    gap: '8px',
                },
                '& .body': {
                    padding: '24px 16px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '32px',
                    alignItems: 'center',
                    justifyContent: 'center',
                },
                '& .btn': {
                    maxWidth: '80px',
                },
            }}
        >
            <Box className="header">
                <TextField
                    InputProps={{ readOnly: readOnly }}
                    required
                    fullWidth
                    label="Route ID"
                    {...formControl.field}
                    error={Boolean(get(formControl.formState.errors, `${formControl.field.name}.message`))}
                    helperText={get(formControl.formState.errors, `${formControl.field.name}.message`)}
                />
                {isEditing && (
                    <IconButton
                        onClick={() => {
                            const newRoutes = [...getValues().data]
                                .filter((_, i) => i !== data.index)
                                .map((route, idx) => ({
                                    ...route,
                                    data: { ...route.data, index: idx },
                                    position: { x: idx * (320 + 120), y: 200 },
                                }));
                            setValue('data', newRoutes, { shouldValidate: true, shouldTouch: true });
                        }}
                        color="error"
                        size="small"
                    >
                        <DeleteIcon />
                    </IconButton>
                )}
            </Box>
            <Box className="body">
                {fields.map((field, index) => (
                    <Controller
                        control={control}
                        render={(props) => (
                            <EIPBuilder isEditing={isEditing} index={index} remove={remove} {...props} />
                        )}
                        name={`${routeFormName}.children.${index}`}
                        key={field.id}
                    />
                ))}
                {isEditing && <AddEIP append={append} />}
            </Box>
        </Box>
    );
};

export default IntegrationRoute;
