/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { SvgIcon, SvgIconProps } from 'ui-style';

export const ComponentIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon {...props} width="251" height="200" viewBox="0 0 251 200" fill="none">
            <rect x="0.5" width="250" height="200" fill="white" />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M63.5 134H154.5C155.015 134 155.517 133.944 156 133.839C156.483 133.944 156.985 134 157.5 134H209.5C213.366 134 216.5 130.866 216.5 127C216.5 123.134 213.366 120 209.5 120H203.5C199.634 120 196.5 116.866 196.5 113C196.5 109.134 199.634 106 203.5 106H222.5C226.366 106 229.5 102.866 229.5 99C229.5 95.134 226.366 92 222.5 92H200.5C204.366 92 207.5 88.866 207.5 85C207.5 81.134 204.366 78 200.5 78H136.5C140.366 78 143.5 74.866 143.5 71C143.5 67.134 140.366 64 136.5 64H79.5C75.634 64 72.5 67.134 72.5 71C72.5 74.866 75.634 78 79.5 78H39.5C35.634 78 32.5 81.134 32.5 85C32.5 88.866 35.634 92 39.5 92H64.5C68.366 92 71.5 95.134 71.5 99C71.5 102.866 68.366 106 64.5 106H24.5C20.634 106 17.5 109.134 17.5 113C17.5 116.866 20.634 120 24.5 120H63.5C59.634 120 56.5 123.134 56.5 127C56.5 130.866 59.634 134 63.5 134ZM226.5 134C230.366 134 233.5 130.866 233.5 127C233.5 123.134 230.366 120 226.5 120C222.634 120 219.5 123.134 219.5 127C219.5 130.866 222.634 134 226.5 134Z"
                fill="#F3F7FF"
            />
            <path d="M125.5 62L171.5 89L125.5 115.5L79.5 88.5L125.5 62Z" fill="white" />
            <path
                d="M125.5 115.5L79.5 88.5L125.5 62L171.5 89"
                stroke="#1F64E7"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="white"
            />
            <path d="M171.5 143.5L125.5 170V115.5L134.5 110.315L148.5 102.25L171.5 89V143.5Z" fill="white" />
            <path
                d="M134.5 110.315L125.5 115.5L125.5 170L171.5 143.5V89L148.5 102.25"
                stroke="#1F64E7"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="white"
            />
            <path
                d="M138.5 108L145 104.5"
                stroke="#1F64E7"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M125.5 170L79.5 143.5V88.5L125.5 115.5V170Z"
                fill="white"
                stroke="#1F64E7"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path d="M120.5 162L84.5 140.865V97L120.5 118.534V162Z" fill="#E8F0FE" />
            <path
                d="M98.5 34.3027L109.6 46.7559M150.6 34.3027L139.5 46.7559L150.6 34.3027ZM124.5 30V46.7559V30Z"
                stroke="#75A4FE"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </SvgIcon>
    );
};

export default ComponentIcon;
