/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, ContentHeader, FixedHeightContainer, AnimatedPage, LoadingOverlay, TabGroup } from 'ui-style';
import { INTEGRATION_TABS } from '../constants';
import get from 'lodash/get';
import { Outlet, useMatch } from 'react-router-dom';
import AddConnectionType from './AddConnectionType';
import useIntegrationStore from '../store';

const Actions = () => {
    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <AddConnectionType />
        </Box>
    );
};

const MainLayout = () => {
    const [isLoading, isLoadingConnectionTypes] = useIntegrationStore((state) => [
        state.isLoadingConnectionTypes,
        state.isLoadingConnectionTypes,
    ]);
    const matched = useMatch('integration/:view/*');
    const view = get(matched, ['params', 'view'], '');
    return (
        <AnimatedPage>
            {(isLoading || isLoadingConnectionTypes) && <LoadingOverlay />}
            <FixedHeightContainer>
                <ContentHeader title="Integration" actionsRenderer={Actions} />
                <Box sx={{ px: '16px' }}>
                    <TabGroup
                        value={view}
                        variant="scrollable"
                        scrollButtons="auto"
                        allowScrollButtonsMobile
                        aria-label="Integration navigation tabs"
                        items={INTEGRATION_TABS}
                        size="small"
                        style={{
                            marginTop: '8px',
                        }}
                    />
                </Box>
                <Outlet />
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default MainLayout;
