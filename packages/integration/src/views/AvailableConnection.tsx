/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import useIntegrationStore from '../store';
import { useEffect } from 'react';
import { Box, Typography, Loading } from 'ui-style';
import ConnectionTypeTemplate from '../components/ConnectionTypeTemplate';

const AvailableConnections = () => {
    const {
        getConnectionTypeTemplates,
        isLoaded,
        getConnectionTypes,
        isLoadedConnectionTypeTemplates,
        connectionTypeTemplates,
    } = useIntegrationStore();
    useEffect(() => {
        getConnectionTypeTemplates();
        getConnectionTypes();
    }, [getConnectionTypeTemplates]);

    return isLoaded && isLoadedConnectionTypeTemplates ? (
        <Box sx={{ m: '16px' }}>
            {connectionTypeTemplates.length > 0 ? (
                <>
                    <Typography variant="bo2">
                        You can create and manage data connections for the following data sources and use them in your
                        integrations
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: '24px', mt: '16px' }}>
                        {connectionTypeTemplates.map((connectionTypeTemplate) => (
                            <ConnectionTypeTemplate
                                key={connectionTypeTemplate.id}
                                connectionTypeTemplate={connectionTypeTemplate}
                            />
                        ))}
                    </Box>
                </>
            ) : (
                <Typography variant="bo2">No template found</Typography>
            )}
        </Box>
    ) : (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '70%' }}>
            <Loading />
        </Box>
    );
};

export default AvailableConnections;
