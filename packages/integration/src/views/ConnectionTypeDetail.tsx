/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useEffect, useMemo, useRef, useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import useIntegrationStore from '../store';
import {
    Box,
    ContentHeader,
    FixedHeightContainer,
    AnimatedPage,
    LoadingOverlay,
    tableStyles,
    Typography,
    SchemaField,
    Loading,
    tableIcons,
    IconButton,
    DeleteIcon,
    MinusIcon,
    Button,
} from 'ui-style';
import { formatDateTime } from 'ui-common';
import capitalize from 'lodash/capitalize';
import { AgGridReact } from '@ag-grid-community/react';
import { useDialog } from '@tripudiotech/admin-caching-store';
import AddConnection from '../components/AddConnection';
import EditConnection from '../components/EditConnection';

const Actions = () => {
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const { connectionTypeId } = useParams();
    const navigate = useNavigate();
    const uninstallConnectionType = useIntegrationStore((state) => state.uninstallConnectionType);

    const handleDelete = async () => {
        onOpenDialog(
            'Delete Connection Type',
            `Do you really want to delete this connection type?`,
            async () => {
                await uninstallConnectionType(connectionTypeId);
                navigate('/integration');
            },
            'error'
        );
    };
    return (
        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
            <Button onClick={handleDelete} color="error" size="small" endIcon={<DeleteIcon />}>
                Delete
            </Button>
        </Box>
    );
};

const ConnectionTypeDetail = () => {
    const { connectionTypeId } = useParams();
    const gridRef = useRef<AgGridReact>();
    const [connectionTypeDetail, getConnectionTypeDetail, deleteConnections] = useIntegrationStore((state) => [
        state.connectionTypeDetails[connectionTypeId],
        state.getConnectionTypeDetail,
        state.deleteConnections,
    ]);
    const [loading, setLoading] = useState(false);
    const onOpenDialog = useDialog((state) => state.onOpenDialog);

    const [selectedRows, setSelectedRows] = useState([]);
    const [editingRow, setEditingRow] = useState();
    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'clientSide',
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Connection Name',
                    checkboxSelection: true,
                    minWidth: 250,
                    cellRenderer: ({ value, data }) => {
                        return (
                            <Link to={`/integration/connection-type/${connectionTypeId}/connection/${data.id}`}>
                                {value}
                            </Link>
                        );
                    },
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    minWidth: 250,
                },
            ].concat(
                connectionTypeDetail?.details
                    ? Object.keys(connectionTypeDetail?.details?.schema?.properties).map((property) => ({
                          field: `properties.${property}`,
                          headerName: capitalize(property),
                          valueGetter: (v) => {
                              if (
                                  ['secret', 'password', 'private', 'key'].some((k) =>
                                      property?.toLowerCase()?.includes(k)
                                  )
                              ) {
                                  return '******';
                              }
                              return v?.properties?.[property];
                          },
                      }))
                    : ([] as any)
            ),
            icons: tableIcons,
            pagination: false,
            rowSelection: 'multiple',
            onSelectionChanged: () => setSelectedRows(gridRef.current.api.getSelectedRows()),
        };
    }, [connectionTypeDetail]);

    const handleDelete = async () => {
        setLoading(true);
        const success = await deleteConnections(
            connectionTypeId,
            selectedRows.map((row) => row.id)
        );
        success && setLoading(false);
    };

    const onDelete = () => {
        onOpenDialog('Delete Connection', 'Do you want to delete selected connections?', handleDelete, 'error');
    };
    useEffect(() => {
        getConnectionTypeDetail(connectionTypeId);
    }, [connectionTypeId]);

    return connectionTypeDetail ? (
        <AnimatedPage>
            <FixedHeightContainer>
                {loading && <LoadingOverlay />}
                <ContentHeader
                    title={`Integration / ${connectionTypeDetail?.details?.name}`}
                    actionsRenderer={Actions}
                />
                <Box
                    sx={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        '& .generalInfo': {
                            width: '350px',
                            padding: '16px',
                            borderRight: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                            display: 'flex',
                            position: 'relative',
                            flexDirection: 'column',
                        },
                    }}
                >
                    <div className="generalInfo">
                        <Typography variant="ol1" sx={{ mb: '8px' }}>
                            General Information
                        </Typography>
                        <SchemaField label="ID" value={connectionTypeDetail?.details?.id} />
                        <SchemaField label="Name" value={connectionTypeDetail?.details?.name} />
                        <SchemaField
                            label="Created Date"
                            value={formatDateTime(connectionTypeDetail?.details.createdAt)}
                        />
                        <SchemaField
                            label="Logo"
                            value={
                                <Box
                                    sx={{
                                        '& .connection-icon': {
                                            width: '32px',
                                            height: '32px',
                                            objectFit: 'center',
                                        },
                                    }}
                                >
                                    <img
                                        className="connection-icon"
                                        src={`data:image/jpeg;base64,${connectionTypeDetail.details?.icon}`}
                                    />
                                </Box>
                            }
                        />
                    </div>
                    <Box sx={{ p: '16px', ...tableStyles, width: '100%' }}>
                        <Typography variant="ol1">Connections</Typography>
                        <Box sx={{ marginTop: '8px', display: 'flex', gap: '4px' }}>
                            <AddConnection connectionType={connectionTypeDetail?.details} />
                            <IconButton
                                onClick={onDelete}
                                size="small"
                                color="primary"
                                disabled={selectedRows.length === 0}
                            >
                                <MinusIcon />
                            </IconButton>
                        </Box>
                        <div className="connections-table" style={{ width: '100%', height: '100%', marginTop: '8px' }}>
                            <AgGridReact
                                className="ag-theme-alpine"
                                {...gridOptions}
                                rowData={connectionTypeDetail?.connections}
                                ref={gridRef}
                            ></AgGridReact>
                        </div>
                    </Box>
                </Box>
                {editingRow && (
                    <EditConnection
                        connection={editingRow}
                        connectionType={connectionTypeDetail.details}
                        open={Boolean(editingRow)}
                        openToggle={{ open: () => {}, close: () => setEditingRow(null) }}
                    />
                )}
            </FixedHeightContainer>
        </AnimatedPage>
    ) : (
        <LoadingOverlay />
    );
};

export default ConnectionTypeDetail;
