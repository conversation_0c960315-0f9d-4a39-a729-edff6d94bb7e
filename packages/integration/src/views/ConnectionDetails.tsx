/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import useIntegrationStore from '../store';
import {
    Box,
    ContentHeader,
    FixedHeightContainer,
    AnimatedPage,
    LoadingOverlay,
    tableStyles,
    Typography,
    SchemaField,
    Loading,
    tableIcons,
    IconButton,
    DeleteIcon,
    MinusIcon,
    Button,
    EditIcon,
    SaveIcon,
    BaseAccordion,
    AccordionSummary,
    AccordionDetails,
    ExpandMoreIcon,
    CloseIcon,
    PlusIcon,
    TextField,
    SendIcon,
} from 'ui-style';
import get from 'lodash/get';
import XMLParser from 'react-xml-parser';
import IntegrationRoute from '../components/route-builder/IntegrationRoute';
import ReactFlow, { Controls, MiniMap, useReactFlow } from 'reactflow';
import { IXmlElement } from '../types/integration';
import { FormProvider, useController, useFieldArray, useForm } from 'react-hook-form';
import { AgGridReact } from '@ag-grid-community/react';
import { useDialog } from '@tripudiotech/admin-caching-store';
import { notifyError } from '@tripudiotech/admin-styleguide';

const nodeTypes = { integrationRoute: IntegrationRoute };

type IntegrationRouteNode = {
    id: string;
    position: { x: number; y: number };
    type: string;
    data: IXmlElement;
};

type FormProps = {
    data: IntegrationRouteNode[];
};

const DEFAULT_TEMPLATE = `<routes xmlns="http://camel.apache.org/schema/spring">
<route id="">
  <from uri="" />
</route>
</routes>
`;

const ConnectionDetails = () => {
    const { connectionId, connectionTypeId } = useParams();
    const [selectedRow, setSelectedRow] = useState(null);
    const { fitView } = useReactFlow();
    const [loading, setLoading] = useState(false);
    const [
        connectionDetail,
        connectionTypeDetail,
        getConnectionDetail,
        getConnectionTypeDetail,
        isEditing,
        setIsEditing,
        updateRoute,
        deleteRoute,
        isCreating,
        setCreating,
        importRouter,
        reloadRoute,
    ] = useIntegrationStore((state) => [
        state.connectionDetails[connectionId],
        state.connectionTypeDetails[connectionTypeId],
        state.getConnectionDetail,
        state.getConnectionTypeDetail,
        state.isEditingRoute,
        state.setEditingRoute,
        state.updateRoute,
        state.deleteRoute,
        state.isCreatingRoute,
        state.setCreatingRoute,
        state.importRouter,
        state.reloadRoute,
    ]);
    const gridRef = useRef<AgGridReact>();
    const onOpenDialog = useDialog((state) => state.onOpenDialog);
    const [newRouteName, setNewRouteName] = useState('');
    const nodes = useMemo(() => {
        if (selectedRow && selectedRow?.template) {
            const xml = new XMLParser().parseFromString(selectedRow.template);
            return xml.children.map((node, idx) => ({
                id: `route-${idx}`,
                type: 'integrationRoute',
                data: { ...node, index: idx },
                position: { x: idx * (320 + 120), y: 200 },
            }));
        }
        return [];
    }, [selectedRow]);

    const formMethods = useForm<FormProps>({
        mode: 'onBlur',
        defaultValues: { data: nodes },
    });
    const {
        control,
        getValues,
        reset,
        formState: { errors },
    } = formMethods;
    const fieldArray = useFieldArray({ control, name: 'data' });
    useEffect(() => {
        reset({ data: nodes });
        setTimeout(() => {
            fitView({
                duration: 500,
                padding: 0.2,
                minZoom: 0.4,
            });
        }, 250);
    }, [nodes]);

    useEffect(() => {
        getConnectionDetail(connectionId);
    }, [connectionId]);

    const onEdit = () => {
        setIsEditing(true);
    };
    const onSave = async () => {
        formMethods.handleSubmit(
            async () => {
                setIsEditing(false);
                setLoading(true);
                const xmlString = objToXml(formMethods);
                await updateRoute(selectedRow.id, xmlString);
                getConnectionDetail(connectionId);
                setLoading(false);
            },
            (errors, e) => {
                notifyError('Some attributes are missing, please check the form validation');
            }
        )();
    };
    const onCancel = () => {
        setIsEditing(false);
        formMethods.reset();
    };
    const onDelete = () => {
        onOpenDialog(
            'Delete Connection Route',
            `Do you really want to delete this connection route?`,
            async () => {
                setLoading(true);
                await deleteRoute(selectedRow.id);
                getConnectionDetail(connectionId);
                setSelectedRow(null);
                setLoading(false);
            },
            'error'
        );
    };

    useEffect(() => {
        getConnectionTypeDetail(connectionTypeId);
    }, [connectionTypeId]);

    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'clientSide',
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Route Name',
                    cellRenderer: ({ value, data }) => {
                        return (
                            <Typography
                                variant="bo2"
                                sx={{ color: (theme) => theme.palette.glide.info, cursor: 'pointer' }}
                            >
                                {value}
                            </Typography>
                        );
                    },
                },
            ],
            icons: tableIcons,
            pagination: false,
            rowSelection: 'single',
            onSelectionChanged: () => {
                setSelectedRow(gridRef.current.api.getSelectedRows()?.[0]);
                setCreating(false);
                setIsEditing(false);
            },
        };
    }, []);

    const onNewRoute = () => {
        setIsEditing(true);
        setCreating(true);
        setSelectedRow({
            template: DEFAULT_TEMPLATE,
        });
    };

    const handleCreate = () => {
        formMethods.handleSubmit(
            async (data) => {
                setLoading(true);
                const xmlString = objToXml(formMethods);

                const res = await importRouter(connectionId, newRouteName, xmlString);
                setLoading(false);
                setIsEditing(false);
                setCreating(false);
                setSelectedRow(res);
            },
            (errors, e) => {
                notifyError('Some attributes are missing, please check the form validation');
            }
        )();
    };
    const onCancelCreate = () => {
        setIsEditing(false);
        setCreating(false);
        setSelectedRow(null);
    };

    const onApply = async () => {
        setLoading(true);
        await reloadRoute();
        setLoading(false);
    };
    return connectionDetail && connectionTypeDetail ? (
        <AnimatedPage>
            {loading && <LoadingOverlay />}
            <FixedHeightContainer>
                <ContentHeader
                    title={`Connection / ${connectionDetail?.name}`}
                    actionsRenderer={() => (
                        <Box sx={{ display: 'flex', gap: '4px', ml: 'auto' }}>
                            {!isCreating && (
                                <Button
                                    onClick={onNewRoute}
                                    variant="contained"
                                    color="secondary"
                                    size="small"
                                    endIcon={<PlusIcon />}
                                >
                                    New Route
                                </Button>
                            )}
                            <Button
                                onClick={onApply}
                                variant="contained"
                                color="secondary"
                                size="small"
                                endIcon={<SendIcon />}
                            >
                                Apply Route
                            </Button>
                        </Box>
                    )}
                />
                <FormProvider {...formMethods}>
                    <Box
                        sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            '& .generalInfo': {
                                width: '350px',
                                padding: '16px',
                                borderRight: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                display: 'flex',
                                position: 'relative',
                                flexDirection: 'column',
                                gap: '16px',
                            },
                            '& .MuiGrid-container': {
                                '& :nth-of-type(2)': {
                                    wordBreak: 'break-word',
                                },
                            },
                            '& .summary': {
                                position: 'sticky',
                                top: 0,
                                zIndex: 102,
                            },
                            '& .diagram-container': {
                                flexGrow: 1,
                                position: 'relative',
                                backgroundColor: (theme) => theme.palette.glide.background.normal.inversePrimary,
                            },
                            ...tableStyles,
                        }}
                    >
                        <div className="generalInfo">
                            <BaseAccordion
                                defaultExpanded={false}
                                TransitionProps={{ unmountOnExit: true }}
                                sx={{ '&.Mui-expanded': { margin: 0 } }}
                            >
                                <AccordionSummary
                                    className="summary"
                                    expandIcon={
                                        <div className="allowPointer sectionSummary">
                                            <ExpandMoreIcon sx={{ width: '16px', height: '16px', mt: '5px' }} />
                                        </div>
                                    }
                                >
                                    <Typography variant="ol1" sx={{ ml: '4px' }}>
                                        General Information
                                    </Typography>
                                </AccordionSummary>
                                <AccordionDetails className="accordionDetails">
                                    {Object.keys(get(connectionTypeDetail, 'details.schema.properties', {})).map(
                                        (key) => (
                                            <SchemaField
                                                breakline
                                                valueComponent={'pre'}
                                                key={key}
                                                label={key}
                                                value={
                                                    ['secret', 'password', 'private', 'key'].some((k) =>
                                                        key.includes(k)
                                                    )
                                                        ? '*****'
                                                        : typeof connectionDetail.properties?.[key] === 'object'
                                                        ? JSON.stringify(connectionDetail.properties?.[key], null, 2)
                                                        : connectionDetail.properties?.[key]
                                                }
                                            />
                                        )
                                    )}
                                </AccordionDetails>
                            </BaseAccordion>
                            <BaseAccordion
                                defaultExpanded
                                TransitionProps={{ unmountOnExit: true }}
                                sx={{ '&.Mui-expanded': { margin: 0 } }}
                            >
                                <AccordionSummary
                                    className="summary"
                                    expandIcon={
                                        <div className="allowPointer sectionSummary">
                                            <ExpandMoreIcon sx={{ width: '16px', height: '16px', mt: '5px' }} />
                                        </div>
                                    }
                                >
                                    <Typography variant="ol1" sx={{ ml: '4px' }}>
                                        Connection Route
                                    </Typography>
                                </AccordionSummary>
                                <AccordionDetails className="accordionDetails">
                                    <Box sx={{ height: '480px' }}>
                                        <div
                                            className="connections-table"
                                            style={{ width: '100%', height: '100%', marginTop: '8px' }}
                                        >
                                            <AgGridReact
                                                className="ag-theme-alpine"
                                                {...gridOptions}
                                                rowData={connectionDetail?.routes}
                                                ref={gridRef}
                                            ></AgGridReact>
                                        </div>
                                    </Box>
                                </AccordionDetails>
                            </BaseAccordion>
                        </div>
                        <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
                            {selectedRow && (
                                <Box
                                    sx={{
                                        padding: '8px 16px',
                                        borderBottom: (theme) =>
                                            `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    {isCreating ? (
                                        <TextField
                                            label="Route Name"
                                            value={newRouteName}
                                            onChange={(e) => setNewRouteName(e.target.value)}
                                            required
                                        />
                                    ) : (
                                        <Typography variant="label1-med">{selectedRow?.name}</Typography>
                                    )}
                                    {isCreating ? (
                                        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
                                            <Button
                                                onClick={handleCreate}
                                                variant="contained"
                                                color="secondary"
                                                size="small"
                                                endIcon={<SaveIcon />}
                                                disabled={!newRouteName}
                                            >
                                                Save
                                            </Button>
                                            <Button
                                                onClick={onCancelCreate}
                                                variant="contained"
                                                color="secondary"
                                                size="small"
                                                endIcon={<CloseIcon />}
                                            >
                                                Cancel
                                            </Button>
                                        </Box>
                                    ) : (
                                        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
                                            {isEditing ? (
                                                <>
                                                    <Button
                                                        onClick={onSave}
                                                        variant="contained"
                                                        color="secondary"
                                                        size="small"
                                                        endIcon={<SaveIcon />}
                                                    >
                                                        Save
                                                    </Button>
                                                    <Button
                                                        onClick={onCancel}
                                                        variant="contained"
                                                        color="secondary"
                                                        size="small"
                                                        endIcon={<CloseIcon />}
                                                    >
                                                        Cancel
                                                    </Button>
                                                </>
                                            ) : (
                                                <Button
                                                    onClick={onEdit}
                                                    variant="contained"
                                                    color="secondary"
                                                    size="small"
                                                    endIcon={<EditIcon />}
                                                >
                                                    Edit
                                                </Button>
                                            )}

                                            <Button
                                                onClick={onDelete}
                                                variant="contained"
                                                color="error"
                                                size="small"
                                                endIcon={<DeleteIcon />}
                                            >
                                                Delete
                                            </Button>
                                        </Box>
                                    )}
                                </Box>
                            )}
                            <Box sx={{ p: '16px', width: '100%', flexGrow: 1, position: 'relative' }}>
                                {selectedRow && (
                                    <ReactFlow edges={[]} fitView nodes={getValues().data} nodeTypes={nodeTypes}>
                                        {/* <MiniMap /> */}
                                        <Controls />
                                    </ReactFlow>
                                )}
                            </Box>
                        </Box>
                    </Box>
                </FormProvider>
            </FixedHeightContainer>
        </AnimatedPage>
    ) : (
        <LoadingOverlay />
    );
};

export default ConnectionDetails;
function objToXml(formMethods) {
    const values = formMethods.getValues();
    const xmlObject = {
        name: 'routes',
        value: '',
        children: values.data.map((data) => data.data),
        attributes: { xmlns: 'http://camel.apache.org/schema/spring' },
    };
    const xmlString = new XMLParser().toString(xmlObject);
    return xmlString;
}
