/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import keyBy from 'lodash/keyBy';
import { integrationUrls, fetch } from '@tripudiotech/admin-api';
import { notifyError } from '@tripudiotech/admin-styleguide';

export interface IConnectionType {
    id: string;
    updatedAt: string;
    createdAt: string;
    createdBy: string;
    updatedBy: string;
    schema: Record<string, any>;
    icon: string;
    name: string;
}

export interface IConnection {
    id: string;
    updatedAt: string;
    createdAt: string;
    name: string;
    description: string;
    properties: Record<string, any>;
    routes: any[];
}

export type ConnectionTypeDetails = {
    connections: IConnection[];
    details: IConnectionType;
};

export type CreateConnectionRequest = {
    name: string;
    description: string;
    properties: Record<string, any>;
};

type IntegrationStore = {
    connectionTypes: IConnectionType[];
    connectionTypeMap: Record<string, IConnectionType>;
    connectionTypeTemplates: IConnectionType[];
    connectionTypeDetails: Record<string, ConnectionTypeDetails>;
    connectionDetails: Record<string, IConnection>;
    isLoadingConnectionTypes: boolean;
    isLoadingConnectionTypeTemplates: boolean;
    isLoadedConnectionTypeTemplates: boolean;
    isLoaded: boolean;
    isEditingRoute: boolean;
    isCreatingRoute: boolean;
    setEditingRoute: (isEditingRoute: boolean) => void;
    setCreatingRoute: (isCreatingRoute: boolean) => void;
    getConnectionTypes: () => Promise<any>;
    getConnectionTypeTemplates: () => Promise<any>;
    getConnectionTypeDetail: (connectionTypeId: string) => Promise<any>;
    getConnectionDetail: (connectionId: string) => Promise<any>;
    installConnectionType: (name: string, file: any) => Promise<any>;
    installConnectionTypeFromTemplate: (templateId: string) => Promise<any>;
    deleteConnections: (connectionTypeId: string, connectionIds: string[]) => Promise<any>;
    addConnection: (connectionTypeId: string, connectionRequest: CreateConnectionRequest) => Promise<any>;
    updateConnection: (
        connectionTypeId: string,
        connectionId: string,
        connectionRequest: CreateConnectionRequest
    ) => Promise<any>;
    uninstallConnectionType: (connectionTypeId: string) => Promise<any>;
    importRouter: (connectionId: string, name: string, xml: any) => Promise<any>;
    deleteRoute: (routeId: string) => Promise<any>;
    updateRoute: (routeId: string, xml: string) => Promise<any>;
    reloadRoute: () => Promise<any>;
};

const useIntegrationStore = create<IntegrationStore>((set, get) => ({
    connectionTypes: [],
    connectionTypeMap: {},
    connectionTypeTemplates: [],
    isEditingRoute: false,
    isCreatingRoute: false,
    isLoadingConnectionTypes: false,
    isLoadingConnectionTypeTemplates: false,
    connectionTypeDetails: {},
    connectionDetails: {},
    isLoaded: false,
    isLoadedConnectionTypeTemplates: false,
    reloadRoute: async () => {
        try {
            await fetch({
                ...integrationUrls.reloadRoute,
            });
        } catch (err) {
            notifyError(`An error occurred while reloading route configurations`);
        }
    },
    setEditingRoute: (isEditingRoute: boolean) => {
        set({ isEditingRoute });
    },
    setCreatingRoute: (isCreatingRoute: boolean) => {
        set({ isCreatingRoute });
    },
    getConnectionTypes: async () => {
        const {
            data: { data },
        } = await fetch({
            ...integrationUrls.getConnectionTypes,
        });
        set({
            isLoaded: true,
            connectionTypes: data,
            isLoadingConnectionTypes: false,
            connectionTypeMap: keyBy(data, 'name'),
        });
    },
    getConnectionTypeTemplates: async () => {
        const {
            data: { data },
        } = await fetch({
            ...integrationUrls.getConnectionTypeTemplates,
        });
        console.log('data', data);
        set({
            isLoadedConnectionTypeTemplates: true,
            connectionTypeTemplates: data,
            isLoadingConnectionTypeTemplates: false,
        });
    },
    getConnectionTypeDetail: async (connectionTypeId: string) => {
        const [detailResponse, connectionsResponse] = await Promise.all([
            fetch({
                ...integrationUrls.getConnectionTypeDetail,
                params: {
                    connectionTypeId,
                },
            }),
            fetch({
                ...integrationUrls.getConnectionsUnderConnectionType,
                params: {
                    connectionTypeId,
                },
            }),
        ]);
        set({
            connectionTypeDetails: {
                ...get().connectionTypeDetails,
                [connectionTypeId]: {
                    connections: connectionsResponse.data.data,
                    details: detailResponse.data,
                },
            },
        });
    },
    getConnectionDetail: async (connectionId: string) => {
        const [detailResponse, routerResponse] = await Promise.all([
            fetch({
                ...integrationUrls.getConnectionDetail,
                params: {
                    connectionId,
                },
            }),
            fetch({
                ...integrationUrls.getConnectionRouters,
                params: {
                    connectionId,
                },
            }),
        ]);
        set({
            connectionDetails: {
                ...get().connectionDetails,
                [connectionId]: { ...detailResponse.data, routes: routerResponse.data?.data },
            },
        });
    },
    uninstallConnectionType: async (connectionTypeId: string) => {
        set({ isLoadingConnectionTypes: true });
        await fetch({
            ...integrationUrls.uninstallConnectionType,
            params: {
                connectionTypeId,
            },
            successMessage: `Successfully deleted the connection type`,
        });
        set({
            connectionTypes: [...get().connectionTypes].filter(
                (connectionType) => connectionType.id !== connectionTypeId
            ),
            isLoadingConnectionTypes: false,
        });
    },
    installConnectionType: async (name: string, file: any) => {
        set({ isLoadingConnectionTypes: true });
        const formData = new FormData();
        formData.append('name', name);
        formData.append('payload', file);
        await fetch({
            ...integrationUrls.installConnectionType,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            data: formData,
            successMessage: `Successfully install new connection type ${name}`,
        });
        await get().getConnectionTypes();
    },
    installConnectionTypeFromTemplate: async (templateId: string) => {
        set({ isLoadingConnectionTypes: true });
        await fetch({
            ...integrationUrls.installConnectionTypeFromTemplate,
            params: {
                templateId,
            },
            successMessage: `Successfully install connection type from the template`,
        });
        await get().getConnectionTypes();
    },
    deleteConnections: async (connectionTypeId: string, connectionIds: string[]) => {
        try {
            await Promise.all(
                connectionIds.map((connectionId) =>
                    fetch({
                        ...integrationUrls.deleteConnection,
                        params: {
                            connectionTypeId,
                            connectionId,
                        },
                    })
                )
            );
            set({
                connectionTypeDetails: {
                    ...get().connectionTypeDetails,
                    [connectionTypeId]: {
                        ...get().connectionTypeDetails[connectionTypeId],
                        connections: [...get().connectionTypeDetails[connectionTypeId].connections].filter(
                            (connection) => !connectionIds.includes(connection.id)
                        ),
                    },
                },
            });
            return true;
        } catch (err) {
            return false;
        }
    },
    addConnection: async (connectionTypeId: string, connectionRequest: CreateConnectionRequest) => {
        try {
            const { data } = await fetch({
                ...integrationUrls.addConnection,
                params: {
                    connectionTypeId,
                },
                data: connectionRequest,
            });
            set({
                connectionTypeDetails: {
                    ...get().connectionTypeDetails,
                    [connectionTypeId]: {
                        ...get().connectionTypeDetails[connectionTypeId],
                        connections: [data, ...get().connectionTypeDetails[connectionTypeId].connections],
                    },
                },
            });
            return true;
        } catch (err) {
            return false;
        }
    },
    updateConnection: async (
        connectionTypeId: string,
        connectionId: string,
        connectionRequest: CreateConnectionRequest
    ) => {
        try {
            const { data } = await fetch({
                ...integrationUrls.updateConnection,
                params: {
                    connectionTypeId,
                    connectionId,
                },
                data: connectionRequest,
            });
            set({
                connectionTypeDetails: {
                    ...get().connectionTypeDetails,
                    [connectionTypeId]: {
                        ...get().connectionTypeDetails[connectionTypeId],
                        connections: [...get().connectionTypeDetails[connectionTypeId].connections].map((connection) =>
                            connection.id !== connectionId ? connection : data
                        ),
                    },
                },
            });
            return true;
        } catch (err) {
            return false;
        }
    },
    importRouter: async (connectionId: string, name: string, file: any) => {
        const formData = new FormData();
        formData.append('name', name);
        formData.append('payload', file);
        try {
            const { data } = await fetch({
                ...integrationUrls.importRouter,
                params: { connectionId },
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                data: formData,
                skipToast: true,
            });
            await get().getConnectionDetail(connectionId);
            return data;
        } catch (e) {
            notifyError('An error occurred while importing the route configuration');
        }
    },
    updateRoute: async (routerId: string, template: any) => {
        try {
            await fetch({
                ...integrationUrls.updateRouter,
                params: { routerId },
                data: {
                    template,
                },
                successMessage: 'Updated Route configuration',
            });
        } catch (e) {
            notifyError('An error occurred while updating the route configuration');
        }
    },
    deleteRoute: async (routerId: string) => {
        try {
            await fetch({
                ...integrationUrls.deleteRouter,
                params: { routerId },
                successMessage: 'Deleted Route configuration',
            });
        } catch (e) {
            notifyError('An error occurred while deleting the route configuration');
        }
    },
}));

export default useIntegrationStore;
