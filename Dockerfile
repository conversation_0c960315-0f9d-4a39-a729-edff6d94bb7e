FROM nginx:1.21.0-alpine

WORKDIR /usr/share/nginx/html

COPY importmap.json ./

COPY packages/app-root/dist/ ./

COPY packages/app-root/dist/assets ./assets

COPY packages/api/dist/ ./

COPY packages/dashboard/dist/ ./

COPY packages/sidebar/dist/ ./

COPY packages/styleguide/dist/ ./

COPY packages/header/dist/ ./

COPY packages/notification/dist/ ./

COPY packages/common/dist/ ./

COPY packages/caching-store/dist ./

COPY packages/schema/dist/ ./

COPY packages/classification/dist/ ./

COPY packages/process/dist/ ./

COPY packages/business-rule/dist/ ./

COPY packages/lifecycle/dist/ ./

COPY packages/role/dist/ ./

COPY packages/company/dist/ ./

COPY packages/permission-management-role/dist/ ./

COPY packages/user/dist/ ./

COPY packages/user-group-management/dist/ ./

COPY packages/app-tool/dist/ ./

COPY packages/integration/dist/ ./

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
