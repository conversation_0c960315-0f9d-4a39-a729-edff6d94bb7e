#!/bin/sh
# edit microfrontends you want to fetch from remote here
remoteFrontends=("header dashboard schema classification business-rule lifecycle role integration user company sidebar styleguide caching-store api notification common process permission-management-role user-group-management app-tool global-setting recycle-bin")
REMOTE_URL="https://ui-stage.glideyoke.com/admin/";
ORG_NAME="tripudiotech"
cd ../packages/app-root/public
rm -r remote
mkdir remote
cd remote
for i in $remoteFrontends
do
	remoteUrl="${REMOTE_URL}${ORG_NAME}-${i}.js"
	outputFile="${ORG_NAME}-${i}.js"
	curl $remoteUrl -o $outputFile
done
