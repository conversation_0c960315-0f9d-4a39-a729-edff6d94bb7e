# Default values for ui-admin.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ************.dkr.ecr.us-east-2.amazonaws.com/glide-common-ecr
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""
  pullSecrets: ""

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

containerPort: 80

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  ingressClassName: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  paths:
    - /
  hosts:
    - example.demo.com
  tls: []
  #  - secretName: example-tls
  #    hosts:
  #      - example.demo.com

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 85
  targetMemoryUtilizationPercentage: 88

nodeSelector: {}

tolerations: []

affinity: {}
  # podAntiAffinity:
  #   enabled: false
  # nodeAffinity:
  #   enabled: false
  #   key: "eks.amazonaws.com/nodegroup"
  #   values:
  #     - on-demand

livenessProbe:
  enabled: false
  path: "/"
  initialDelaySeconds: 300
  periodSeconds: 10  

readinessProbe:
  enabled: false
  path: "/"
  initialDelaySeconds: 5
  periodSeconds: 5

variables: {}

secrets: {}

serviceMonitor:
  enabled: false
  path: /q/metrics
  interval: 30s