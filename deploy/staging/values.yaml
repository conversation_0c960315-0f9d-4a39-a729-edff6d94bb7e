containerPort: 80

service:
  type: NodePort

resources:
  requests:
    cpu: 50m
    memory: 256Mi

affinity:
  podAntiAffinity:
    enabled: true
  nodeAffinity:
    enabled: true
    key: "Environment"
    values:
      - stag

ingress:
  enabled: true
  annotations:
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig":
      { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-2:764067299928:certificate/a5a60cc2-aeb0-4a0f-9ff5-7bd93175f4a6
    alb.ingress.kubernetes.io/group.name: tripudiotech-dev
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/tags: Environment=Tripudiotech-Dev
    kubernetes.io/ingress.class: alb
  paths:
    - /*
  hosts:
    - admin-stage.glideyoke.com
    - admin-master.glideyoke.com
    - admin-ui-qa-stage.glideyoke.com
    - admin-ui-po-stage.glideyoke.com
    - admin-cad-integrator.glideyoke.com
    - admin-inventor-autodesk.glideyoke.com
    - admin-oslc-integration.glideyoke.com
    - admin-deploy-stage.glideyoke.com
    - admin-ui-demo.glideyoke.com
    - admin-silicon-expert.glideyoke.com
