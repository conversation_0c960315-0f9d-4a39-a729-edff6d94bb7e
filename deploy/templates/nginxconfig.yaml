apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "ui-admin.fullname" . }}-nginx-config
  labels:
    {{- include "ui-admin.labels" . | nindent 4 }}
data:
  nginx.conf: |-
    user  nginx;
    worker_processes  auto;

    error_log  /var/log/nginx/error.log notice;
    pid        /var/run/nginx.pid;

    events {
        worker_connections  1024;
    }

    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for"';
        access_log  /var/log/nginx/access.log  main;
        sendfile        on;
        keepalive_timeout  65;
        server {
          listen 80;
          location / {
            root /usr/share/nginx/html/;
            include /etc/nginx/mime.types;
            try_files $uri $uri/ /index.html;
            error_page 404 =200 /index.html;
            error_page 403 =200 /index.html;
          }
        }
        #gzip  on;
        #tcp_nopush     on;
        include /etc/nginx/conf.d/*.conf;
        large_client_header_buffers 4 32k;
    }