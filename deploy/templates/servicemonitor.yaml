{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "ui-admin.fullname" . }}
  labels:
    {{- include "ui-admin.labels" . | nindent 4 }}
spec:
  jobLabel: {{ include "ui-admin.fullname" . }}
  endpoints:
    - port: http
      path: {{ .Values.serviceMonitor.path }}
      interval: {{ .Values.serviceMonitor.interval }}
  selector:
    matchLabels: 
      {{- include "ui-admin.selectorLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace | quote }}      
{{- end }}