apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ui-admin.fullname" . }}
  labels:
    {{- include "ui-admin.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "ui-admin.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ui-admin.selectorLabels" . | nindent 8 }}
    spec:
      {{- if .Values.image.pullSecrets }}
      imagePullSecrets: {{- .Values.image.pullSecrets }}
      {{- end }}
      serviceAccountName: {{ include "ui-admin.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.containerPort }}
              protocol: TCP
          volumeMounts:
            - name: nginx-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf              
          env:
            {{- range $key, $value := .Values.variables }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- if .Values.secrets }}    
          envFrom:
            - secretRef:
                name: {{ include "ui-admin.fullname" . }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ default "/" .Values.livenessProbe.path }}
              port: http
            initialDelaySeconds: {{ default 5 .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ default 5 .Values.livenessProbe.periodSeconds }}              
          {{- end }}   
          {{- if .Values.readinessProbe.enabled }}          
          readinessProbe:
            httpGet:
              path: {{ default "/" .Values.readinessProbe.path }}
              port: http
            initialDelaySeconds: {{ default 5 .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ default 5 .Values.readinessProbe.periodSeconds }}
          {{- end }}           
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.affinity }}
      {{- if or .Values.affinity.podAntiAffinity.enabled .Values.affinity.nodeAffinity.enabled }}
      affinity:
        {{- if .Values.affinity.podAntiAffinity.enabled}}
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app.kubernetes.io/name
                      operator: In
                      values:
                        - {{ include "ui-admin.name" . }}
                topologyKey: kubernetes.io/hostname
              weight: 100
        {{- end }}
        {{- if .Values.affinity.nodeAffinity.enabled}}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: {{ .Values.affinity.nodeAffinity.key }}
                  operator: In
                  values:
                    {{- range .Values.affinity.nodeAffinity.values }}
                    - {{ . }}
                    {{- end }}
        {{- end }}
      {{- end }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: nginx-config
          configMap:
            name: {{ include "ui-admin.fullname" . }}-nginx-config