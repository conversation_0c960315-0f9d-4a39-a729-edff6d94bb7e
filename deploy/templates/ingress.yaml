{{- if .Values.ingress.enabled -}}
{{- $fullName := include "ui-admin.fullname" . -}}
{{- $ingressPaths := .Values.ingress.paths -}}
apiVersion: {{ include "ui-admin.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "ui-admin.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.ingressClassName (eq (include "ui-admin.ingress.apiVersion" $) "networking.k8s.io/v1") }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ . | quote }}
      http:
        paths:
          - backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
            pathType: ImplementationSpecific 
            path: /* 
	        {{- range $ingressPaths }}
          - path: {{ . }}
            {{- if eq (include "ui-admin.ingress.apiVersion" $) "networking.k8s.io/v1" }}
            pathType: "ImplementationSpecific"
            {{- end }}
            backend:
              {{- if eq (include "ui-admin.ingress.apiVersion" $) "networking.k8s.io/v1" }}
              service:
                name: {{ $fullName }}
                port:
                  name: http
              {{ else }}
              serviceName: {{ $fullName }}
              servicePort: http
              {{- end }}             
	        {{- end }}
    {{- end }}
{{- end }}