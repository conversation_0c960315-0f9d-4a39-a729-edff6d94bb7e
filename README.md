# UI Admin

Admin UI for managing Schema, Classificaitons, Organization, etc

## Overview architecture

![Admin UI Architecture](https://gitlab.com/tripudiotech/ui-admin/-/raw/main/documentation/UI%20Architecture.png)

## Setup local development environment

- To install private packages from gitlab registry, you must create an access token with `read_api` and `read-registry` permissions, and configure the following

```sh
npm config set -- //gitlab.com/api/v4/packages/npm/:_authToken=<Your access token>
npm config set @tripudiotech:registry https://gitlab.com/api/v4/packages/npm/
npm config set -- //gitlab.com/api/v4/projects/43205761/packages/npm/:_authToken=<Your access token>
npm config set -- //gitlab.com/api/v4/projects/43252241/packages/npm/:_authToken=<Your access token>
```

- Important: Do not install the private packages with @org name, as SystemJS tries to load this as in browser modules. Instead, install it with a specific name. Eg: `"ui-common": "npm:ui-common@^1.0.2"`

- Run the following cmd: `npm start:parallel`

- To link with other packages on local enviroment, add the following to your styleguide package.json

```js
"ui-common": "file:../../../ui-common",
"ui-style": "file:../../../ui-style",

```

### Run specific micro-frontends

- If you want to run only specific microfrontends:

Run `lerna run --scope "@tripudiotech/{admin-api,admin-root-config,admin-styleguide,admin-caching-store,admin-header,admin-sidebar,admin-classification}" start`

These are mandatory packages and you cannot skip running them: `admin-api,admin-root-config,admin-styleguide,admin-caching-store,admin-header,admin-sidebar`.

If running all of these packages is extremely slow on your computer:

1. Run `./downloadMicrofrontends.sh` from the `scripts` folder (must be run inside this folder for correct context) to download the microfrontends from a deployed environment.
2. Remove isLocal from the `package.json` of app-root `start` script (don't commit this)
3. Create an `importmap.json` in `packages/app-root/public/importmap.json` with contents like this
	(remotely fetched MFE's have /remote prefix others you are running locally don't). You don't need to specify all the imports, only the ones you need.
```json
{
    "imports": {
        "@tripudiotech/admin-root-config": "/tripudiotech-root-config.js",
        "@tripudiotech/admin-header": "/remote/tripudiotech-header.js",
        "@tripudiotech/admin-sidebar": "/remote/tripudiotech-sidebar.js",
        "@tripudiotech/admin-styleguide": "/remote/tripudiotech-styleguide.js",
        "@tripudiotech/admin-api": "//localhost:9007/tripudiotech-api.js",
        "@tripudiotech/admin-classification": "//localhost:9005/tripudiotech-classification.js",
        "@tripudiotech/admin-caching-store": "/remote/tripudiotech-caching-store.js",
        "@tripudiotech/admin-dashboard": "/remote/tripudiotech-dashboard.js",
        "@tripudiotech/admin-notification": "/remote/tripudiotech-notification.js",
        "@tripudiotech/admin-common": "/remote/tripudiotech-common.js"
    }
}
```
4. Make sure you add this line to `/etc/hosts` : `127.0.0.1       ui-dev.localhost`
5. Go to http://ui-dev.localhost:9000 to view the UI

Notes and example:

You have to run `app-root`, `api` locally. Don't fetch them from remote.
So, for example, if you're debugging `entity-detail`:
1. You could run the `scripts/downloadMicrofrontends.sh`script to fetch `header,styleguide,caching-store,sidebar` (edit the ones you want to fetch inside the script before running it)
2. Your `importmap.json` in `packages/app-root/public/importmap.json` should look like below (you don't need to import the ones you don't need):
```json
{
    "imports": {
        "@tripudiotech/admin-root-config": "/tripudiotech-root-config.js",
        "@tripudiotech/admin-header": "/remote/tripudiotech-header.js",
        "@tripudiotech/admin-sidebar": "/remote/tripudiotech-sidebar.js",
        "@tripudiotech/admin-styleguide": "/remote/tripudiotech-styleguide.js",
        "@tripudiotech/admin-api": "//localhost:9007/tripudiotech-api.js",
        "@tripudiotech/admin-classification": "//localhost:9005/tripudiotech-classification.js",
        "@tripudiotech/admin-caching-store": "/remote/tripudiotech-caching-store.js",
        "@tripudiotech/admin-dashboard": "/remote/tripudiotech-dashboard.js",
        "@tripudiotech/admin-notification": "/remote/tripudiotech-notification.js",
        "@tripudiotech/admin-common": "/remote/tripudiotech-common.js"
    }
}

```
3. Run this from the terminal `lerna run --scope "@tripudiotech/{admin-root-config,admin-classification,admin-api}" start`

If you are make type changes to `styleguide`, `caching-store`, `api`, the build process generates types for those under types/. You must commit those changes. Don't modify them by hand as they are auto-generated.


## Libraries & dependencies

- [MUI v5 - For styling the application](https://mui.com/)
- [ReactFlow - A highly customizable React component for building node-based editors and interactive diagrams](https://reactflow.dev/)
- [AgGrid - A data grid library for rendering table. AgGrid supports many feature like row grouping, filtering, sorting, exporting data, etc.](https://ag-grid.com/)
- [BpmnJS - For drawing bpmn 2.0 diagram, support for process](http://bpmn.io/)
- [dndkit - Toolkit for builing drag & drop interfaces](https://dndkit.com/)
- [Zustand](https://zustand-demo.pmnd.rs/)
