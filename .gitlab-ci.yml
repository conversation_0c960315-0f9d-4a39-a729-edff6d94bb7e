stages:
  - install
  - package
  - publish
  - invalidate

default:
  tags:
    - glide-runner

image: public.ecr.aws/m6s1b6b2/ci-image:latest

include:
  - project: tripudiotech/others/ci-templates
    file:
      - ui-admin/main.yaml
      - ui-admin/package-dir.yaml
      - ui-admin/aws-stag-packages.yaml
      - ui-admin/gcp-stag-packages.yaml
      - ui-admin/gcp-uat-packages.yaml
      - ui-admin/nxp-poc-packages.yaml